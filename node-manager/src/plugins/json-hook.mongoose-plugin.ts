import mongoose from 'mongoose';

/**
 * jsonHook contains logic to run become converting model to json
 * @param {string[]} fieldsToOmit  - array of strings to omit from the json object
 * @param {function} extraManipulation - an optional function to define your own behavior in the json hook
 */

export default (
  fieldsToOmit: Array<string>,
  extraManipulation = (obj: Record<string, unknown>) => obj,
) => (schema: mongoose.Schema) => {
  schema.methods.toJSON = function () {
    let obj = this.toObject({ virtuals: true });
    fieldsToOmit.forEach((field) => {
      delete obj[field];
    });

    obj = extraManipulation(obj);

    return obj;
  };
};
