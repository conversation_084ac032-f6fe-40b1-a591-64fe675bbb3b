// @generated by protobuf-ts 2.9.4 with parameter server_grpc1,client_none,optimize_code_size
// @generated from protobuf file "OpenApiMessages.proto" (syntax proto2)
// tslint:disable
import { MessageType } from "@protobuf-ts/runtime";
import { ProtoOAPositionUnrealizedPnL } from "./interfaces";
import { ProtoOADealOffset } from "./interfaces";
import { ProtoOADynamicLeverage } from "./interfaces";
import { ProtoOAMarginCall } from "./interfaces";
import { ProtoOASymbolCategory } from "./interfaces";
import { ProtoOADepthQuote } from "./interfaces";
import { ProtoOACtidProfile } from "./interfaces";
import { ProtoOATickData } from "./interfaces";
import { ProtoOAQuoteType } from "./interfaces";
import { ProtoOATrendbarPeriod } from "./interfaces";
import { ProtoOATrendbar } from "./interfaces";
import { ProtoOACtidTraderAccount } from "./interfaces";
import { ProtoOAClientPermissionScope } from "./interfaces";
import { ProtoOAExpectedMargin } from "./interfaces";
import { ProtoOATrader } from "./interfaces";
import { ProtoOAAssetClass } from "./interfaces";
import { ProtoOASymbol } from "./interfaces";
import { ProtoOAArchivedSymbol } from "./interfaces";
import { ProtoOALightSymbol } from "./interfaces";
import { ProtoOAAsset } from "./interfaces";
import { ProtoOADepositWithdraw } from "./interfaces";
import { ProtoOABonusDepositWithdraw } from "./interfaces";
import { ProtoOADeal } from "./interfaces";
import { ProtoOAOrder } from "./interfaces";
import { ProtoOAPosition } from "./interfaces";
import { ProtoOAExecutionType } from "./interfaces";
import { ProtoOAOrderTriggerMethod } from "./interfaces";
import { ProtoOATimeInForce } from "./interfaces";
import { ProtoOATradeSide } from "./interfaces";
import { ProtoOAOrderType } from "./interfaces";
import { ProtoOAPayloadType } from "./interfaces";
/**
 * * Request for the authorizing an application to work with the cTrader platform Proxies.
 *
 * @generated from protobuf message ProtoOAApplicationAuthReq
 */
export interface ProtoOAApplicationAuthReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: string clientId = 2;
     */
    clientId: string; // The unique Client ID provided during the registration.
    /**
     * @generated from protobuf field: string clientSecret = 3;
     */
    clientSecret: string; // The unique Client Secret provided during the registration.
}
/**
 * * Response to the ProtoOAApplicationAuthReq request.
 *
 * @generated from protobuf message ProtoOAApplicationAuthRes
 */
export interface ProtoOAApplicationAuthRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
}
/**
 * * Request for authorizing of the trading account session. Requires established authorized connection with the client application using ProtoOAApplicationAuthReq.
 *
 * @generated from protobuf message ProtoOAAccountAuthReq
 */
export interface ProtoOAAccountAuthReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // The unique identifier of the trader's account in cTrader platform.
    /**
     * @generated from protobuf field: string accessToken = 3;
     */
    accessToken: string; // The Access Token issued for providing access to the Trader's Account.
}
/**
 * * Response to the ProtoOAApplicationAuthRes request.
 *
 * @generated from protobuf message ProtoOAAccountAuthRes
 */
export interface ProtoOAAccountAuthRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // The unique identifier of the trader's account in cTrader platform.
}
/**
 * * Generic response when an ERROR occurred.
 *
 * @generated from protobuf message ProtoOAErrorRes
 */
export interface ProtoOAErrorRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: optional int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId?: bigint; // The unique identifier of the trader's account in cTrader platform.
    /**
     * @generated from protobuf field: string errorCode = 3;
     */
    errorCode: string; // The name of the ProtoErrorCode or the other custom ErrorCodes (e.g. ProtoCHErrorCode).
    /**
     * @generated from protobuf field: optional string description = 4;
     */
    description?: string; // The error description.
    /**
     * @generated from protobuf field: optional int64 maintenanceEndTimestamp = 5;
     */
    maintenanceEndTimestamp?: bigint; // The Unix time in seconds when the current maintenance session will be ended.
    /**
     * @generated from protobuf field: optional uint64 retryAfter = 6;
     */
    retryAfter?: bigint; // When you hit rate limit with errorCode=BLOCKED_PAYLOAD_TYPE, this field will contain amount of seconds until related payload type will be unlocked.
}
/**
 * * Event that is sent when the connection with the client application is cancelled by the server. All the sessions for the traders' accounts will be terminated.
 *
 * @generated from protobuf message ProtoOAClientDisconnectEvent
 */
export interface ProtoOAClientDisconnectEvent {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: optional string reason = 2;
     */
    reason?: string; // The disconnection reason explained. For example: The application access was blocked by cTrader Administrator.
}
/**
 * * Event that is sent when a session to a specific trader's account is terminated by the server but the existing connections with the other trader's accounts are maintained. Reasons to trigger: account was deleted, cTID was deleted, token was refreshed, token was revoked.
 *
 * @generated from protobuf message ProtoOAAccountsTokenInvalidatedEvent
 */
export interface ProtoOAAccountsTokenInvalidatedEvent {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: repeated int64 ctidTraderAccountIds = 2;
     */
    ctidTraderAccountIds: bigint[]; // The unique identifier of the trader's account in cTrader platform.
    /**
     * @generated from protobuf field: optional string reason = 3;
     */
    reason?: string; // The disconnection reason explained. For example: Access Token is expired or recalled.
}
/**
 * * Request for getting the proxy version. Can be used to check the current version of the Open API scheme.
 *
 * @generated from protobuf message ProtoOAVersionReq
 */
export interface ProtoOAVersionReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
}
/**
 * * Response to the ProtoOAVersionReq request.
 *
 * @generated from protobuf message ProtoOAVersionRes
 */
export interface ProtoOAVersionRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: string version = 2;
     */
    version: string; // The current version of the server application.
}
/**
 * * Request for sending a new trading order. Allowed only if the accessToken has the "trade" permissions for the trading account.
 *
 * @generated from protobuf message ProtoOANewOrderReq
 */
export interface ProtoOANewOrderReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // The unique identifier of the trader's account in cTrader platform.
    /**
     * @generated from protobuf field: int64 symbolId = 3;
     */
    symbolId: bigint; // The unique identifier of a symbol in cTrader platform.
    /**
     * @generated from protobuf field: ProtoOAOrderType orderType = 4;
     */
    orderType: ProtoOAOrderType; // The type of an order - MARKET, LIMIT, STOP, MARKET_RANGE, STOP_LIMIT.
    /**
     * @generated from protobuf field: ProtoOATradeSide tradeSide = 5;
     */
    tradeSide: ProtoOATradeSide; // The trade direction - BUY or SELL.
    /**
     * @generated from protobuf field: int64 volume = 6;
     */
    volume: bigint; // The volume represented in 0.01 of a unit (e.g. 1000 in protocol means 10.00 units).
    /**
     * @generated from protobuf field: optional double limitPrice = 7;
     */
    limitPrice?: number; // The limit price, can be specified for the LIMIT order only.
    /**
     * @generated from protobuf field: optional double stopPrice = 8;
     */
    stopPrice?: number; // Stop Price, can be specified for the STOP and the STOP_LIMIT orders only.
    /**
     * @generated from protobuf field: optional ProtoOATimeInForce timeInForce = 9;
     */
    timeInForce?: ProtoOATimeInForce; // The specific order execution or expiration instruction - GOOD_TILL_DATE, GOOD_TILL_CANCEL, IMMEDIATE_OR_CANCEL, FILL_OR_KILL, MARKET_ON_OPEN.
    /**
     * @generated from protobuf field: optional int64 expirationTimestamp = 10;
     */
    expirationTimestamp?: bigint; // The Unix time in milliseconds of Order expiration. Should be set for the Good Till Date orders.
    /**
     * @generated from protobuf field: optional double stopLoss = 11;
     */
    stopLoss?: number; // The absolute Stop Loss price (1.23456 for example). Not supported for the MARKER orders.
    /**
     * @generated from protobuf field: optional double takeProfit = 12;
     */
    takeProfit?: number; // The absolute Take Profit price (1.23456 for example). Unsupported for the MARKER orders.
    /**
     * @generated from protobuf field: optional string comment = 13;
     */
    comment?: string; // User-specified comment. MaxLength = 512.
    /**
     * @generated from protobuf field: optional double baseSlippagePrice = 14;
     */
    baseSlippagePrice?: number; // Base price to calculate relative slippage price for MARKET_RANGE order.
    /**
     * @generated from protobuf field: optional int32 slippageInPoints = 15;
     */
    slippageInPoints?: number; // Slippage distance for MARKET_RANGE and STOP_LIMIT order.
    /**
     * @generated from protobuf field: optional string label = 16;
     */
    label?: string; // User-specified label. MaxLength = 100.
    /**
     * @generated from protobuf field: optional int64 positionId = 17;
     */
    positionId?: bigint; // Reference to the existing position if the Order is intended to modify it.
    /**
     * @generated from protobuf field: optional string clientOrderId = 18;
     */
    clientOrderId?: string; // Optional user-specific clientOrderId (similar to FIX ClOrderID). MaxLength = 50.
    /**
     * @generated from protobuf field: optional int64 relativeStopLoss = 19;
     */
    relativeStopLoss?: bigint; // Relative Stop Loss that can be specified instead of the absolute as one. Specified in 1/100000 of unit of a price. (e.g. 123000 in protocol means 1.23, ******** means 534.23782) For BUY stopLoss = entryPrice - relativeStopLoss, for SELL stopLoss = entryPrice + relativeStopLoss.
    /**
     * @generated from protobuf field: optional int64 relativeTakeProfit = 20;
     */
    relativeTakeProfit?: bigint; // Relative Take Profit that can be specified instead of the absolute one. Specified in 1/100000 of unit of a price. (e.g. 123000 in protocol means 1.23, ******** means 534.23782) For BUY takeProfit = entryPrice + relativeTakeProfit, for SELL takeProfit = entryPrice - relativeTakeProfit.
    /**
     * @generated from protobuf field: optional bool guaranteedStopLoss = 21;
     */
    guaranteedStopLoss?: boolean; // If TRUE then stopLoss is guaranteed. Required to be set to TRUE for the Limited Risk accounts (ProtoOATrader.isLimitedRisk=true).
    /**
     * @generated from protobuf field: optional bool trailingStopLoss = 22;
     */
    trailingStopLoss?: boolean; // If TRUE then the Stop Loss is Trailing.
    /**
     * @generated from protobuf field: optional ProtoOAOrderTriggerMethod stopTriggerMethod = 23;
     */
    stopTriggerMethod?: ProtoOAOrderTriggerMethod; // Trigger method for the STOP or the STOP_LIMIT pending order.
}
/**
 * * Event that is sent following the successful order acceptance or execution by the server. Acts as response to the ProtoOANewOrderReq, ProtoOACancelOrderReq, ProtoOAAmendOrderReq, ProtoOAAmendPositionSLTPReq, ProtoOAClosePositionReq requests. Also, the event is sent when a Deposit/Withdrawal took place.
 *
 * @generated from protobuf message ProtoOAExecutionEvent
 */
export interface ProtoOAExecutionEvent {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: ProtoOAExecutionType executionType = 3;
     */
    executionType: ProtoOAExecutionType; // Type of the order operation. For example: ACCEPTED, FILLED, etc.
    /**
     * @generated from protobuf field: optional ProtoOAPosition position = 4;
     */
    position?: ProtoOAPosition; // Reference to the position linked with the execution
    /**
     * @generated from protobuf field: optional ProtoOAOrder order = 5;
     */
    order?: ProtoOAOrder; // Reference to the initial order.
    /**
     * @generated from protobuf field: optional ProtoOADeal deal = 6;
     */
    deal?: ProtoOADeal; // Reference to the deal (execution).
    /**
     * @generated from protobuf field: optional ProtoOABonusDepositWithdraw bonusDepositWithdraw = 7;
     */
    bonusDepositWithdraw?: ProtoOABonusDepositWithdraw; // Reference to the Bonus Deposit or Withdrawal operation.
    /**
     * @generated from protobuf field: optional ProtoOADepositWithdraw depositWithdraw = 8;
     */
    depositWithdraw?: ProtoOADepositWithdraw; // Reference to the Deposit or Withdrawal operation.
    /**
     * @generated from protobuf field: optional string errorCode = 9;
     */
    errorCode?: string; // The name of the ProtoErrorCode or the other custom ErrorCodes (e.g. ProtoCHErrorCode).
    /**
     * @generated from protobuf field: optional bool isServerEvent = 10;
     */
    isServerEvent?: boolean; // If TRUE then the event generated by the server logic instead of the trader's request. (e.g. stop-out).
}
/**
 * * Request for cancelling existing pending order. Allowed only if the accessToken has "trade" permissions for the trading account.
 *
 * @generated from protobuf message ProtoOACancelOrderReq
 */
export interface ProtoOACancelOrderReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 orderId = 3;
     */
    orderId: bigint; // The unique ID of the order.
}
/**
 * * Request for amending the existing pending order. Allowed only if the Access Token has "trade" permissions for the trading account.
 *
 * @generated from protobuf message ProtoOAAmendOrderReq
 */
export interface ProtoOAAmendOrderReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 orderId = 3;
     */
    orderId: bigint; // The unique ID of the order.
    /**
     * @generated from protobuf field: optional int64 volume = 4;
     */
    volume?: bigint; // Volume, represented in 0.01 of a unit (e.g. 1000 in protocol means 10.00 units).
    /**
     * @generated from protobuf field: optional double limitPrice = 5;
     */
    limitPrice?: number; // The Limit Price, can be specified for the LIMIT order only.
    /**
     * @generated from protobuf field: optional double stopPrice = 6;
     */
    stopPrice?: number; // The Stop Price, can be specified for the STOP and the STOP_LIMIT orders.
    /**
     * @generated from protobuf field: optional int64 expirationTimestamp = 7;
     */
    expirationTimestamp?: bigint; // The Unix timestamp in milliseconds of Order expiration. Should be set for the Good Till Date orders.
    /**
     * @generated from protobuf field: optional double stopLoss = 8;
     */
    stopLoss?: number; // The absolute Stop Loss price (e.g. 1.23456). Not supported for the MARKER orders.
    /**
     * @generated from protobuf field: optional double takeProfit = 9;
     */
    takeProfit?: number; // The absolute Take Profit price (e.g. 1.23456). Not supported for the MARKER orders.
    /**
     * @generated from protobuf field: optional int32 slippageInPoints = 10;
     */
    slippageInPoints?: number; // Slippage distance for the MARKET_RANGE and the STOP_LIMIT orders.
    /**
     * @generated from protobuf field: optional int64 relativeStopLoss = 11;
     */
    relativeStopLoss?: bigint; // The relative Stop Loss can be specified instead of the absolute one. Specified in 1/100000 of a unit of price. (e.g. 123000 in protocol means 1.23, ******** means 534.23782) For BUY stopLoss = entryPrice - relativeStopLoss, for SELL stopLoss = entryPrice + relativeStopLoss.
    /**
     * @generated from protobuf field: optional int64 relativeTakeProfit = 12;
     */
    relativeTakeProfit?: bigint; // The relative Take Profit can be specified instead of the absolute one. Specified in 1/100000 of a unit of price. (e.g. 123000 in protocol means 1.23, ******** means 534.23782) For BUY takeProfit = entryPrice + relativeTakeProfit, for SELL takeProfit = entryPrice - relativeTakeProfit.
    /**
     * @generated from protobuf field: optional bool guaranteedStopLoss = 13;
     */
    guaranteedStopLoss?: boolean; // If TRUE then the Stop Loss is guaranteed. Available for the French Risk or the Guaranteed Stop Loss Accounts.
    /**
     * @generated from protobuf field: optional bool trailingStopLoss = 14;
     */
    trailingStopLoss?: boolean; // If TRUE then the Trailing Stop Loss is applied.
    /**
     * @generated from protobuf field: optional ProtoOAOrderTriggerMethod stopTriggerMethod = 15;
     */
    stopTriggerMethod?: ProtoOAOrderTriggerMethod; // Trigger method for the STOP or the STOP_LIMIT pending order.
}
/**
 * * Request for amending StopLoss and TakeProfit of existing position. Allowed only if the accessToken has "trade" permissions for the trading account.
 *
 * @generated from protobuf message ProtoOAAmendPositionSLTPReq
 */
export interface ProtoOAAmendPositionSLTPReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 positionId = 3;
     */
    positionId: bigint; // The unique ID of the position to amend.
    /**
     * @generated from protobuf field: optional double stopLoss = 4;
     */
    stopLoss?: number; // Absolute Stop Loss price (1.23456 for example).
    /**
     * @generated from protobuf field: optional double takeProfit = 5;
     */
    takeProfit?: number; // Absolute Take Profit price (1.26543 for example).
    /**
     * @generated from protobuf field: optional bool guaranteedStopLoss = 7;
     */
    guaranteedStopLoss?: boolean; // If TRUE then the Stop Loss is guaranteed. Available for the French Risk or the Guaranteed Stop Loss Accounts.
    /**
     * @generated from protobuf field: optional bool trailingStopLoss = 8;
     */
    trailingStopLoss?: boolean; // If TRUE then the Trailing Stop Loss is applied.
    /**
     * @generated from protobuf field: optional ProtoOAOrderTriggerMethod stopLossTriggerMethod = 9;
     */
    stopLossTriggerMethod?: ProtoOAOrderTriggerMethod; // The Stop trigger method for the Stop Loss/Take Profit order.
}
/**
 * * Request for closing or partially closing of an existing position. Allowed only if the accessToken has "trade" permissions for the trading account.
 *
 * @generated from protobuf message ProtoOAClosePositionReq
 */
export interface ProtoOAClosePositionReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 positionId = 3;
     */
    positionId: bigint; // The unique ID of the position to close.
    /**
     * @generated from protobuf field: int64 volume = 4;
     */
    volume: bigint; // Volume to close, represented in 0.01 of a unit (e.g. 1000 in protocol means 10.00 units).
}
/**
 * * Event that is sent when the level of the Trailing Stop Loss is changed due to the price level changes.
 *
 * @generated from protobuf message ProtoOATrailingSLChangedEvent
 */
export interface ProtoOATrailingSLChangedEvent {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 positionId = 3;
     */
    positionId: bigint; // The unique ID of the position.
    /**
     * @generated from protobuf field: int64 orderId = 4;
     */
    orderId: bigint; // The unique ID of the order.
    /**
     * @generated from protobuf field: double stopPrice = 5;
     */
    stopPrice: number; // New value of the Stop Loss price.
    /**
     * @generated from protobuf field: int64 utcLastUpdateTimestamp = 6;
     */
    utcLastUpdateTimestamp: bigint; // The Unix time in milliseconds when the Stop Loss was updated.
}
/**
 * * Request for the list of assets available for a trader's account.
 *
 * @generated from protobuf message ProtoOAAssetListReq
 */
export interface ProtoOAAssetListReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
}
/**
 * * Response to the ProtoOAAssetListReq request.
 *
 * @generated from protobuf message ProtoOAAssetListRes
 */
export interface ProtoOAAssetListRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOAAsset asset = 3;
     */
    asset: ProtoOAAsset[]; // The list of assets.
}
/**
 * * Request for a list of symbols available for a trading account. Symbol entries are returned with the limited set of fields.
 *
 * @generated from protobuf message ProtoOASymbolsListReq
 */
export interface ProtoOASymbolsListReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: optional bool includeArchivedSymbols = 3;
     */
    includeArchivedSymbols?: boolean; // Whether to include old archived symbols into response.
}
/**
 * * Response to the ProtoOASymbolsListReq request.
 *
 * @generated from protobuf message ProtoOASymbolsListRes
 */
export interface ProtoOASymbolsListRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOALightSymbol symbol = 3;
     */
    symbol: ProtoOALightSymbol[]; // The list of symbols.
    /**
     * @generated from protobuf field: repeated ProtoOAArchivedSymbol archivedSymbol = 4;
     */
    archivedSymbol: ProtoOAArchivedSymbol[]; // The list of archived symbols.
}
/**
 * * Request for getting a full symbol entity.
 *
 * @generated from protobuf message ProtoOASymbolByIdReq
 */
export interface ProtoOASymbolByIdReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated int64 symbolId = 3;
     */
    symbolId: bigint[]; // Unique identifier of the symbol in cTrader platform.
}
/**
 * * Response to the ProtoOASymbolByIdReq request.
 *
 * @generated from protobuf message ProtoOASymbolByIdRes
 */
export interface ProtoOASymbolByIdRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOASymbol symbol = 3;
     */
    symbol: ProtoOASymbol[]; // Symbol entity with the full set of fields.
    /**
     * @generated from protobuf field: repeated ProtoOAArchivedSymbol archivedSymbol = 4;
     */
    archivedSymbol: ProtoOAArchivedSymbol[]; // Archived symbols.
}
/**
 * * Request for getting a conversion chain between two assets that consists of several symbols. Use when no direct quote is available.
 *
 * @generated from protobuf message ProtoOASymbolsForConversionReq
 */
export interface ProtoOASymbolsForConversionReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 firstAssetId = 3;
     */
    firstAssetId: bigint; // The ID of the firs asset in the conversation chain. e.g.: for EUR/USD the firstAssetId is EUR ID and lastAssetId is USD ID.
    /**
     * @generated from protobuf field: int64 lastAssetId = 4;
     */
    lastAssetId: bigint; // The ID of the last asset in the conversation chain. e.g.: for EUR/USD the firstAssetId is EUR ID and lastAssetId is USD ID.
}
/**
 * * Response to the ProtoOASymbolsForConversionReq request.
 *
 * @generated from protobuf message ProtoOASymbolsForConversionRes
 */
export interface ProtoOASymbolsForConversionRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOALightSymbol symbol = 3;
     */
    symbol: ProtoOALightSymbol[]; // Conversion chain of the symbols (e.g. EUR/USD, USD/JPY, GBP/JPY -> EUR/GBP).
}
/**
 * * Event that is sent when the symbol is changed on the Server side.
 *
 * @generated from protobuf message ProtoOASymbolChangedEvent
 */
export interface ProtoOASymbolChangedEvent {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated int64 symbolId = 3;
     */
    symbolId: bigint[]; // Unique identifier of the Symbol in cTrader platform.
}
/**
 * * Request for a list of asset classes available for the trader's account.
 *
 * @generated from protobuf message ProtoOAAssetClassListReq
 */
export interface ProtoOAAssetClassListReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
}
/**
 * * Response to the ProtoOAAssetListReq request.
 *
 * @generated from protobuf message ProtoOAAssetClassListRes
 */
export interface ProtoOAAssetClassListRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOAAssetClass assetClass = 3;
     */
    assetClass: ProtoOAAssetClass[]; // List of the asset classes.
}
/**
 * * Request for getting data of Trader's Account.
 *
 * @generated from protobuf message ProtoOATraderReq
 */
export interface ProtoOATraderReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
}
/**
 * * Response to the ProtoOATraderReq request.
 *
 * @generated from protobuf message ProtoOATraderRes
 */
export interface ProtoOATraderRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: ProtoOATrader trader = 3;
     */
    trader?: ProtoOATrader; // The Trader account information.
}
/**
 * * Event that is sent when a Trader is updated on Server side.
 *
 * @generated from protobuf message ProtoOATraderUpdatedEvent
 */
export interface ProtoOATraderUpdatedEvent {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: ProtoOATrader trader = 3;
     */
    trader?: ProtoOATrader; // The Trader account information.
}
/**
 * * Request for getting Trader's current open positions and pending orders data.
 *
 * @generated from protobuf message ProtoOAReconcileReq
 */
export interface ProtoOAReconcileReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: optional bool returnProtectionOrders = 3;
     */
    returnProtectionOrders?: boolean; // If TRUE, then current protection orders are returned separately, otherwise you can use position.stopLoss and position.takeProfit fields.
}
/**
 * * The response to the ProtoOAReconcileReq request.
 *
 * @generated from protobuf message ProtoOAReconcileRes
 */
export interface ProtoOAReconcileRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOAPosition position = 3;
     */
    position: ProtoOAPosition[]; // The list of trader's account open positions.
    /**
     * @generated from protobuf field: repeated ProtoOAOrder order = 4;
     */
    order: ProtoOAOrder[]; // The list of trader's account pending orders.
}
/**
 * * Event that is sent when errors occur during the order requests.
 *
 * @generated from protobuf message ProtoOAOrderErrorEvent
 */
export interface ProtoOAOrderErrorEvent {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 5;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: string errorCode = 2;
     */
    errorCode: string; // The name of the ProtoErrorCode or the other custom ErrorCodes (e.g. ProtoCHErrorCode).
    /**
     * @generated from protobuf field: optional int64 orderId = 3;
     */
    orderId?: bigint; // The unique ID of the order.
    /**
     * @generated from protobuf field: optional int64 positionId = 6;
     */
    positionId?: bigint; // The unique ID of the position.
    /**
     * @generated from protobuf field: optional string description = 7;
     */
    description?: string; // The error description.
}
/**
 * * Request for getting Trader's deals historical data (execution details).
 *
 * @generated from protobuf message ProtoOADealListReq
 */
export interface ProtoOADealListReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: optional int64 fromTimestamp = 3;
     */
    fromTimestamp?: bigint; // The Unix time from which the search starts >=0 (1st Jan 1970).
    /**
     * @generated from protobuf field: optional int64 toTimestamp = 4;
     */
    toTimestamp?: bigint; // The Unix time where to stop searching <= ************* (19th Jan 2038).
    /**
     * @generated from protobuf field: optional int32 maxRows = 5;
     */
    maxRows?: number; // The maximum number of the deals to return.
}
/**
 * * The response to the ProtoOADealListRes request.
 *
 * @generated from protobuf message ProtoOADealListRes
 */
export interface ProtoOADealListRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOADeal deal = 3;
     */
    deal: ProtoOADeal[]; // The list of the deals.
    /**
     * @generated from protobuf field: bool hasMore = 4;
     */
    hasMore: boolean; // If TRUE then the number of records by filter is larger than chunkSize, the response contains the number of records that is equal to chunkSize.
}
/**
 * * Request for getting Trader's orders filtered by timestamp
 *
 * @generated from protobuf message ProtoOAOrderListReq
 */
export interface ProtoOAOrderListReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: optional int64 fromTimestamp = 3;
     */
    fromTimestamp?: bigint; // The Unix time from which the search starts >=0 (1st Jan 1970).
    /**
     * @generated from protobuf field: optional int64 toTimestamp = 4;
     */
    toTimestamp?: bigint; // The Unix time where to stop searching <= ************* (19th Jan 2038).
}
/**
 * * The response to the ProtoOAOrderListReq request.
 *
 * @generated from protobuf message ProtoOAOrderListRes
 */
export interface ProtoOAOrderListRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOAOrder order = 3;
     */
    order: ProtoOAOrder[]; // The list of the orders.
    /**
     * @generated from protobuf field: bool hasMore = 4;
     */
    hasMore: boolean; // If TRUE then the number of records by filter is larger than chunkSize, the response contains the number of records that is equal to chunkSize.
}
/**
 * * Request for getting the margin estimate according to leverage profiles. Can be used before sending a new order request. This doesn't consider ACCORDING_TO_GSL margin calculation type, as this calculation is trivial: usedMargin = (VWAP price of the position - GSL price) * volume * Quote2Deposit.
 *
 * @generated from protobuf message ProtoOAExpectedMarginReq
 */
export interface ProtoOAExpectedMarginReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 symbolId = 3;
     */
    symbolId: bigint; // Unique identifier of the Symbol in cTrader platform.
    /**
     * @generated from protobuf field: repeated int64 volume = 4;
     */
    volume: bigint[]; // Volume represented in 0.01 of a unit (e.g. 1000 in protocol means 10.00 units).
}
/**
 * * The response to the ProtoOAExpectedMarginReq request.
 *
 * @generated from protobuf message ProtoOAExpectedMarginRes
 */
export interface ProtoOAExpectedMarginRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOAExpectedMargin margin = 3;
     */
    margin: ProtoOAExpectedMargin[]; // The buy and sell margin estimate.
    /**
     * @generated from protobuf field: optional uint32 moneyDigits = 4;
     */
    moneyDigits?: number; // Specifies the exponent of the monetary values. E.g. moneyDigits = 8 must be interpret as business value multiplied by 10^8, then real balance would be *********** / 10^8 = 100.********. Affects margin.buyMargin, margin.sellMargin.
}
/**
 * * Event that is sent when the margin allocated to a specific position is changed.
 *
 * @generated from protobuf message ProtoOAMarginChangedEvent
 */
export interface ProtoOAMarginChangedEvent {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: uint64 positionId = 3;
     */
    positionId: bigint; // The unique ID of the position.
    /**
     * @generated from protobuf field: uint64 usedMargin = 4;
     */
    usedMargin: bigint; // The new value of the margin used.
    /**
     * @generated from protobuf field: optional uint32 moneyDigits = 5;
     */
    moneyDigits?: number; // Specifies the exponent of the monetary values. E.g. moneyDigits = 8 must be interpret as business value multiplied by 10^8, then real balance would be *********** / 10^8 = 100.********. Affects usedMargin.
}
/**
 * * Request for getting Trader's historical data of deposits and withdrawals.
 *
 * @generated from protobuf message ProtoOACashFlowHistoryListReq
 */
export interface ProtoOACashFlowHistoryListReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 fromTimestamp = 3;
     */
    fromTimestamp: bigint; // The Unix time from which the search starts >=0 (1st Jan 1970). Validation: toTimestamp - fromTimestamp <= ********* (1 week).
    /**
     * @generated from protobuf field: int64 toTimestamp = 4;
     */
    toTimestamp: bigint; // The Unix time where to stop searching <= ************* (19th Jan 2038).
}
/**
 * * Response to the ProtoOACashFlowHistoryListReq request.
 *
 * @generated from protobuf message ProtoOACashFlowHistoryListRes
 */
export interface ProtoOACashFlowHistoryListRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOADepositWithdraw depositWithdraw = 3;
     */
    depositWithdraw: ProtoOADepositWithdraw[]; // The list of deposit and withdrawal operations.
}
/**
 * * Request for getting the list of granted trader's account for the access token.
 *
 * @generated from protobuf message ProtoOAGetAccountListByAccessTokenReq
 */
export interface ProtoOAGetAccountListByAccessTokenReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: string accessToken = 2;
     */
    accessToken: string; // The Access Token issued for providing access to the Trader's Account.
}
/**
 * * Response to the ProtoOAGetAccountListByAccessTokenReq request.
 *
 * @generated from protobuf message ProtoOAGetAccountListByAccessTokenRes
 */
export interface ProtoOAGetAccountListByAccessTokenRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: string accessToken = 2;
     */
    accessToken: string; // The Access Token issued for providing access to the Trader's Account.
    /**
     * @generated from protobuf field: optional ProtoOAClientPermissionScope permissionScope = 3;
     */
    permissionScope?: ProtoOAClientPermissionScope; // SCOPE_VIEW, SCOPE_TRADE.
    /**
     * @generated from protobuf field: repeated ProtoOACtidTraderAccount ctidTraderAccount = 4;
     */
    ctidTraderAccount: ProtoOACtidTraderAccount[]; // The list of the accounts.
}
/**
 * * Request to refresh the access token using refresh token of granted trader's account.
 *
 * @generated from protobuf message ProtoOARefreshTokenReq
 */
export interface ProtoOARefreshTokenReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: string refreshToken = 2;
     */
    refreshToken: string; // The Refresh Token issued for updating Access Token.
}
/**
 * * Response to the ProtoOARefreshTokenReq request.
 *
 * @generated from protobuf message ProtoOARefreshTokenRes
 */
export interface ProtoOARefreshTokenRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: string accessToken = 2;
     */
    accessToken: string; // The Access Token issued for providing access to the Trader's Account.
    /**
     * @generated from protobuf field: string tokenType = 3;
     */
    tokenType: string; // bearer
    /**
     * @generated from protobuf field: int64 expiresIn = 4;
     */
    expiresIn: bigint; // Access Token expiration in seconds.
    /**
     * @generated from protobuf field: string refreshToken = 5;
     */
    refreshToken: string; // Your new Refresh Token.
}
/**
 * * Request for subscribing on spot events of the specified symbol. After successful subscription you'll receive technical ProtoOASpotEvent with latest price, after which you'll start receiving updates on prices via consequent ProtoOASpotEvents.
 *
 * @generated from protobuf message ProtoOASubscribeSpotsReq
 */
export interface ProtoOASubscribeSpotsReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated int64 symbolId = 3;
     */
    symbolId: bigint[]; // Unique identifier of the Symbol in cTrader platform.
    /**
     * @generated from protobuf field: optional bool subscribeToSpotTimestamp = 4;
     */
    subscribeToSpotTimestamp?: boolean; // If TRUE you will also receive the timestamp in ProtoOASpotEvent.
}
/**
 * * Response to the ProtoOASubscribeSpotsReq request. Reflects that your request to subscribe for symbol has been added to queue. You'll receive technical ProtoOASpotEvent with current price shortly after this response.
 *
 * @generated from protobuf message ProtoOASubscribeSpotsRes
 */
export interface ProtoOASubscribeSpotsRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
}
/**
 * * Request for unsubscribing from the spot events of the specified symbol. Request to stop receiving ProtoOASpotEvents related to particular symbols. Unsubscription is useful to minimize traffic, especially during high volatility events.
 *
 * @generated from protobuf message ProtoOAUnsubscribeSpotsReq
 */
export interface ProtoOAUnsubscribeSpotsReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated int64 symbolId = 3;
     */
    symbolId: bigint[]; // Unique identifier of the Symbol in cTrader platform.
}
/**
 * * Response to the ProtoOASubscribeSpotsRes request. Reflects that your request to unsubscribe will has been added to queue and will be completed shortly. You may still occasionally receive ProtoOASpotEvents until request processing is complete.
 *
 * @generated from protobuf message ProtoOAUnsubscribeSpotsRes
 */
export interface ProtoOAUnsubscribeSpotsRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
}
/**
 * * Event that is sent when a new spot event is generated on the server side. Requires subscription on the spot events, see ProtoOASubscribeSpotsReq. First event, received after subscription will contain latest spot prices even if market is closed.
 *
 * @generated from protobuf message ProtoOASpotEvent
 */
export interface ProtoOASpotEvent {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 symbolId = 3;
     */
    symbolId: bigint; // Unique identifier of the Symbol in cTrader platform.
    /**
     * @generated from protobuf field: optional uint64 bid = 4;
     */
    bid?: bigint; // Bid price. Specified in 1/100000 of unit of a price. (e.g. 123000 in protocol means 1.23, ******** means 534.23782)
    /**
     * @generated from protobuf field: optional uint64 ask = 5;
     */
    ask?: bigint; // Ask price. Specified in 1/100000 of unit of a price. (e.g. 123000 in protocol means 1.23, ******** means 534.23782)
    /**
     * @generated from protobuf field: repeated ProtoOATrendbar trendbar = 6;
     */
    trendbar: ProtoOATrendbar[]; // Returns live trend bar. Requires subscription on the trend bars.
    /**
     * @generated from protobuf field: optional uint64 sessionClose = 7;
     */
    sessionClose?: bigint; // Last session close. Specified in 1/100000 of unit of a price. (e.g. 123000 in protocol means 1.23, ******** means 534.23782)
    /**
     * @generated from protobuf field: optional int64 timestamp = 8;
     */
    timestamp?: bigint; // The Unix time for spot.
}
/**
 * * Request for subscribing for live trend bars. Requires subscription on the spot events, see ProtoOASubscribeSpotsReq.
 *
 * @generated from protobuf message ProtoOASubscribeLiveTrendbarReq
 */
export interface ProtoOASubscribeLiveTrendbarReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: ProtoOATrendbarPeriod period = 3;
     */
    period: ProtoOATrendbarPeriod; // Specifies period of trend bar series (e.g. M1, M10, etc.).
    /**
     * @generated from protobuf field: int64 symbolId = 4;
     */
    symbolId: bigint; // Unique identifier of the Symbol in cTrader platform.
}
/**
 * * Response to the ProtoOASubscribeLiveTrendbarReq request.
 *
 * @generated from protobuf message ProtoOASubscribeLiveTrendbarRes
 */
export interface ProtoOASubscribeLiveTrendbarRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
}
/**
 * * Request for unsubscribing from the live trend bars.
 *
 * @generated from protobuf message ProtoOAUnsubscribeLiveTrendbarReq
 */
export interface ProtoOAUnsubscribeLiveTrendbarReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: ProtoOATrendbarPeriod period = 3;
     */
    period: ProtoOATrendbarPeriod; // Specifies period of trend bar series (e.g. M1, M10, etc.).
    /**
     * @generated from protobuf field: int64 symbolId = 4;
     */
    symbolId: bigint; // Unique identifier of the Symbol in cTrader platform.
}
/**
 * * Response to the ProtoOASubscribeLiveTrendbarReq request.
 *
 * @generated from protobuf message ProtoOAUnsubscribeLiveTrendbarRes
 */
export interface ProtoOAUnsubscribeLiveTrendbarRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
}
/**
 * * Request for getting historical trend bars for the symbol.
 *
 * @generated from protobuf message ProtoOAGetTrendbarsReq
 */
export interface ProtoOAGetTrendbarsReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: optional int64 fromTimestamp = 3;
     */
    fromTimestamp?: bigint; // The Unix time in milliseconds from which the search starts. Must be bigger or equal to zero (1st Jan 1970).
    /**
     * @generated from protobuf field: optional int64 toTimestamp = 4;
     */
    toTimestamp?: bigint; // The Unix time in milliseconds of finishing the search. Smaller or equal to ************* (19th Jan 2038).
    /**
     * @generated from protobuf field: ProtoOATrendbarPeriod period = 5;
     */
    period: ProtoOATrendbarPeriod; // Specifies period of trend bar series (e.g. M1, M10, etc.).
    /**
     * @generated from protobuf field: int64 symbolId = 6;
     */
    symbolId: bigint; // Unique identifier of the Symbol in cTrader platform.
    /**
     * @generated from protobuf field: optional uint32 count = 7;
     */
    count?: number; // Limit number of trend bars in response back from toTimestamp.
}
/**
 * * Response to the ProtoOAGetTrendbarsReq request.
 *
 * @generated from protobuf message ProtoOAGetTrendbarsRes
 */
export interface ProtoOAGetTrendbarsRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: ProtoOATrendbarPeriod period = 3;
     */
    period: ProtoOATrendbarPeriod; // Specifies period of trend bar series (e.g. M1, M10, etc.).
    /**
     * @deprecated
     * @generated from protobuf field: optional int64 timestamp = 4 [deprecated = true];
     */
    timestamp?: bigint; // Simply don't use this field, as your original request already contains toTimestamp.
    /**
     * @generated from protobuf field: repeated ProtoOATrendbar trendbar = 5;
     */
    trendbar: ProtoOATrendbar[]; // The list of trend bars.
    /**
     * @generated from protobuf field: optional int64 symbolId = 6;
     */
    symbolId?: bigint; // Unique identifier of the Symbol in cTrader platform.
    /**
     * @generated from protobuf field: optional bool hasMore = 7;
     */
    hasMore?: boolean; // If TRUE then the number of records by filter is larger than chunkSize, the response contains the number of records that is equal to chunkSize.
}
/**
 * * Request for getting historical tick data for the symbol.
 *
 * @generated from protobuf message ProtoOAGetTickDataReq
 */
export interface ProtoOAGetTickDataReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 symbolId = 3;
     */
    symbolId: bigint; // Unique identifier of the Symbol in cTrader platform.
    /**
     * @generated from protobuf field: ProtoOAQuoteType type = 4;
     */
    type: ProtoOAQuoteType; // Bid/Ask (1/2).
    /**
     * @generated from protobuf field: optional int64 fromTimestamp = 5;
     */
    fromTimestamp?: bigint; // The Unix time in milliseconds of starting the search. Must be bigger or equal to zero (1st Jan 1970).
    /**
     * @generated from protobuf field: optional int64 toTimestamp = 6;
     */
    toTimestamp?: bigint; // The Unix time in milliseconds of finishing the search. <= ************* (19th Jan 2038).
}
/**
 * * Response to the ProtoOAGetTickDataReq request.
 *
 * @generated from protobuf message ProtoOAGetTickDataRes
 */
export interface ProtoOAGetTickDataRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOATickData tickData = 3;
     */
    tickData: ProtoOATickData[]; // The list of ticks is in chronological order (newest first). The first tick contains Unix time in milliseconds while all subsequent ticks have the time difference in milliseconds between the previous and the current one.
    /**
     * @generated from protobuf field: bool hasMore = 4;
     */
    hasMore: boolean; // If TRUE then the number of records by filter is larger than chunkSize, the response contains the number of records that is equal to chunkSize.
}
// +------------------------------------------------------------------+
// |                      End quotes section                          |
// +------------------------------------------------------------------+

/**
 * * Request for getting details of Trader's profile. Limited due to GDRP requirements.
 *
 * @generated from protobuf message ProtoOAGetCtidProfileByTokenReq
 */
export interface ProtoOAGetCtidProfileByTokenReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: string accessToken = 2;
     */
    accessToken: string; // The Access Token issued for providing access to the Trader's Account.
}
/**
 * * Response to the ProtoOAGetCtidProfileByTokenReq request.
 *
 * @generated from protobuf message ProtoOAGetCtidProfileByTokenRes
 */
export interface ProtoOAGetCtidProfileByTokenRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: ProtoOACtidProfile profile = 2;
     */
    profile?: ProtoOACtidProfile; // Trader's profile.
}
/**
 * * Event that is sent when the structure of depth of market is changed. Requires subscription on the depth of markets for the symbol, see ProtoOASubscribeDepthQuotesReq.
 *
 * @generated from protobuf message ProtoOADepthEvent
 */
export interface ProtoOADepthEvent {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: uint64 symbolId = 3;
     */
    symbolId: bigint; // Unique identifier of the Symbol in cTrader platform.
    /**
     * @generated from protobuf field: repeated ProtoOADepthQuote newQuotes = 4;
     */
    newQuotes: ProtoOADepthQuote[]; // The list of changes in the depth of market quotes.
    /**
     * @generated from protobuf field: repeated uint64 deletedQuotes = 5 [packed = true];
     */
    deletedQuotes: bigint[]; // The list of quotes to delete.
}
/**
 * * Request for subscribing on depth of market of the specified symbol.
 *
 * @generated from protobuf message ProtoOASubscribeDepthQuotesReq
 */
export interface ProtoOASubscribeDepthQuotesReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated int64 symbolId = 3;
     */
    symbolId: bigint[]; // Unique identifier of the Symbol in cTrader platform.
}
/**
 * * Response to the ProtoOASubscribeDepthQuotesReq request.
 *
 * @generated from protobuf message ProtoOASubscribeDepthQuotesRes
 */
export interface ProtoOASubscribeDepthQuotesRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
}
/**
 * * Request for unsubscribing from the depth of market of the specified symbol.
 *
 * @generated from protobuf message ProtoOAUnsubscribeDepthQuotesReq
 */
export interface ProtoOAUnsubscribeDepthQuotesReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated int64 symbolId = 3;
     */
    symbolId: bigint[]; // Unique identifier of the Symbol in cTrader platform.
}
/**
 * * Response to the ProtoOAUnsubscribeDepthQuotesReq request.
 *
 * @generated from protobuf message ProtoOAUnsubscribeDepthQuotesRes
 */
export interface ProtoOAUnsubscribeDepthQuotesRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
}
/**
 * * Request for a list of symbol categories available for a trading account.
 *
 * @generated from protobuf message ProtoOASymbolCategoryListReq
 */
export interface ProtoOASymbolCategoryListReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
}
/**
 * * Response to the ProtoSymbolCategoryListReq request.
 *
 * @generated from protobuf message ProtoOASymbolCategoryListRes
 */
export interface ProtoOASymbolCategoryListRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOASymbolCategory symbolCategory = 3;
     */
    symbolCategory: ProtoOASymbolCategory[]; // The list of symbol categories.
}
/**
 * * Request for logout of trading account session.
 *
 * @generated from protobuf message ProtoOAAccountLogoutReq
 */
export interface ProtoOAAccountLogoutReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // The unique identifier of the trader's account in cTrader platform.
}
/**
 * * Response to the ProtoOATraderLogoutReq request. Actual logout of trading account will be completed on ProtoOAAccountDisconnectEvent.
 *
 * @generated from protobuf message ProtoOAAccountLogoutRes
 */
export interface ProtoOAAccountLogoutRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // The unique identifier of the trader's account in cTrader platform.
}
/**
 * * Event that is sent when the established session for an account is dropped on the server side. A new session must be authorized for the account.
 *
 * @generated from protobuf message ProtoOAAccountDisconnectEvent
 */
export interface ProtoOAAccountDisconnectEvent {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // The unique identifier of the trader's account in cTrader platform.
}
/**
 * * Request for a list of existing margin call thresholds configured for a user.
 *
 * @generated from protobuf message ProtoOAMarginCallListReq
 */
export interface ProtoOAMarginCallListReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint;
}
/**
 * * Response with a list of existing user Margin Calls, usually contains 3 items.
 *
 * @generated from protobuf message ProtoOAMarginCallListRes
 */
export interface ProtoOAMarginCallListRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: repeated ProtoOAMarginCall marginCall = 2;
     */
    marginCall: ProtoOAMarginCall[];
}
/**
 * * Request to modify marginLevelThreshold of specified marginCallType for ctidTraderAccountId.
 *
 * @generated from protobuf message ProtoOAMarginCallUpdateReq
 */
export interface ProtoOAMarginCallUpdateReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint;
    /**
     * @generated from protobuf field: ProtoOAMarginCall marginCall = 3;
     */
    marginCall?: ProtoOAMarginCall;
}
/**
 * * If this response received, it means that margin call was successfully updated.
 *
 * @generated from protobuf message ProtoOAMarginCallUpdateRes
 */
export interface ProtoOAMarginCallUpdateRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
}
/**
 * * Event that is sent when a Margin Call threshold configuration is updated.
 *
 * @generated from protobuf message ProtoOAMarginCallUpdateEvent
 */
export interface ProtoOAMarginCallUpdateEvent {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint;
    /**
     * @generated from protobuf field: ProtoOAMarginCall marginCall = 3;
     */
    marginCall?: ProtoOAMarginCall;
}
/**
 * * Event that is sent when account margin level reaches target marginLevelThreshold. Event is sent no more than once every 10 minutes to avoid spamming.
 *
 * @generated from protobuf message ProtoOAMarginCallTriggerEvent
 */
export interface ProtoOAMarginCallTriggerEvent {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint;
    /**
     * @generated from protobuf field: ProtoOAMarginCall marginCall = 3;
     */
    marginCall?: ProtoOAMarginCall;
}
/**
 * * Request for getting a dynamic leverage entity referenced in ProtoOASymbol.leverageId.
 *
 * @generated from protobuf message ProtoOAGetDynamicLeverageByIDReq
 */
export interface ProtoOAGetDynamicLeverageByIDReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 leverageId = 3;
     */
    leverageId: bigint;
}
/**
 * * Response to the ProtoOAGetDynamicLeverageByIDReq request.
 *
 * @generated from protobuf message ProtoOAGetDynamicLeverageByIDRes
 */
export interface ProtoOAGetDynamicLeverageByIDRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: ProtoOADynamicLeverage leverage = 3;
     */
    leverage?: ProtoOADynamicLeverage;
}
/**
 * * Request for retrieving the deals related to a position.
 *
 * @generated from protobuf message ProtoOADealListByPositionIdReq
 */
export interface ProtoOADealListByPositionIdReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 positionId = 3;
     */
    positionId: bigint; // The unique ID of the position.
    /**
     * @generated from protobuf field: optional int64 fromTimestamp = 4;
     */
    fromTimestamp?: bigint; // The Unix time in milliseconds of starting the search. Must be bigger or equal to zero (1st Jan 1970).
    /**
     * @generated from protobuf field: optional int64 toTimestamp = 5;
     */
    toTimestamp?: bigint; // The Unix time in milliseconds of finishing the search. <= ************* (19th Jan 2038).
}
/**
 * * Response to the ProtoOADealListByPositionIdReq request.
 *
 * @generated from protobuf message ProtoOADealListByPositionIdRes
 */
export interface ProtoOADealListByPositionIdRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOADeal deal = 3;
     */
    deal: ProtoOADeal[]; // The list of deals.
    /**
     * @generated from protobuf field: bool hasMore = 4;
     */
    hasMore: boolean; // If TRUE then the number of records by filter is larger than chunkSize, the response contains the number of records that is equal to chunkSize.
}
/**
 * * Request for getting Order and its related Deals.
 *
 * @generated from protobuf message ProtoOAOrderDetailsReq
 */
export interface ProtoOAOrderDetailsReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 orderId = 3;
     */
    orderId: bigint; // The unique ID of the Order.
}
/**
 * * Response to the ProtoOAOrderDetailsReq request.
 *
 * @generated from protobuf message ProtoOAOrderDetailsRes
 */
export interface ProtoOAOrderDetailsRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: ProtoOAOrder order = 3;
     */
    order?: ProtoOAOrder; // Order details.
    /**
     * @generated from protobuf field: repeated ProtoOADeal deal = 4;
     */
    deal: ProtoOADeal[]; // All Deals created by filling the specified Order.
}
/**
 * * Request for retrieving Orders related to a Position by using Position ID. Filtered by utcLastUpdateTimestamp.
 *
 * @generated from protobuf message ProtoOAOrderListByPositionIdReq
 */
export interface ProtoOAOrderListByPositionIdReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 positionId = 3;
     */
    positionId: bigint; // The unique ID of the Position.
    /**
     * @generated from protobuf field: optional int64 fromTimestamp = 4;
     */
    fromTimestamp?: bigint; // The Unix time from which the search starts >=0 (1st Jan 1970). Search by utcLastUpdateTimestamp of the Order.
    /**
     * @generated from protobuf field: optional int64 toTimestamp = 5;
     */
    toTimestamp?: bigint; // The Unix time where to stop searching <= ************* (19th Jan 2038). Search by utcLastUpdateTimestamp of the Order.
}
/**
 * * Response to ProtoOAOrderListByPositionIdReq request.
 *
 * @generated from protobuf message ProtoOAOrderListByPositionIdRes
 */
export interface ProtoOAOrderListByPositionIdRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOAOrder order = 3;
     */
    order: ProtoOAOrder[]; // Orders related to the specified Position, sorted by utcLastUpdateTimestamp in descending order (newest first).
    /**
     * @generated from protobuf field: bool hasMore = 4;
     */
    hasMore: boolean; // If TRUE then the number of records by filter is larger than chunkSize, the response contains the number of records that is equal to chunkSize.
}
/**
 * * Request for getting sets of Deals that were offset by a specific Deal and that are offsetting the Deal.
 *
 * @generated from protobuf message ProtoOADealOffsetListReq
 */
export interface ProtoOADealOffsetListReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 dealId = 3;
     */
    dealId: bigint; // The unique ID of the Deal.
}
/**
 * * Response for ProtoOADealOffsetListReq.
 *
 * @generated from protobuf message ProtoOADealOffsetListRes
 */
export interface ProtoOADealOffsetListRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: repeated ProtoOADealOffset offsetBy = 3;
     */
    offsetBy: ProtoOADealOffset[]; // Deals which closed the specified deal.
    /**
     * @generated from protobuf field: repeated ProtoOADealOffset offsetting = 4;
     */
    offsetting: ProtoOADealOffset[]; // Deals which were closed by the specified deal.
}
/**
 * * Request for getting trader's positions' unrealized PnLs.
 *
 * @generated from protobuf message ProtoOAGetPositionUnrealizedPnLReq
 */
export interface ProtoOAGetPositionUnrealizedPnLReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // The unique identifier of the trader's account in cTrader platform.
}
/**
 * * Response to ProtoOAGetPositionUnrealizedPnLReq request.
 *
 * @generated from protobuf message ProtoOAGetPositionUnrealizedPnLRes
 */
export interface ProtoOAGetPositionUnrealizedPnLRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // The unique identifier of the trader's account in cTrader platform.
    /**
     * @generated from protobuf field: repeated ProtoOAPositionUnrealizedPnL positionUnrealizedPnL = 3;
     */
    positionUnrealizedPnL: ProtoOAPositionUnrealizedPnL[]; // Information about trader's positions' unrealized PnLs.
    /**
     * @generated from protobuf field: uint32 moneyDigits = 4;
     */
    moneyDigits: number; // Specifies the exponent of various monetary values. E.g., moneyDigits = 8 should be interpreted as the value multiplied by 10^8 with the 'real' value equal to *********** / 10^8 = 100.********. Affects positionUnrealizedPnL.grossUnrealizedPnL, positionUnrealizedPnL.netUnrealizedPnL.
}
/**
 * The event that is sent when the unrealized PnL is changed due to market movement. Requires subscribing to PnL events, see ProtoOAv1PnLChangeSubscribeReq
 *
 * @generated from protobuf message ProtoOAv1PnLChangeEvent
 */
export interface ProtoOAv1PnLChangeEvent {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
    /**
     * @generated from protobuf field: int64 grossUnrealizedPnL = 3;
     */
    grossUnrealizedPnL: bigint; // The gross unrealized PnL denoted in the account deposit currency
    /**
     * @generated from protobuf field: int64 netUnrealizedPnL = 4;
     */
    netUnrealizedPnL: bigint; // The net unrealized PnL denoted in the account deposit currency
    /**
     * @generated from protobuf field: uint32 moneyDigits = 5;
     */
    moneyDigits: number; // Specifies the exponent of various monetary values. E.g., moneyDigits = 8 should be interpreted as the value multiplied by 10^8 with the 'real' value equal to *********** / 10^8 = 100.********
}
/**
 * The request to subscribe to ProtoOAv1PnLChangeEvent
 *
 * @generated from protobuf message ProtoOAv1PnLChangeSubscribeReq
 */
export interface ProtoOAv1PnLChangeSubscribeReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
}
/**
 * The response for ProtoOAv1PnLChangeSubscribeReq
 *
 * @generated from protobuf message ProtoOAv1PnLChangeSubscribeRes
 */
export interface ProtoOAv1PnLChangeSubscribeRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // The unique identifier of the trader's account in cTrader platform.
}
/**
 * The request to stop an existing subscription to PnL events. The subscriber who sends this request will stop receiving ProtoOAv1PnLChangeEvent
 *
 * @generated from protobuf message ProtoOAv1PnLChangeUnSubscribeReq
 */
export interface ProtoOAv1PnLChangeUnSubscribeReq {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
}
/**
 * The response for ProtoOAv1PnLChangeUnSubscribeReq
 *
 * @generated from protobuf message ProtoOAv1PnLChangeUnSubscribeRes
 */
export interface ProtoOAv1PnLChangeUnSubscribeRes {
    /**
     * @generated from protobuf field: optional ProtoOAPayloadType payloadType = 1;
     */
    payloadType?: ProtoOAPayloadType;
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 2;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.
}
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAApplicationAuthReq$Type extends MessageType<ProtoOAApplicationAuthReq> {
    constructor() {
        super("ProtoOAApplicationAuthReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "clientId", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "clientSecret", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAApplicationAuthReq
 */
export const ProtoOAApplicationAuthReq = new ProtoOAApplicationAuthReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAApplicationAuthRes$Type extends MessageType<ProtoOAApplicationAuthRes> {
    constructor() {
        super("ProtoOAApplicationAuthRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAApplicationAuthRes
 */
export const ProtoOAApplicationAuthRes = new ProtoOAApplicationAuthRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAAccountAuthReq$Type extends MessageType<ProtoOAAccountAuthReq> {
    constructor() {
        super("ProtoOAAccountAuthReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "accessToken", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAAccountAuthReq
 */
export const ProtoOAAccountAuthReq = new ProtoOAAccountAuthReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAAccountAuthRes$Type extends MessageType<ProtoOAAccountAuthRes> {
    constructor() {
        super("ProtoOAAccountAuthRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAAccountAuthRes
 */
export const ProtoOAAccountAuthRes = new ProtoOAAccountAuthRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAErrorRes$Type extends MessageType<ProtoOAErrorRes> {
    constructor() {
        super("ProtoOAErrorRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "errorCode", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "description", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 5, name: "maintenanceEndTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 6, name: "retryAfter", kind: "scalar", opt: true, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAErrorRes
 */
export const ProtoOAErrorRes = new ProtoOAErrorRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAClientDisconnectEvent$Type extends MessageType<ProtoOAClientDisconnectEvent> {
    constructor() {
        super("ProtoOAClientDisconnectEvent", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "reason", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAClientDisconnectEvent
 */
export const ProtoOAClientDisconnectEvent = new ProtoOAClientDisconnectEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAAccountsTokenInvalidatedEvent$Type extends MessageType<ProtoOAAccountsTokenInvalidatedEvent> {
    constructor() {
        super("ProtoOAAccountsTokenInvalidatedEvent", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountIds", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "reason", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAAccountsTokenInvalidatedEvent
 */
export const ProtoOAAccountsTokenInvalidatedEvent = new ProtoOAAccountsTokenInvalidatedEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAVersionReq$Type extends MessageType<ProtoOAVersionReq> {
    constructor() {
        super("ProtoOAVersionReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAVersionReq
 */
export const ProtoOAVersionReq = new ProtoOAVersionReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAVersionRes$Type extends MessageType<ProtoOAVersionRes> {
    constructor() {
        super("ProtoOAVersionRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "version", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAVersionRes
 */
export const ProtoOAVersionRes = new ProtoOAVersionRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOANewOrderReq$Type extends MessageType<ProtoOANewOrderReq> {
    constructor() {
        super("ProtoOANewOrderReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbolId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "orderType", kind: "enum", T: () => ["ProtoOAOrderType", ProtoOAOrderType] },
            { no: 5, name: "tradeSide", kind: "enum", T: () => ["ProtoOATradeSide", ProtoOATradeSide] },
            { no: 6, name: "volume", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 7, name: "limitPrice", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 8, name: "stopPrice", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 9, name: "timeInForce", kind: "enum", opt: true, T: () => ["ProtoOATimeInForce", ProtoOATimeInForce] },
            { no: 10, name: "expirationTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 11, name: "stopLoss", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 12, name: "takeProfit", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 13, name: "comment", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 14, name: "baseSlippagePrice", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 15, name: "slippageInPoints", kind: "scalar", opt: true, T: 5 /*ScalarType.INT32*/ },
            { no: 16, name: "label", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 17, name: "positionId", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 18, name: "clientOrderId", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 19, name: "relativeStopLoss", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 20, name: "relativeTakeProfit", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 21, name: "guaranteedStopLoss", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 22, name: "trailingStopLoss", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 23, name: "stopTriggerMethod", kind: "enum", opt: true, T: () => ["ProtoOAOrderTriggerMethod", ProtoOAOrderTriggerMethod] }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOANewOrderReq
 */
export const ProtoOANewOrderReq = new ProtoOANewOrderReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAExecutionEvent$Type extends MessageType<ProtoOAExecutionEvent> {
    constructor() {
        super("ProtoOAExecutionEvent", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "executionType", kind: "enum", T: () => ["ProtoOAExecutionType", ProtoOAExecutionType] },
            { no: 4, name: "position", kind: "message", T: () => ProtoOAPosition },
            { no: 5, name: "order", kind: "message", T: () => ProtoOAOrder },
            { no: 6, name: "deal", kind: "message", T: () => ProtoOADeal },
            { no: 7, name: "bonusDepositWithdraw", kind: "message", T: () => ProtoOABonusDepositWithdraw },
            { no: 8, name: "depositWithdraw", kind: "message", T: () => ProtoOADepositWithdraw },
            { no: 9, name: "errorCode", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 10, name: "isServerEvent", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAExecutionEvent
 */
export const ProtoOAExecutionEvent = new ProtoOAExecutionEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOACancelOrderReq$Type extends MessageType<ProtoOACancelOrderReq> {
    constructor() {
        super("ProtoOACancelOrderReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "orderId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOACancelOrderReq
 */
export const ProtoOACancelOrderReq = new ProtoOACancelOrderReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAAmendOrderReq$Type extends MessageType<ProtoOAAmendOrderReq> {
    constructor() {
        super("ProtoOAAmendOrderReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "orderId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "volume", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "limitPrice", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 6, name: "stopPrice", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 7, name: "expirationTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 8, name: "stopLoss", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 9, name: "takeProfit", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 10, name: "slippageInPoints", kind: "scalar", opt: true, T: 5 /*ScalarType.INT32*/ },
            { no: 11, name: "relativeStopLoss", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 12, name: "relativeTakeProfit", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 13, name: "guaranteedStopLoss", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 14, name: "trailingStopLoss", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 15, name: "stopTriggerMethod", kind: "enum", opt: true, T: () => ["ProtoOAOrderTriggerMethod", ProtoOAOrderTriggerMethod] }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAAmendOrderReq
 */
export const ProtoOAAmendOrderReq = new ProtoOAAmendOrderReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAAmendPositionSLTPReq$Type extends MessageType<ProtoOAAmendPositionSLTPReq> {
    constructor() {
        super("ProtoOAAmendPositionSLTPReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "positionId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "stopLoss", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 5, name: "takeProfit", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 7, name: "guaranteedStopLoss", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 8, name: "trailingStopLoss", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 9, name: "stopLossTriggerMethod", kind: "enum", opt: true, T: () => ["ProtoOAOrderTriggerMethod", ProtoOAOrderTriggerMethod] }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAAmendPositionSLTPReq
 */
export const ProtoOAAmendPositionSLTPReq = new ProtoOAAmendPositionSLTPReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAClosePositionReq$Type extends MessageType<ProtoOAClosePositionReq> {
    constructor() {
        super("ProtoOAClosePositionReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "positionId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "volume", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAClosePositionReq
 */
export const ProtoOAClosePositionReq = new ProtoOAClosePositionReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOATrailingSLChangedEvent$Type extends MessageType<ProtoOATrailingSLChangedEvent> {
    constructor() {
        super("ProtoOATrailingSLChangedEvent", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "positionId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "orderId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "stopPrice", kind: "scalar", T: 1 /*ScalarType.DOUBLE*/ },
            { no: 6, name: "utcLastUpdateTimestamp", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOATrailingSLChangedEvent
 */
export const ProtoOATrailingSLChangedEvent = new ProtoOATrailingSLChangedEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAAssetListReq$Type extends MessageType<ProtoOAAssetListReq> {
    constructor() {
        super("ProtoOAAssetListReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAAssetListReq
 */
export const ProtoOAAssetListReq = new ProtoOAAssetListReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAAssetListRes$Type extends MessageType<ProtoOAAssetListRes> {
    constructor() {
        super("ProtoOAAssetListRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "asset", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOAAsset }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAAssetListRes
 */
export const ProtoOAAssetListRes = new ProtoOAAssetListRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASymbolsListReq$Type extends MessageType<ProtoOASymbolsListReq> {
    constructor() {
        super("ProtoOASymbolsListReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "includeArchivedSymbols", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASymbolsListReq
 */
export const ProtoOASymbolsListReq = new ProtoOASymbolsListReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASymbolsListRes$Type extends MessageType<ProtoOASymbolsListRes> {
    constructor() {
        super("ProtoOASymbolsListRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbol", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOALightSymbol },
            { no: 4, name: "archivedSymbol", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOAArchivedSymbol }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASymbolsListRes
 */
export const ProtoOASymbolsListRes = new ProtoOASymbolsListRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASymbolByIdReq$Type extends MessageType<ProtoOASymbolByIdReq> {
    constructor() {
        super("ProtoOASymbolByIdReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbolId", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASymbolByIdReq
 */
export const ProtoOASymbolByIdReq = new ProtoOASymbolByIdReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASymbolByIdRes$Type extends MessageType<ProtoOASymbolByIdRes> {
    constructor() {
        super("ProtoOASymbolByIdRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbol", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOASymbol },
            { no: 4, name: "archivedSymbol", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOAArchivedSymbol }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASymbolByIdRes
 */
export const ProtoOASymbolByIdRes = new ProtoOASymbolByIdRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASymbolsForConversionReq$Type extends MessageType<ProtoOASymbolsForConversionReq> {
    constructor() {
        super("ProtoOASymbolsForConversionReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "firstAssetId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "lastAssetId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASymbolsForConversionReq
 */
export const ProtoOASymbolsForConversionReq = new ProtoOASymbolsForConversionReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASymbolsForConversionRes$Type extends MessageType<ProtoOASymbolsForConversionRes> {
    constructor() {
        super("ProtoOASymbolsForConversionRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbol", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOALightSymbol }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASymbolsForConversionRes
 */
export const ProtoOASymbolsForConversionRes = new ProtoOASymbolsForConversionRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASymbolChangedEvent$Type extends MessageType<ProtoOASymbolChangedEvent> {
    constructor() {
        super("ProtoOASymbolChangedEvent", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbolId", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASymbolChangedEvent
 */
export const ProtoOASymbolChangedEvent = new ProtoOASymbolChangedEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAAssetClassListReq$Type extends MessageType<ProtoOAAssetClassListReq> {
    constructor() {
        super("ProtoOAAssetClassListReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAAssetClassListReq
 */
export const ProtoOAAssetClassListReq = new ProtoOAAssetClassListReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAAssetClassListRes$Type extends MessageType<ProtoOAAssetClassListRes> {
    constructor() {
        super("ProtoOAAssetClassListRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "assetClass", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOAAssetClass }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAAssetClassListRes
 */
export const ProtoOAAssetClassListRes = new ProtoOAAssetClassListRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOATraderReq$Type extends MessageType<ProtoOATraderReq> {
    constructor() {
        super("ProtoOATraderReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOATraderReq
 */
export const ProtoOATraderReq = new ProtoOATraderReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOATraderRes$Type extends MessageType<ProtoOATraderRes> {
    constructor() {
        super("ProtoOATraderRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "trader", kind: "message", T: () => ProtoOATrader }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOATraderRes
 */
export const ProtoOATraderRes = new ProtoOATraderRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOATraderUpdatedEvent$Type extends MessageType<ProtoOATraderUpdatedEvent> {
    constructor() {
        super("ProtoOATraderUpdatedEvent", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "trader", kind: "message", T: () => ProtoOATrader }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOATraderUpdatedEvent
 */
export const ProtoOATraderUpdatedEvent = new ProtoOATraderUpdatedEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAReconcileReq$Type extends MessageType<ProtoOAReconcileReq> {
    constructor() {
        super("ProtoOAReconcileReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "returnProtectionOrders", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAReconcileReq
 */
export const ProtoOAReconcileReq = new ProtoOAReconcileReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAReconcileRes$Type extends MessageType<ProtoOAReconcileRes> {
    constructor() {
        super("ProtoOAReconcileRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "position", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOAPosition },
            { no: 4, name: "order", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOAOrder }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAReconcileRes
 */
export const ProtoOAReconcileRes = new ProtoOAReconcileRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAOrderErrorEvent$Type extends MessageType<ProtoOAOrderErrorEvent> {
    constructor() {
        super("ProtoOAOrderErrorEvent", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 5, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "errorCode", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "orderId", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 6, name: "positionId", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 7, name: "description", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAOrderErrorEvent
 */
export const ProtoOAOrderErrorEvent = new ProtoOAOrderErrorEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOADealListReq$Type extends MessageType<ProtoOADealListReq> {
    constructor() {
        super("ProtoOADealListReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "fromTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "toTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "maxRows", kind: "scalar", opt: true, T: 5 /*ScalarType.INT32*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOADealListReq
 */
export const ProtoOADealListReq = new ProtoOADealListReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOADealListRes$Type extends MessageType<ProtoOADealListRes> {
    constructor() {
        super("ProtoOADealListRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "deal", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOADeal },
            { no: 4, name: "hasMore", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOADealListRes
 */
export const ProtoOADealListRes = new ProtoOADealListRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAOrderListReq$Type extends MessageType<ProtoOAOrderListReq> {
    constructor() {
        super("ProtoOAOrderListReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "fromTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "toTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAOrderListReq
 */
export const ProtoOAOrderListReq = new ProtoOAOrderListReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAOrderListRes$Type extends MessageType<ProtoOAOrderListRes> {
    constructor() {
        super("ProtoOAOrderListRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "order", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOAOrder },
            { no: 4, name: "hasMore", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAOrderListRes
 */
export const ProtoOAOrderListRes = new ProtoOAOrderListRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAExpectedMarginReq$Type extends MessageType<ProtoOAExpectedMarginReq> {
    constructor() {
        super("ProtoOAExpectedMarginReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbolId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "volume", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAExpectedMarginReq
 */
export const ProtoOAExpectedMarginReq = new ProtoOAExpectedMarginReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAExpectedMarginRes$Type extends MessageType<ProtoOAExpectedMarginRes> {
    constructor() {
        super("ProtoOAExpectedMarginRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "margin", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOAExpectedMargin },
            { no: 4, name: "moneyDigits", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAExpectedMarginRes
 */
export const ProtoOAExpectedMarginRes = new ProtoOAExpectedMarginRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAMarginChangedEvent$Type extends MessageType<ProtoOAMarginChangedEvent> {
    constructor() {
        super("ProtoOAMarginChangedEvent", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "positionId", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "usedMargin", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "moneyDigits", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAMarginChangedEvent
 */
export const ProtoOAMarginChangedEvent = new ProtoOAMarginChangedEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOACashFlowHistoryListReq$Type extends MessageType<ProtoOACashFlowHistoryListReq> {
    constructor() {
        super("ProtoOACashFlowHistoryListReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "fromTimestamp", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "toTimestamp", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOACashFlowHistoryListReq
 */
export const ProtoOACashFlowHistoryListReq = new ProtoOACashFlowHistoryListReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOACashFlowHistoryListRes$Type extends MessageType<ProtoOACashFlowHistoryListRes> {
    constructor() {
        super("ProtoOACashFlowHistoryListRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "depositWithdraw", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOADepositWithdraw }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOACashFlowHistoryListRes
 */
export const ProtoOACashFlowHistoryListRes = new ProtoOACashFlowHistoryListRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAGetAccountListByAccessTokenReq$Type extends MessageType<ProtoOAGetAccountListByAccessTokenReq> {
    constructor() {
        super("ProtoOAGetAccountListByAccessTokenReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "accessToken", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAGetAccountListByAccessTokenReq
 */
export const ProtoOAGetAccountListByAccessTokenReq = new ProtoOAGetAccountListByAccessTokenReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAGetAccountListByAccessTokenRes$Type extends MessageType<ProtoOAGetAccountListByAccessTokenRes> {
    constructor() {
        super("ProtoOAGetAccountListByAccessTokenRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "accessToken", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "permissionScope", kind: "enum", opt: true, T: () => ["ProtoOAClientPermissionScope", ProtoOAClientPermissionScope] },
            { no: 4, name: "ctidTraderAccount", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOACtidTraderAccount }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAGetAccountListByAccessTokenRes
 */
export const ProtoOAGetAccountListByAccessTokenRes = new ProtoOAGetAccountListByAccessTokenRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOARefreshTokenReq$Type extends MessageType<ProtoOARefreshTokenReq> {
    constructor() {
        super("ProtoOARefreshTokenReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "refreshToken", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOARefreshTokenReq
 */
export const ProtoOARefreshTokenReq = new ProtoOARefreshTokenReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOARefreshTokenRes$Type extends MessageType<ProtoOARefreshTokenRes> {
    constructor() {
        super("ProtoOARefreshTokenRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "accessToken", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "tokenType", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "expiresIn", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "refreshToken", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOARefreshTokenRes
 */
export const ProtoOARefreshTokenRes = new ProtoOARefreshTokenRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASubscribeSpotsReq$Type extends MessageType<ProtoOASubscribeSpotsReq> {
    constructor() {
        super("ProtoOASubscribeSpotsReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbolId", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "subscribeToSpotTimestamp", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASubscribeSpotsReq
 */
export const ProtoOASubscribeSpotsReq = new ProtoOASubscribeSpotsReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASubscribeSpotsRes$Type extends MessageType<ProtoOASubscribeSpotsRes> {
    constructor() {
        super("ProtoOASubscribeSpotsRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASubscribeSpotsRes
 */
export const ProtoOASubscribeSpotsRes = new ProtoOASubscribeSpotsRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAUnsubscribeSpotsReq$Type extends MessageType<ProtoOAUnsubscribeSpotsReq> {
    constructor() {
        super("ProtoOAUnsubscribeSpotsReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbolId", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAUnsubscribeSpotsReq
 */
export const ProtoOAUnsubscribeSpotsReq = new ProtoOAUnsubscribeSpotsReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAUnsubscribeSpotsRes$Type extends MessageType<ProtoOAUnsubscribeSpotsRes> {
    constructor() {
        super("ProtoOAUnsubscribeSpotsRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAUnsubscribeSpotsRes
 */
export const ProtoOAUnsubscribeSpotsRes = new ProtoOAUnsubscribeSpotsRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASpotEvent$Type extends MessageType<ProtoOASpotEvent> {
    constructor() {
        super("ProtoOASpotEvent", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbolId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "bid", kind: "scalar", opt: true, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "ask", kind: "scalar", opt: true, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 6, name: "trendbar", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOATrendbar },
            { no: 7, name: "sessionClose", kind: "scalar", opt: true, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 8, name: "timestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASpotEvent
 */
export const ProtoOASpotEvent = new ProtoOASpotEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASubscribeLiveTrendbarReq$Type extends MessageType<ProtoOASubscribeLiveTrendbarReq> {
    constructor() {
        super("ProtoOASubscribeLiveTrendbarReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "period", kind: "enum", T: () => ["ProtoOATrendbarPeriod", ProtoOATrendbarPeriod] },
            { no: 4, name: "symbolId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASubscribeLiveTrendbarReq
 */
export const ProtoOASubscribeLiveTrendbarReq = new ProtoOASubscribeLiveTrendbarReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASubscribeLiveTrendbarRes$Type extends MessageType<ProtoOASubscribeLiveTrendbarRes> {
    constructor() {
        super("ProtoOASubscribeLiveTrendbarRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASubscribeLiveTrendbarRes
 */
export const ProtoOASubscribeLiveTrendbarRes = new ProtoOASubscribeLiveTrendbarRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAUnsubscribeLiveTrendbarReq$Type extends MessageType<ProtoOAUnsubscribeLiveTrendbarReq> {
    constructor() {
        super("ProtoOAUnsubscribeLiveTrendbarReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "period", kind: "enum", T: () => ["ProtoOATrendbarPeriod", ProtoOATrendbarPeriod] },
            { no: 4, name: "symbolId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAUnsubscribeLiveTrendbarReq
 */
export const ProtoOAUnsubscribeLiveTrendbarReq = new ProtoOAUnsubscribeLiveTrendbarReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAUnsubscribeLiveTrendbarRes$Type extends MessageType<ProtoOAUnsubscribeLiveTrendbarRes> {
    constructor() {
        super("ProtoOAUnsubscribeLiveTrendbarRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAUnsubscribeLiveTrendbarRes
 */
export const ProtoOAUnsubscribeLiveTrendbarRes = new ProtoOAUnsubscribeLiveTrendbarRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAGetTrendbarsReq$Type extends MessageType<ProtoOAGetTrendbarsReq> {
    constructor() {
        super("ProtoOAGetTrendbarsReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "fromTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "toTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "period", kind: "enum", T: () => ["ProtoOATrendbarPeriod", ProtoOATrendbarPeriod] },
            { no: 6, name: "symbolId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 7, name: "count", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAGetTrendbarsReq
 */
export const ProtoOAGetTrendbarsReq = new ProtoOAGetTrendbarsReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAGetTrendbarsRes$Type extends MessageType<ProtoOAGetTrendbarsRes> {
    constructor() {
        super("ProtoOAGetTrendbarsRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "period", kind: "enum", T: () => ["ProtoOATrendbarPeriod", ProtoOATrendbarPeriod] },
            { no: 4, name: "timestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "trendbar", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOATrendbar },
            { no: 6, name: "symbolId", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 7, name: "hasMore", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAGetTrendbarsRes
 */
export const ProtoOAGetTrendbarsRes = new ProtoOAGetTrendbarsRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAGetTickDataReq$Type extends MessageType<ProtoOAGetTickDataReq> {
    constructor() {
        super("ProtoOAGetTickDataReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbolId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "type", kind: "enum", T: () => ["ProtoOAQuoteType", ProtoOAQuoteType] },
            { no: 5, name: "fromTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 6, name: "toTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAGetTickDataReq
 */
export const ProtoOAGetTickDataReq = new ProtoOAGetTickDataReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAGetTickDataRes$Type extends MessageType<ProtoOAGetTickDataRes> {
    constructor() {
        super("ProtoOAGetTickDataRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "tickData", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOATickData },
            { no: 4, name: "hasMore", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAGetTickDataRes
 */
export const ProtoOAGetTickDataRes = new ProtoOAGetTickDataRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAGetCtidProfileByTokenReq$Type extends MessageType<ProtoOAGetCtidProfileByTokenReq> {
    constructor() {
        super("ProtoOAGetCtidProfileByTokenReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "accessToken", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAGetCtidProfileByTokenReq
 */
export const ProtoOAGetCtidProfileByTokenReq = new ProtoOAGetCtidProfileByTokenReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAGetCtidProfileByTokenRes$Type extends MessageType<ProtoOAGetCtidProfileByTokenRes> {
    constructor() {
        super("ProtoOAGetCtidProfileByTokenRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "profile", kind: "message", T: () => ProtoOACtidProfile }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAGetCtidProfileByTokenRes
 */
export const ProtoOAGetCtidProfileByTokenRes = new ProtoOAGetCtidProfileByTokenRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOADepthEvent$Type extends MessageType<ProtoOADepthEvent> {
    constructor() {
        super("ProtoOADepthEvent", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbolId", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "newQuotes", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOADepthQuote },
            { no: 5, name: "deletedQuotes", kind: "scalar", repeat: 1 /*RepeatType.PACKED*/, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOADepthEvent
 */
export const ProtoOADepthEvent = new ProtoOADepthEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASubscribeDepthQuotesReq$Type extends MessageType<ProtoOASubscribeDepthQuotesReq> {
    constructor() {
        super("ProtoOASubscribeDepthQuotesReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbolId", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASubscribeDepthQuotesReq
 */
export const ProtoOASubscribeDepthQuotesReq = new ProtoOASubscribeDepthQuotesReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASubscribeDepthQuotesRes$Type extends MessageType<ProtoOASubscribeDepthQuotesRes> {
    constructor() {
        super("ProtoOASubscribeDepthQuotesRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASubscribeDepthQuotesRes
 */
export const ProtoOASubscribeDepthQuotesRes = new ProtoOASubscribeDepthQuotesRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAUnsubscribeDepthQuotesReq$Type extends MessageType<ProtoOAUnsubscribeDepthQuotesReq> {
    constructor() {
        super("ProtoOAUnsubscribeDepthQuotesReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbolId", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAUnsubscribeDepthQuotesReq
 */
export const ProtoOAUnsubscribeDepthQuotesReq = new ProtoOAUnsubscribeDepthQuotesReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAUnsubscribeDepthQuotesRes$Type extends MessageType<ProtoOAUnsubscribeDepthQuotesRes> {
    constructor() {
        super("ProtoOAUnsubscribeDepthQuotesRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAUnsubscribeDepthQuotesRes
 */
export const ProtoOAUnsubscribeDepthQuotesRes = new ProtoOAUnsubscribeDepthQuotesRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASymbolCategoryListReq$Type extends MessageType<ProtoOASymbolCategoryListReq> {
    constructor() {
        super("ProtoOASymbolCategoryListReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASymbolCategoryListReq
 */
export const ProtoOASymbolCategoryListReq = new ProtoOASymbolCategoryListReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASymbolCategoryListRes$Type extends MessageType<ProtoOASymbolCategoryListRes> {
    constructor() {
        super("ProtoOASymbolCategoryListRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "symbolCategory", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOASymbolCategory }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASymbolCategoryListRes
 */
export const ProtoOASymbolCategoryListRes = new ProtoOASymbolCategoryListRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAAccountLogoutReq$Type extends MessageType<ProtoOAAccountLogoutReq> {
    constructor() {
        super("ProtoOAAccountLogoutReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAAccountLogoutReq
 */
export const ProtoOAAccountLogoutReq = new ProtoOAAccountLogoutReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAAccountLogoutRes$Type extends MessageType<ProtoOAAccountLogoutRes> {
    constructor() {
        super("ProtoOAAccountLogoutRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAAccountLogoutRes
 */
export const ProtoOAAccountLogoutRes = new ProtoOAAccountLogoutRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAAccountDisconnectEvent$Type extends MessageType<ProtoOAAccountDisconnectEvent> {
    constructor() {
        super("ProtoOAAccountDisconnectEvent", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAAccountDisconnectEvent
 */
export const ProtoOAAccountDisconnectEvent = new ProtoOAAccountDisconnectEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAMarginCallListReq$Type extends MessageType<ProtoOAMarginCallListReq> {
    constructor() {
        super("ProtoOAMarginCallListReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAMarginCallListReq
 */
export const ProtoOAMarginCallListReq = new ProtoOAMarginCallListReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAMarginCallListRes$Type extends MessageType<ProtoOAMarginCallListRes> {
    constructor() {
        super("ProtoOAMarginCallListRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "marginCall", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOAMarginCall }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAMarginCallListRes
 */
export const ProtoOAMarginCallListRes = new ProtoOAMarginCallListRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAMarginCallUpdateReq$Type extends MessageType<ProtoOAMarginCallUpdateReq> {
    constructor() {
        super("ProtoOAMarginCallUpdateReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "marginCall", kind: "message", T: () => ProtoOAMarginCall }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAMarginCallUpdateReq
 */
export const ProtoOAMarginCallUpdateReq = new ProtoOAMarginCallUpdateReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAMarginCallUpdateRes$Type extends MessageType<ProtoOAMarginCallUpdateRes> {
    constructor() {
        super("ProtoOAMarginCallUpdateRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAMarginCallUpdateRes
 */
export const ProtoOAMarginCallUpdateRes = new ProtoOAMarginCallUpdateRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAMarginCallUpdateEvent$Type extends MessageType<ProtoOAMarginCallUpdateEvent> {
    constructor() {
        super("ProtoOAMarginCallUpdateEvent", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "marginCall", kind: "message", T: () => ProtoOAMarginCall }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAMarginCallUpdateEvent
 */
export const ProtoOAMarginCallUpdateEvent = new ProtoOAMarginCallUpdateEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAMarginCallTriggerEvent$Type extends MessageType<ProtoOAMarginCallTriggerEvent> {
    constructor() {
        super("ProtoOAMarginCallTriggerEvent", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "marginCall", kind: "message", T: () => ProtoOAMarginCall }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAMarginCallTriggerEvent
 */
export const ProtoOAMarginCallTriggerEvent = new ProtoOAMarginCallTriggerEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAGetDynamicLeverageByIDReq$Type extends MessageType<ProtoOAGetDynamicLeverageByIDReq> {
    constructor() {
        super("ProtoOAGetDynamicLeverageByIDReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "leverageId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAGetDynamicLeverageByIDReq
 */
export const ProtoOAGetDynamicLeverageByIDReq = new ProtoOAGetDynamicLeverageByIDReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAGetDynamicLeverageByIDRes$Type extends MessageType<ProtoOAGetDynamicLeverageByIDRes> {
    constructor() {
        super("ProtoOAGetDynamicLeverageByIDRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "leverage", kind: "message", T: () => ProtoOADynamicLeverage }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAGetDynamicLeverageByIDRes
 */
export const ProtoOAGetDynamicLeverageByIDRes = new ProtoOAGetDynamicLeverageByIDRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOADealListByPositionIdReq$Type extends MessageType<ProtoOADealListByPositionIdReq> {
    constructor() {
        super("ProtoOADealListByPositionIdReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "positionId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "fromTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "toTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOADealListByPositionIdReq
 */
export const ProtoOADealListByPositionIdReq = new ProtoOADealListByPositionIdReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOADealListByPositionIdRes$Type extends MessageType<ProtoOADealListByPositionIdRes> {
    constructor() {
        super("ProtoOADealListByPositionIdRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "deal", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOADeal },
            { no: 4, name: "hasMore", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOADealListByPositionIdRes
 */
export const ProtoOADealListByPositionIdRes = new ProtoOADealListByPositionIdRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAOrderDetailsReq$Type extends MessageType<ProtoOAOrderDetailsReq> {
    constructor() {
        super("ProtoOAOrderDetailsReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "orderId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAOrderDetailsReq
 */
export const ProtoOAOrderDetailsReq = new ProtoOAOrderDetailsReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAOrderDetailsRes$Type extends MessageType<ProtoOAOrderDetailsRes> {
    constructor() {
        super("ProtoOAOrderDetailsRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "order", kind: "message", T: () => ProtoOAOrder },
            { no: 4, name: "deal", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOADeal }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAOrderDetailsRes
 */
export const ProtoOAOrderDetailsRes = new ProtoOAOrderDetailsRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAOrderListByPositionIdReq$Type extends MessageType<ProtoOAOrderListByPositionIdReq> {
    constructor() {
        super("ProtoOAOrderListByPositionIdReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "positionId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "fromTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "toTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAOrderListByPositionIdReq
 */
export const ProtoOAOrderListByPositionIdReq = new ProtoOAOrderListByPositionIdReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAOrderListByPositionIdRes$Type extends MessageType<ProtoOAOrderListByPositionIdRes> {
    constructor() {
        super("ProtoOAOrderListByPositionIdRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "order", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOAOrder },
            { no: 4, name: "hasMore", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAOrderListByPositionIdRes
 */
export const ProtoOAOrderListByPositionIdRes = new ProtoOAOrderListByPositionIdRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOADealOffsetListReq$Type extends MessageType<ProtoOADealOffsetListReq> {
    constructor() {
        super("ProtoOADealOffsetListReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "dealId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOADealOffsetListReq
 */
export const ProtoOADealOffsetListReq = new ProtoOADealOffsetListReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOADealOffsetListRes$Type extends MessageType<ProtoOADealOffsetListRes> {
    constructor() {
        super("ProtoOADealOffsetListRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "offsetBy", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOADealOffset },
            { no: 4, name: "offsetting", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOADealOffset }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOADealOffsetListRes
 */
export const ProtoOADealOffsetListRes = new ProtoOADealOffsetListRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAGetPositionUnrealizedPnLReq$Type extends MessageType<ProtoOAGetPositionUnrealizedPnLReq> {
    constructor() {
        super("ProtoOAGetPositionUnrealizedPnLReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAGetPositionUnrealizedPnLReq
 */
export const ProtoOAGetPositionUnrealizedPnLReq = new ProtoOAGetPositionUnrealizedPnLReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAGetPositionUnrealizedPnLRes$Type extends MessageType<ProtoOAGetPositionUnrealizedPnLRes> {
    constructor() {
        super("ProtoOAGetPositionUnrealizedPnLRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "positionUnrealizedPnL", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOAPositionUnrealizedPnL },
            { no: 4, name: "moneyDigits", kind: "scalar", T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAGetPositionUnrealizedPnLRes
 */
export const ProtoOAGetPositionUnrealizedPnLRes = new ProtoOAGetPositionUnrealizedPnLRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAv1PnLChangeEvent$Type extends MessageType<ProtoOAv1PnLChangeEvent> {
    constructor() {
        super("ProtoOAv1PnLChangeEvent", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "grossUnrealizedPnL", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "netUnrealizedPnL", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "moneyDigits", kind: "scalar", T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAv1PnLChangeEvent
 */
export const ProtoOAv1PnLChangeEvent = new ProtoOAv1PnLChangeEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAv1PnLChangeSubscribeReq$Type extends MessageType<ProtoOAv1PnLChangeSubscribeReq> {
    constructor() {
        super("ProtoOAv1PnLChangeSubscribeReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAv1PnLChangeSubscribeReq
 */
export const ProtoOAv1PnLChangeSubscribeReq = new ProtoOAv1PnLChangeSubscribeReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAv1PnLChangeSubscribeRes$Type extends MessageType<ProtoOAv1PnLChangeSubscribeRes> {
    constructor() {
        super("ProtoOAv1PnLChangeSubscribeRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAv1PnLChangeSubscribeRes
 */
export const ProtoOAv1PnLChangeSubscribeRes = new ProtoOAv1PnLChangeSubscribeRes$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAv1PnLChangeUnSubscribeReq$Type extends MessageType<ProtoOAv1PnLChangeUnSubscribeReq> {
    constructor() {
        super("ProtoOAv1PnLChangeUnSubscribeReq", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAv1PnLChangeUnSubscribeReq
 */
export const ProtoOAv1PnLChangeUnSubscribeReq = new ProtoOAv1PnLChangeUnSubscribeReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAv1PnLChangeUnSubscribeRes$Type extends MessageType<ProtoOAv1PnLChangeUnSubscribeRes> {
    constructor() {
        super("ProtoOAv1PnLChangeUnSubscribeRes", [
            { no: 1, name: "payloadType", kind: "enum", opt: true, T: () => ["ProtoOAPayloadType", ProtoOAPayloadType] },
            { no: 2, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAv1PnLChangeUnSubscribeRes
 */
export const ProtoOAv1PnLChangeUnSubscribeRes = new ProtoOAv1PnLChangeUnSubscribeRes$Type();
