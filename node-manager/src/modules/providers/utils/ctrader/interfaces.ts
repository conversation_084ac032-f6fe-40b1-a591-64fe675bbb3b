// @generated by protobuf-ts 2.9.4 with parameter server_grpc1,client_none,optimize_code_size
// @generated from protobuf file "OpenApiModelMessages.proto" (syntax proto2)
// tslint:disable
import { MessageType } from "@protobuf-ts/runtime";
/**
 * * Asset entity.
 *
 * @generated from protobuf message ProtoOAAsset
 */
export interface ProtoOAAsset {
    /**
     * @generated from protobuf field: int64 assetId = 1;
     */
    assetId: bigint; // The unique asset ID.
    /**
     * @generated from protobuf field: string name = 2;
     */
    name: string; // The asset name.
    /**
     * @generated from protobuf field: optional string displayName = 3;
     */
    displayName?: string; // User friendly name.
    /**
     * @generated from protobuf field: optional int32 digits = 4;
     */
    digits?: number; // Precision of the asset.
}
/**
 * * Trading symbol entity.
 *
 * @generated from protobuf message ProtoOASymbol
 */
export interface ProtoOASymbol {
    /**
     * @generated from protobuf field: int64 symbolId = 1;
     */
    symbolId: bigint; // The unique identifier of the symbol in specific server environment within cTrader platform. Different servers have different IDs.
    /**
     * @generated from protobuf field: int32 digits = 2;
     */
    digits: number; // Number of price digits to be displayed.
    /**
     * @generated from protobuf field: int32 pipPosition = 3;
     */
    pipPosition: number; // Pip position on digits.
    /**
     * @generated from protobuf field: optional bool enableShortSelling = 4;
     */
    enableShortSelling?: boolean; // If TRUE then the short selling with the symbol is enabled.
    /**
     * @generated from protobuf field: optional bool guaranteedStopLoss = 5;
     */
    guaranteedStopLoss?: boolean; // If TRUE then setting of guaranteedStopLoss is available for limited risk accounts.
    /**
     * @generated from protobuf field: optional ProtoOADayOfWeek swapRollover3Days = 6;
     */
    swapRollover3Days?: ProtoOADayOfWeek; // Day of the week when SWAP charge amount will be tripled. Doesn't impact Rollover Commission.
    /**
     * @generated from protobuf field: optional double swapLong = 7;
     */
    swapLong?: number; // SWAP charge for long positions.
    /**
     * @generated from protobuf field: optional double swapShort = 8;
     */
    swapShort?: number; // SWAP charge for short positions.
    /**
     * @generated from protobuf field: optional int64 maxVolume = 9;
     */
    maxVolume?: bigint; // Maximum allowed volume in cents for an order with a symbol.
    /**
     * @generated from protobuf field: optional int64 minVolume = 10;
     */
    minVolume?: bigint; // Minimum allowed volume in cents for an order with a symbol.
    /**
     * @generated from protobuf field: optional int64 stepVolume = 11;
     */
    stepVolume?: bigint; // Step of the volume in cents for an order.
    /**
     * @generated from protobuf field: optional uint64 maxExposure = 12;
     */
    maxExposure?: bigint; // Value of max exposure per symbol, per account. Blocks execution if breached.
    /**
     * @generated from protobuf field: repeated ProtoOAInterval schedule = 13;
     */
    schedule: ProtoOAInterval[]; // Symbol trading interval, specified in seconds starting from SUNDAY 00:00 in specified time zone.
    /**
     * @deprecated
     * @generated from protobuf field: optional int64 commission = 14 [deprecated = true];
     */
    commission?: bigint; // Commission base amount. Total commission depends on commissionType. Use preciseTradingCommissionRate.
    /**
     * @generated from protobuf field: optional ProtoOACommissionType commissionType = 15;
     */
    commissionType?: ProtoOACommissionType; // Commission type. See ProtoOACommissionType for details.
    /**
     * @generated from protobuf field: optional uint32 slDistance = 16;
     */
    slDistance?: number; // Minimum allowed distance between stop loss and current market price.
    /**
     * @generated from protobuf field: optional uint32 tpDistance = 17;
     */
    tpDistance?: number; // Minimum allowed distance between take profit and current market price.
    /**
     * @generated from protobuf field: optional uint32 gslDistance = 18;
     */
    gslDistance?: number; // Minimum allowed distance between guaranteed stop loss and current market price.
    /**
     * @generated from protobuf field: optional int64 gslCharge = 19;
     */
    gslCharge?: bigint; // Guaranteed stop loss fee.
    /**
     * @generated from protobuf field: optional ProtoOASymbolDistanceType distanceSetIn = 20;
     */
    distanceSetIn?: ProtoOASymbolDistanceType; // Unit of distance measure for slDistance, tpDistance, gslDistance.
    /**
     * @deprecated
     * @generated from protobuf field: optional int64 minCommission = 21 [deprecated = true];
     */
    minCommission?: bigint; // Minimum commission amount per trade. Use preciseMinCommission.
    /**
     * @generated from protobuf field: optional ProtoOAMinCommissionType minCommissionType = 22;
     */
    minCommissionType?: ProtoOAMinCommissionType; // Minimum commission Type. See ProtoOAMinCommissionType for details.
    /**
     * @generated from protobuf field: optional string minCommissionAsset = 23;
     */
    minCommissionAsset?: string; // Currency for minimum commission. (USD or quote currency).
    /**
     * @generated from protobuf field: optional int64 rolloverCommission = 24;
     */
    rolloverCommission?: bigint; // Administrative Fee, charged instead of Swaps if the Account is marked as a "Shariah Compliant (Swap Free)". The Administrative Fee is charged daily as USD per current open volume of Position in lots. The Account charged in the Deposit currency.
    /**
     * @generated from protobuf field: optional int32 skipRolloverDays = 25;
     */
    skipRolloverDays?: number; // Initial period before the first rolloverCommission will be charged on the account.
    /**
     * @generated from protobuf field: optional string scheduleTimeZone = 26;
     */
    scheduleTimeZone?: string; // Time zone for the symbol trading intervals.
    /**
     * @generated from protobuf field: optional ProtoOATradingMode tradingMode = 27;
     */
    tradingMode?: ProtoOATradingMode; // Rules for trading with the symbol. See ProtoOATradingMode for details.
    /**
     * @generated from protobuf field: optional ProtoOADayOfWeek rolloverCommission3Days = 28;
     */
    rolloverCommission3Days?: ProtoOADayOfWeek; // Day of the week (in UTC) when Administrative Fee charge amount will be tripled. Applied only if RolloverChargePeriod = 0 or 1.
    /**
     * @generated from protobuf field: optional ProtoOASwapCalculationType swapCalculationType = 29;
     */
    swapCalculationType?: ProtoOASwapCalculationType; // Specifies type of SWAP computation as PIPS (0) or PERCENTAGE (1, annual, in percent).
    /**
     * @generated from protobuf field: optional int64 lotSize = 30;
     */
    lotSize?: bigint; // Lot size of the Symbol (in cents).
    /**
     * @generated from protobuf field: optional int64 preciseTradingCommissionRate = 31;
     */
    preciseTradingCommissionRate?: bigint; // Commission base amount. Total commission depends on commissionType: for non-percentage types it is multiplied by 10^8, for percentage of value commission type it is multiplied by 10^5.
    /**
     * @generated from protobuf field: optional int64 preciseMinCommission = 32;
     */
    preciseMinCommission?: bigint; // Minimum commission amount per trade multiplied by 10^8.
    /**
     * @generated from protobuf field: repeated ProtoOAHoliday holiday = 33;
     */
    holiday: ProtoOAHoliday[]; // List of holidays for this symbol specified by broker.
    /**
     * @generated from protobuf field: optional int32 pnlConversionFeeRate = 34;
     */
    pnlConversionFeeRate?: number; // Percentage (1 = 0.01%) of the realized Gross Profit, which will be paid by the Trader for any trade if the Quote Asset of the traded Symbol is not matched with the Deposit Asset.
    /**
     * @generated from protobuf field: optional int64 leverageId = 35;
     */
    leverageId?: bigint; // The unique identifier of dynamic leverage entity. https://help.ctrader.com/ctrader/trading/dynamic-leverage
    /**
     * @generated from protobuf field: optional int32 swapPeriod = 36;
     */
    swapPeriod?: number; // Period of charging swaps in hours. 24 means swaps will be charged 1 time per day, 12 - every 12 hours, 8 - every 8 hours, etc.
    /**
     * @generated from protobuf field: optional int32 swapTime = 37;
     */
    swapTime?: number; // Time in minutes from 00:00 (UTC) when intraday swaps are charged for the first time.
    /**
     * @generated from protobuf field: optional int32 skipSWAPPeriods = 38;
     */
    skipSWAPPeriods?: number; // Count of swapPeriods before the first SWAP charge.
    /**
     * @generated from protobuf field: optional bool chargeSwapAtWeekends = 39;
     */
    chargeSwapAtWeekends?: boolean; // If enabled, SWAP will be charged for all days of the week, including Saturday and Sunday.
    /**
     * @generated from protobuf field: optional string measurementUnits = 40;
     */
    measurementUnits?: string; // Specifies the units in which the base Asset of the Symbol is denominated.
}
/**
 * * Lightweight symbol entity.
 *
 * @generated from protobuf message ProtoOALightSymbol
 */
export interface ProtoOALightSymbol {
    /**
     * @generated from protobuf field: int64 symbolId = 1;
     */
    symbolId: bigint; // The unique identifier of the symbol in specific server environment within cTrader platform. Different brokers might have different IDs.
    /**
     * @generated from protobuf field: optional string symbolName = 2;
     */
    symbolName?: string; // Name of the symbol (e.g. EUR/USD).
    /**
     * @generated from protobuf field: optional bool enabled = 3;
     */
    enabled?: boolean; // If TRUE then symbol is visible for traders.
    /**
     * @generated from protobuf field: optional int64 baseAssetId = 4;
     */
    baseAssetId?: bigint; // Base asset.
    /**
     * @generated from protobuf field: optional int64 quoteAssetId = 5;
     */
    quoteAssetId?: bigint; // Quote asset.
    /**
     * @generated from protobuf field: optional int64 symbolCategoryId = 6;
     */
    symbolCategoryId?: bigint; // Id of the symbol category used for symbols grouping.
    /**
     * @generated from protobuf field: optional string description = 7;
     */
    description?: string;
    /**
     * @generated from protobuf field: optional double sortingNumber = 8;
     */
    sortingNumber?: number; // The number used for sorting Symbols in the UI (lowest number should appear at the top).
}
/**
 * @generated from protobuf message ProtoOAArchivedSymbol
 */
export interface ProtoOAArchivedSymbol {
    /**
     * @generated from protobuf field: int64 symbolId = 1;
     */
    symbolId: bigint; // The unique identifier of the symbol in specific server environment within cTrader platform. Different brokers might have different IDs.
    /**
     * @generated from protobuf field: string name = 2;
     */
    name: string; // Name of the symbol (e.g. EUR/USD).
    /**
     * @generated from protobuf field: int64 utcLastUpdateTimestamp = 3;
     */
    utcLastUpdateTimestamp: bigint; // The Unix time in milliseconds of the last update of the symbol.
    /**
     * @generated from protobuf field: optional string description = 4;
     */
    description?: string; // Description of the symbol.
}
/**
 * * Symbol category entity.
 *
 * @generated from protobuf message ProtoOASymbolCategory
 */
export interface ProtoOASymbolCategory {
    /**
     * @generated from protobuf field: int64 id = 1;
     */
    id: bigint; // The unique identifier of the symbol category.
    /**
     * @generated from protobuf field: int64 assetClassId = 2;
     */
    assetClassId: bigint; // Link to the asset class. One asset class can have many symbol categories.
    /**
     * @generated from protobuf field: string name = 3;
     */
    name: string; // Category name.
    /**
     * @generated from protobuf field: optional double sortingNumber = 4;
     */
    sortingNumber?: number; // The number used for sorting Symbol Categories in the UI (lowest number should appear at the top).
}
/**
 * * Symbol trading session entity.
 *
 * @generated from protobuf message ProtoOAInterval
 */
export interface ProtoOAInterval {
    /**
     * @generated from protobuf field: uint32 startSecond = 3;
     */
    startSecond: number; // Interval start, specified in seconds starting from SUNDAY 00:00 in specified time zone (inclusive to the interval).
    /**
     * @generated from protobuf field: uint32 endSecond = 4;
     */
    endSecond: number; // Interval end, specified in seconds starting from SUNDAY 00:00 in specified time zone (exclusive from the interval).
}
/**
 * * Trading account entity.
 *
 * @generated from protobuf message ProtoOATrader
 */
export interface ProtoOATrader {
    /**
     * @generated from protobuf field: int64 ctidTraderAccountId = 1;
     */
    ctidTraderAccountId: bigint; // The unique Trader's Account ID used to match the responses to the Trader's Account.
    /**
     * @generated from protobuf field: int64 balance = 2;
     */
    balance: bigint; // Current account balance.
    /**
     * @generated from protobuf field: optional int64 balanceVersion = 3;
     */
    balanceVersion?: bigint; // Balance version used to identify the final balance. Increments each time when the trader's account balance is changed.
    /**
     * @generated from protobuf field: optional int64 managerBonus = 4;
     */
    managerBonus?: bigint; // Amount of broker's bonus allocated to the account.
    /**
     * @generated from protobuf field: optional int64 ibBonus = 5;
     */
    ibBonus?: bigint; // Amount of introducing broker bonus allocated to the account.
    /**
     * @generated from protobuf field: optional int64 nonWithdrawableBonus = 6;
     */
    nonWithdrawableBonus?: bigint; // Broker's bonus that cannot be withdrew from the account as cash.
    /**
     * @generated from protobuf field: optional ProtoOAAccessRights accessRights = 7;
     */
    accessRights?: ProtoOAAccessRights; // Access rights that an owner has to the account in cTrader platform. See ProtoOAAccessRights for details.
    /**
     * @generated from protobuf field: int64 depositAssetId = 8;
     */
    depositAssetId: bigint; // Deposit currency of the account.
    /**
     * @generated from protobuf field: optional bool swapFree = 9;
     */
    swapFree?: boolean; // If TRUE then account is Shariah compliant.
    /**
     * @generated from protobuf field: optional uint32 leverageInCents = 10;
     */
    leverageInCents?: number; // Account leverage (e.g. If leverage = 1:50 then value = 5000).
    /**
     * @generated from protobuf field: optional ProtoOATotalMarginCalculationType totalMarginCalculationType = 11;
     */
    totalMarginCalculationType?: ProtoOATotalMarginCalculationType; // Margin computation type for the account (MAX, SUM, NET).
    /**
     * @generated from protobuf field: optional uint32 maxLeverage = 12;
     */
    maxLeverage?: number; // Maximum allowed leverage for the account. Used as validation when a Trader can change leverage value.
    /**
     * @deprecated
     * @generated from protobuf field: optional bool frenchRisk = 13 [deprecated = true];
     */
    frenchRisk?: boolean; // If TRUE then account is AMF compliant. Use isLimitedRisk and limitedRiskMarginCalculationStrategy.
    /**
     * @generated from protobuf field: optional int64 traderLogin = 14;
     */
    traderLogin?: bigint; // ID of the account that is unique per server (Broker).
    /**
     * @generated from protobuf field: optional ProtoOAAccountType accountType = 15;
     */
    accountType?: ProtoOAAccountType; // Account type: HEDGED, NETTED, etc.
    /**
     * @generated from protobuf field: optional string brokerName = 16;
     */
    brokerName?: string; // Some whitelabel assigned to trader by broker at the moment of account creation.
    /**
     * @generated from protobuf field: optional int64 registrationTimestamp = 17;
     */
    registrationTimestamp?: bigint; // The Unix timestamp in milliseconds of the account registration. Should be used as minimal date in historical data requests.
    /**
     * @generated from protobuf field: optional bool isLimitedRisk = 18;
     */
    isLimitedRisk?: boolean; // If TRUE then account is compliant to use specific margin calculation strategy. Such accounts are require to have guaranteed stop loss on all positions.
    /**
     * @generated from protobuf field: optional ProtoOALimitedRiskMarginCalculationStrategy limitedRiskMarginCalculationStrategy = 19;
     */
    limitedRiskMarginCalculationStrategy?: ProtoOALimitedRiskMarginCalculationStrategy; // Special strategy used in margin calculations for this account (if account isLimitedRisk).
    /**
     * @generated from protobuf field: optional uint32 moneyDigits = 20;
     */
    moneyDigits?: number; // Specifies the exponent of the monetary values. E.g. moneyDigits = 8 must be interpret as business value multiplied by 10^8, then real balance would be *********** / 10^8 = 100.********. Affects balance, managerBonus, ibBonus, nonWithdrawableBonus.
    /**
     * @generated from protobuf field: optional bool fairStopOut = 21;
     */
    fairStopOut?: boolean; // If TRUE - Position is fully closed on Stop Out, if FALSE - smart (partial closing) Stop Out is applied, if unspecified  - Stop Out format is determined by Broker.
    /**
     * @generated from protobuf field: optional ProtoOAStopOutStrategy stopOutStrategy = 22;
     */
    stopOutStrategy?: ProtoOAStopOutStrategy; // The Stop Out strategy that is used for this Trader. The Trader can change the value in the cTrader UI if this option is not disabled by the Broker
}
/**
 * * Trade position entity.
 *
 * @generated from protobuf message ProtoOAPosition
 */
export interface ProtoOAPosition {
    /**
     * @generated from protobuf field: int64 positionId = 1;
     */
    positionId: bigint; // The unique ID of the position. Note: trader might have two positions with the same id if positions are taken from accounts from different brokers.
    /**
     * @generated from protobuf field: ProtoOATradeData tradeData = 2;
     */
    tradeData?: ProtoOATradeData; // Position details. See ProtoOATradeData for details.
    /**
     * @generated from protobuf field: ProtoOAPositionStatus positionStatus = 3;
     */
    positionStatus: ProtoOAPositionStatus; // Current status of the position.
    /**
     * @generated from protobuf field: int64 swap = 4;
     */
    swap: bigint; // Total amount of charged swap on open position.
    /**
     * @generated from protobuf field: optional double price = 5;
     */
    price?: number; // VWAP price of the position based on all executions (orders) linked to the position.
    /**
     * @generated from protobuf field: optional double stopLoss = 6;
     */
    stopLoss?: number; // Current stop loss price.
    /**
     * @generated from protobuf field: optional double takeProfit = 7;
     */
    takeProfit?: number; // Current take profit price.
    /**
     * @generated from protobuf field: optional int64 utcLastUpdateTimestamp = 8;
     */
    utcLastUpdateTimestamp?: bigint; // The Unix time in milliseconds of the last change of the position, including amend SL/TP of the position, execution of related order, cancel or related order, etc.
    /**
     * @generated from protobuf field: optional int64 commission = 9;
     */
    commission?: bigint; // Current unrealized commission related to the position.
    /**
     * @generated from protobuf field: optional double marginRate = 10;
     */
    marginRate?: number; // Rate for used margin computation. Represented as Base/Deposit.
    /**
     * @generated from protobuf field: optional int64 mirroringCommission = 11;
     */
    mirroringCommission?: bigint; // Amount of unrealized commission related to following of strategy provider.
    /**
     * @generated from protobuf field: optional bool guaranteedStopLoss = 12;
     */
    guaranteedStopLoss?: boolean; // If TRUE then position's stop loss is guaranteedStopLoss.
    /**
     * @generated from protobuf field: optional uint64 usedMargin = 13;
     */
    usedMargin?: bigint; // Amount of margin used for the position in deposit currency.
    /**
     * @generated from protobuf field: optional ProtoOAOrderTriggerMethod stopLossTriggerMethod = 14;
     */
    stopLossTriggerMethod?: ProtoOAOrderTriggerMethod; // Stop trigger method for SL/TP of the position.
    /**
     * @generated from protobuf field: optional uint32 moneyDigits = 15;
     */
    moneyDigits?: number; // Specifies the exponent of the monetary values. E.g. moneyDigits = 8 must be interpret as business value multiplied by 10^8, then real balance would be *********** / 10^8 = 100.********. Affects swap, commission, mirroringCommission, usedMargin.
    /**
     * @generated from protobuf field: optional bool trailingStopLoss = 16;
     */
    trailingStopLoss?: boolean; // If TRUE then the Trailing Stop Loss is applied.
}
/**
 * * Position/order trading details entity.
 *
 * @generated from protobuf message ProtoOATradeData
 */
export interface ProtoOATradeData {
    /**
     * @generated from protobuf field: int64 symbolId = 1;
     */
    symbolId: bigint; // The unique identifier of the symbol in specific server environment within cTrader platform. Different brokers might have different IDs.
    /**
     * @generated from protobuf field: int64 volume = 2;
     */
    volume: bigint; // Volume in cents (e.g. 1000 in protocol means 10.00 units).
    /**
     * @generated from protobuf field: ProtoOATradeSide tradeSide = 3;
     */
    tradeSide: ProtoOATradeSide; // Buy, Sell.
    /**
     * @generated from protobuf field: optional int64 openTimestamp = 4;
     */
    openTimestamp?: bigint; // The Unix time in milliseconds when position was opened or order was created.
    /**
     * @generated from protobuf field: optional string label = 5;
     */
    label?: string; // Text label specified during order request.
    /**
     * @generated from protobuf field: optional bool guaranteedStopLoss = 6;
     */
    guaranteedStopLoss?: boolean; // If TRUE then position/order stop loss is guaranteedStopLoss.
    /**
     * @generated from protobuf field: optional string comment = 7;
     */
    comment?: string; // User-specified comment.
    /**
     * @generated from protobuf field: optional string measurementUnits = 8;
     */
    measurementUnits?: string; // Specifies the units in which the Symbol is denominated.
    /**
     * @generated from protobuf field: optional uint64 closeTimestamp = 9;
     */
    closeTimestamp?: bigint; // The Unix time in milliseconds when a Position was closed
}
/**
 * * Trade order entity.
 *
 * @generated from protobuf message ProtoOAOrder
 */
export interface ProtoOAOrder {
    /**
     * @generated from protobuf field: int64 orderId = 1;
     */
    orderId: bigint; // The unique ID of the order. Note: trader might have two orders with the same id if orders are taken from accounts from different brokers.
    /**
     * @generated from protobuf field: ProtoOATradeData tradeData = 2;
     */
    tradeData?: ProtoOATradeData; // Detailed trader data.
    /**
     * @generated from protobuf field: ProtoOAOrderType orderType = 3;
     */
    orderType: ProtoOAOrderType; // Order type.
    /**
     * @generated from protobuf field: ProtoOAOrderStatus orderStatus = 4;
     */
    orderStatus: ProtoOAOrderStatus; // Order status.
    /**
     * @generated from protobuf field: optional int64 expirationTimestamp = 6;
     */
    expirationTimestamp?: bigint; // The Unix time in milliseconds of expiration if the order has time in force GTD.
    /**
     * @generated from protobuf field: optional double executionPrice = 7;
     */
    executionPrice?: number; // Price at which an order was executed. For order with FILLED status.
    /**
     * @generated from protobuf field: optional int64 executedVolume = 8;
     */
    executedVolume?: bigint; // Part of the volume that was filled in cents (e.g. 1000 in protocol means 10.00 units).
    /**
     * @generated from protobuf field: optional int64 utcLastUpdateTimestamp = 9;
     */
    utcLastUpdateTimestamp?: bigint; // The Unix time in milliseconds of the last update of the order.
    /**
     * @generated from protobuf field: optional double baseSlippagePrice = 10;
     */
    baseSlippagePrice?: number; // Used for Market Range order with combination of slippageInPoints to specify price range were order can be executed.
    /**
     * @generated from protobuf field: optional int64 slippageInPoints = 11;
     */
    slippageInPoints?: bigint; // Used for Market Range and STOP_LIMIT orders to to specify price range were order can be executed.
    /**
     * @generated from protobuf field: optional bool closingOrder = 12;
     */
    closingOrder?: boolean; // If TRUE then the order is closing part of whole position. Must have specified positionId.
    /**
     * @generated from protobuf field: optional double limitPrice = 13;
     */
    limitPrice?: number; // Valid only for LIMIT orders.
    /**
     * @generated from protobuf field: optional double stopPrice = 14;
     */
    stopPrice?: number; // Valid only for STOP and STOP_LIMIT orders.
    /**
     * @generated from protobuf field: optional double stopLoss = 15;
     */
    stopLoss?: number; // Absolute stopLoss price.
    /**
     * @generated from protobuf field: optional double takeProfit = 16;
     */
    takeProfit?: number; // Absolute takeProfit price.
    /**
     * @generated from protobuf field: optional string clientOrderId = 17;
     */
    clientOrderId?: string; // Optional ClientOrderId. Max Length = 50 chars.
    /**
     * @generated from protobuf field: optional ProtoOATimeInForce timeInForce = 18;
     */
    timeInForce?: ProtoOATimeInForce; // Order's time in force. Depends on order type.
    /**
     * @generated from protobuf field: optional int64 positionId = 19;
     */
    positionId?: bigint; // ID of the position linked to the order (e.g. closing order, order that increase volume of a specific position, etc.).
    /**
     * @generated from protobuf field: optional int64 relativeStopLoss = 20;
     */
    relativeStopLoss?: bigint; // Relative stopLoss that can be specified instead of absolute as one. Specified in 1/100000 of unit of a price. (e.g. 123000 in protocol means 1.23, 53423782 means 534.23782) For BUY stopLoss = entryPrice - relativeStopLoss, for SELL stopLoss = entryPrice + relativeStopLoss.
    /**
     * @generated from protobuf field: optional int64 relativeTakeProfit = 21;
     */
    relativeTakeProfit?: bigint; // Relative takeProfit that can be specified instead of absolute one. Specified in 1/100000 of unit of a price. (e.g. 123000 in protocol means 1.23, 53423782 means 534.23782) ForBUY takeProfit = entryPrice + relativeTakeProfit, for SELL takeProfit = entryPrice - relativeTakeProfit.
    /**
     * @generated from protobuf field: optional bool isStopOut = 22;
     */
    isStopOut?: boolean; // If TRUE then order was stopped out from server side.
    /**
     * @generated from protobuf field: optional bool trailingStopLoss = 23;
     */
    trailingStopLoss?: boolean; // If TRUE then order is trailingStopLoss. Valid for STOP_LOSS_TAKE_PROFIT order.
    /**
     * @generated from protobuf field: optional ProtoOAOrderTriggerMethod stopTriggerMethod = 24;
     */
    stopTriggerMethod?: ProtoOAOrderTriggerMethod; // Trigger method for the order. Valid only for STOP and STOP_LIMIT orders.
}
/**
 * * Bonus deposit/withdrawal entity.
 *
 * @generated from protobuf message ProtoOABonusDepositWithdraw
 */
export interface ProtoOABonusDepositWithdraw {
    /**
     * @generated from protobuf field: ProtoOAChangeBonusType operationType = 1;
     */
    operationType: ProtoOAChangeBonusType; // Type of the operation. Deposit/Withdrawal.
    /**
     * @generated from protobuf field: int64 bonusHistoryId = 2;
     */
    bonusHistoryId: bigint; // The unique ID of the bonus deposit/withdrawal operation.
    /**
     * @generated from protobuf field: int64 managerBonus = 3;
     */
    managerBonus: bigint; // Total amount of broker's bonus after the operation.
    /**
     * @generated from protobuf field: int64 managerDelta = 4;
     */
    managerDelta: bigint; // Amount of bonus deposited/withdrew by manager.
    /**
     * @generated from protobuf field: int64 ibBonus = 5;
     */
    ibBonus: bigint; // Total amount of introducing broker's bonus after the operation.
    /**
     * @generated from protobuf field: int64 ibDelta = 6;
     */
    ibDelta: bigint; // Amount of bonus deposited/withdrew by introducing broker.
    /**
     * @generated from protobuf field: int64 changeBonusTimestamp = 7;
     */
    changeBonusTimestamp: bigint; // The Unix time in milliseconds when the bonus operation was executed.
    /**
     * @generated from protobuf field: optional string externalNote = 8;
     */
    externalNote?: string; // Note added to operation. Visible to the trader.
    /**
     * @generated from protobuf field: optional int64 introducingBrokerId = 9;
     */
    introducingBrokerId?: bigint; // ID of introducing broker who deposited/withdrew bonus.
    /**
     * @generated from protobuf field: optional uint32 moneyDigits = 10;
     */
    moneyDigits?: number; // Specifies the exponent of the monetary values. E.g. moneyDigits = 8 must be interpret as business value multiplied by 10^8, then real balance would be *********** / 10^8 = 100.********. Affects managerBonus, managerDelta, ibBonus, ibDelta.
}
/**
 * * Account deposit/withdrawal operation entity.
 *
 * @generated from protobuf message ProtoOADepositWithdraw
 */
export interface ProtoOADepositWithdraw {
    /**
     * @generated from protobuf field: ProtoOAChangeBalanceType operationType = 1;
     */
    operationType: ProtoOAChangeBalanceType; // Type of the operation. Deposit/Withdrawal.
    /**
     * @generated from protobuf field: int64 balanceHistoryId = 2;
     */
    balanceHistoryId: bigint; // The unique ID of the deposit/withdrawal operation.
    /**
     * @generated from protobuf field: int64 balance = 3;
     */
    balance: bigint; // Account balance after the operation was executed.
    /**
     * @generated from protobuf field: int64 delta = 4;
     */
    delta: bigint; // Amount of deposit/withdrawal operation.
    /**
     * @generated from protobuf field: int64 changeBalanceTimestamp = 5;
     */
    changeBalanceTimestamp: bigint; // The Unix time in milliseconds when deposit/withdrawal operation was executed.
    /**
     * @generated from protobuf field: optional string externalNote = 6;
     */
    externalNote?: string; // Note added to operation. Visible to the trader.
    /**
     * @generated from protobuf field: optional int64 balanceVersion = 7;
     */
    balanceVersion?: bigint; // Balance version used to identify the final balance. Increments each time when the trader's account balance is changed.
    /**
     * @generated from protobuf field: optional int64 equity = 8;
     */
    equity?: bigint; // Total account's equity after balance operation was executed.
    /**
     * @generated from protobuf field: optional uint32 moneyDigits = 9;
     */
    moneyDigits?: number; // Specifies the exponent of the monetary values. E.g. moneyDigits = 8 must be interpret as business value multiplied by 10^8, then real balance would be *********** / 10^8 = 100.********. Affects balance, delta, equity.
}
/**
 * * Execution entity.
 *
 * @generated from protobuf message ProtoOADeal
 */
export interface ProtoOADeal {
    /**
     * @generated from protobuf field: int64 dealId = 1;
     */
    dealId: bigint; // The unique ID of the execution deal.
    /**
     * @generated from protobuf field: int64 orderId = 2;
     */
    orderId: bigint; // Source order of the deal.
    /**
     * @generated from protobuf field: int64 positionId = 3;
     */
    positionId: bigint; // Source position of the deal.
    /**
     * @generated from protobuf field: int64 volume = 4;
     */
    volume: bigint; // Volume sent for execution, in cents.
    /**
     * @generated from protobuf field: int64 filledVolume = 5;
     */
    filledVolume: bigint; // Filled volume, in cents.
    /**
     * @generated from protobuf field: int64 symbolId = 6;
     */
    symbolId: bigint; // The unique identifier of the symbol in specific server environment within cTrader platform. Different servers have different IDs.
    /**
     * @generated from protobuf field: int64 createTimestamp = 7;
     */
    createTimestamp: bigint; // The Unix time in milliseconds when the deal was sent for execution.
    /**
     * @generated from protobuf field: int64 executionTimestamp = 8;
     */
    executionTimestamp: bigint; // The Unix time in milliseconds when the deal was executed.
    /**
     * @generated from protobuf field: optional int64 utcLastUpdateTimestamp = 9;
     */
    utcLastUpdateTimestamp?: bigint; // The Unix time in milliseconds when the deal was created, executed or rejected.
    /**
     * @generated from protobuf field: optional double executionPrice = 10;
     */
    executionPrice?: number; // Execution price.
    /**
     * @generated from protobuf field: ProtoOATradeSide tradeSide = 11;
     */
    tradeSide: ProtoOATradeSide; // Buy/Sell.
    /**
     * @generated from protobuf field: ProtoOADealStatus dealStatus = 12;
     */
    dealStatus: ProtoOADealStatus; // Status of the deal.
    /**
     * @generated from protobuf field: optional double marginRate = 13;
     */
    marginRate?: number; // Rate for used margin computation. Represented as Base/Deposit.
    /**
     * @generated from protobuf field: optional int64 commission = 14;
     */
    commission?: bigint; // Amount of trading commission associated with the deal.
    /**
     * @generated from protobuf field: optional double baseToUsdConversionRate = 15;
     */
    baseToUsdConversionRate?: number; // Base to USD conversion rate on the time of deal execution.
    /**
     * @generated from protobuf field: optional ProtoOAClosePositionDetail closePositionDetail = 16;
     */
    closePositionDetail?: ProtoOAClosePositionDetail; // Closing position detail. Valid only for closing deal.
    /**
     * @generated from protobuf field: optional uint32 moneyDigits = 17;
     */
    moneyDigits?: number; // Specifies the exponent of the monetary values. E.g. moneyDigits = 8 must be interpret as business value multiplied by 10^8, then real balance would be *********** / 10^8 = 100.********. Affects commission.
}
/**
 * * Deal details for ProtoOADealOffsetListReq.
 *
 * @generated from protobuf message ProtoOADealOffset
 */
export interface ProtoOADealOffset {
    /**
     * @generated from protobuf field: int64 dealId = 1;
     */
    dealId: bigint; // The unique ID of the execution Deal.
    /**
     * @generated from protobuf field: int64 volume = 2;
     */
    volume: bigint; // Matched volume, in cents.
    /**
     * @generated from protobuf field: optional int64 executionTimestamp = 3;
     */
    executionTimestamp?: bigint; // The Unix time in milliseconds when the offset Deal was executed.
    /**
     * @generated from protobuf field: optional double executionPrice = 4;
     */
    executionPrice?: number; //  Execution price of the offset Deal.
}
/**
 * * Trading details for closing deal.
 *
 * @generated from protobuf message ProtoOAClosePositionDetail
 */
export interface ProtoOAClosePositionDetail {
    /**
     * @generated from protobuf field: double entryPrice = 1;
     */
    entryPrice: number; // Position price at the moment of filling the closing order.
    /**
     * @generated from protobuf field: int64 grossProfit = 2;
     */
    grossProfit: bigint; // Amount of realized gross profit after closing deal execution.
    /**
     * @generated from protobuf field: int64 swap = 3;
     */
    swap: bigint; // Amount of realized swap related to closed volume.
    /**
     * @generated from protobuf field: int64 commission = 4;
     */
    commission: bigint; // Amount of realized commission related to closed volume.
    /**
     * @generated from protobuf field: int64 balance = 5;
     */
    balance: bigint; // Account balance after closing deal execution.
    /**
     * @generated from protobuf field: optional double quoteToDepositConversionRate = 6;
     */
    quoteToDepositConversionRate?: number; // Quote/Deposit currency conversion rate on the time of closing deal execution.
    /**
     * @generated from protobuf field: optional int64 closedVolume = 7;
     */
    closedVolume?: bigint; // Closed volume in cents.
    /**
     * @generated from protobuf field: optional int64 balanceVersion = 8;
     */
    balanceVersion?: bigint; // Balance version of the account related to closing deal operation.
    /**
     * @generated from protobuf field: optional uint32 moneyDigits = 9;
     */
    moneyDigits?: number; // Specifies the exponent of the monetary values. E.g. moneyDigits = 8 must be interpret as business value multiplied by 10^8, then real balance would be *********** / 10^8 = 100.********. Affects grossProfit, swap, commission, balance, pnlConversionFee.
    /**
     * @generated from protobuf field: optional int64 pnlConversionFee = 10;
     */
    pnlConversionFee?: bigint; // Fee for conversion applied to the Deal in account's ccy when trader symbol's quote asset id <> ProtoOATrader.depositAssetId.
}
/**
 * * Historical Trendbar entity.
 *
 * @generated from protobuf message ProtoOATrendbar
 */
export interface ProtoOATrendbar {
    /**
     * @generated from protobuf field: int64 volume = 3;
     */
    volume: bigint; // Bar volume in ticks.
    /**
     * @generated from protobuf field: optional ProtoOATrendbarPeriod period = 4;
     */
    period?: ProtoOATrendbarPeriod; // Bar period.
    /**
     * @generated from protobuf field: optional int64 low = 5;
     */
    low?: bigint; // Low price of the bar.
    /**
     * @generated from protobuf field: optional uint64 deltaOpen = 6;
     */
    deltaOpen?: bigint; // Delta between open and low price. open = low + deltaOpen.
    /**
     * @generated from protobuf field: optional uint64 deltaClose = 7;
     */
    deltaClose?: bigint; // Delta between close and low price. close = low + deltaClose.
    /**
     * @generated from protobuf field: optional uint64 deltaHigh = 8;
     */
    deltaHigh?: bigint; // Delta between high and low price. high = low + deltaHigh.
    /**
     * @generated from protobuf field: optional uint32 utcTimestampInMinutes = 9;
     */
    utcTimestampInMinutes?: number; // The Unix time in minutes of the bar, equal to the timestamp of the open tick.
}
/**
 * * Expected margin computation entity.
 *
 * @generated from protobuf message ProtoOAExpectedMargin
 */
export interface ProtoOAExpectedMargin {
    /**
     * @generated from protobuf field: int64 volume = 1;
     */
    volume: bigint; // Volume in cents used for computation of expected margin.
    /**
     * @generated from protobuf field: int64 buyMargin = 2;
     */
    buyMargin: bigint; // Buy margin amount.
    /**
     * @generated from protobuf field: int64 sellMargin = 3;
     */
    sellMargin: bigint; // Sell margin amount.
}
/**
 * * Historical tick data type.
 *
 * @generated from protobuf message ProtoOATickData
 */
export interface ProtoOATickData {
    /**
     * @generated from protobuf field: int64 timestamp = 1;
     */
    timestamp: bigint; // The Unix time in milliseconds of the tick. See ProtoOAGetTickDataRes.tickData for details.
    /**
     * @generated from protobuf field: int64 tick = 2;
     */
    tick: bigint; // Tick price.
}
/**
 * * Trader profile entity. Empty due to GDPR.
 *
 * @generated from protobuf message ProtoOACtidProfile
 */
export interface ProtoOACtidProfile {
    /**
     * @generated from protobuf field: int64 userId = 1;
     */
    userId: bigint;
}
/**
 * * Trader account entity.
 *
 * @generated from protobuf message ProtoOACtidTraderAccount
 */
export interface ProtoOACtidTraderAccount {
    /**
     * @generated from protobuf field: uint64 ctidTraderAccountId = 1;
     */
    ctidTraderAccountId: bigint; // Unique identifier of the trader's account. Used to match responses to trader's accounts.cTrader platform. Different brokers might have different ids
    /**
     * @generated from protobuf field: optional bool isLive = 2;
     */
    isLive?: boolean; // If TRUE then the account is belong to Live environment and live host must be used to authorize it
    /**
     * @generated from protobuf field: optional int64 traderLogin = 3;
     */
    traderLogin?: bigint; // TraderLogin for a specific account. Value is displayed on Client App UI
    /**
     * @generated from protobuf field: optional int64 lastClosingDealTimestamp = 4;
     */
    lastClosingDealTimestamp?: bigint; // The Unix time in milliseconds of the last ProtoOAClosePositionDetail happened to this account.
    /**
     * @generated from protobuf field: optional int64 lastBalanceUpdateTimestamp = 5;
     */
    lastBalanceUpdateTimestamp?: bigint; // The Unix time in milliseconds of the last ProtoOADepositWithdraw happened to this account.
    /**
     * @generated from protobuf field: optional string brokerTitleShort = 6;
     */
    brokerTitleShort?: string; // The name of the broker to which the account belongs to. Shortened to be displayed in the UI.
}
/**
 * * Asset class entity.
 *
 * @generated from protobuf message ProtoOAAssetClass
 */
export interface ProtoOAAssetClass {
    /**
     * @generated from protobuf field: optional int64 id = 1;
     */
    id?: bigint; // Unique asset ID.
    /**
     * @generated from protobuf field: optional string name = 2;
     */
    name?: string; // Asset class name.
    /**
     * @generated from protobuf field: optional double sortingNumber = 3;
     */
    sortingNumber?: number; // The number used for sorting Asset Classes in the UI (lowest number should appear at the top).
}
/**
 * * Depth of market entity.
 *
 * @generated from protobuf message ProtoOADepthQuote
 */
export interface ProtoOADepthQuote {
    /**
     * @generated from protobuf field: uint64 id = 1;
     */
    id: bigint; // Quote ID.
    /**
     * @generated from protobuf field: uint64 size = 3;
     */
    size: bigint; // Quote size in cents.
    /**
     * @generated from protobuf field: optional uint64 bid = 4;
     */
    bid?: bigint; // Bid price for bid quotes.
    /**
     * @generated from protobuf field: optional uint64 ask = 5;
     */
    ask?: bigint; // Ask price for ask quotes.
}
/**
 * * Margin call entity, specifies threshold for exact margin call type. Only 3 instances of margin calls are supported, identified by marginCallType. See ProtoOANotificationType for details.
 *
 * @generated from protobuf message ProtoOAMarginCall
 */
export interface ProtoOAMarginCall {
    /**
     * @generated from protobuf field: ProtoOANotificationType marginCallType = 1;
     */
    marginCallType: ProtoOANotificationType; // Type of margin call. All margin calls are similar, only difference is in marginLevelThreshold.
    /**
     * @generated from protobuf field: double marginLevelThreshold = 2;
     */
    marginLevelThreshold: number; // Margin level threshold for margin call.
    /**
     * @generated from protobuf field: optional int64 utcLastUpdateTimestamp = 3;
     */
    utcLastUpdateTimestamp?: bigint; // The Unix time in milliseconds of the last update of the margin call.
}
/**
 * @generated from protobuf message ProtoOAHoliday
 */
export interface ProtoOAHoliday {
    /**
     * @generated from protobuf field: int64 holidayId = 1;
     */
    holidayId: bigint; // Unique ID of holiday.
    /**
     * @generated from protobuf field: string name = 2;
     */
    name: string; // Name of holiday.
    /**
     * @generated from protobuf field: optional string description = 3;
     */
    description?: string; // Description of holiday.
    /**
     * @generated from protobuf field: string scheduleTimeZone = 4;
     */
    scheduleTimeZone: string; // Timezone used for holiday.
    /**
     * @generated from protobuf field: int64 holidayDate = 5;
     */
    holidayDate: bigint; // Amount of days from 1st Jan 1970, multiply it by 86400000 to get Unix time in milliseconds.
    /**
     * @generated from protobuf field: bool isRecurring = 6;
     */
    isRecurring: boolean; // If TRUE, then the holiday happens each year.
    /**
     * @generated from protobuf field: optional int32 startSecond = 7;
     */
    startSecond?: number; // Amount of seconds from 00:00:00 of the holiday day when holiday actually starts.
    /**
     * @generated from protobuf field: optional int32 endSecond = 8;
     */
    endSecond?: number; // Amount of seconds from 00:00:00 of the holiday day when holiday actually finishes.
}
/**
 * @generated from protobuf message ProtoOADynamicLeverage
 */
export interface ProtoOADynamicLeverage {
    /**
     * @generated from protobuf field: int64 leverageId = 1;
     */
    leverageId: bigint; // Unique ID of dynamic leverage.
    /**
     * @generated from protobuf field: repeated ProtoOADynamicLeverageTier tiers = 2;
     */
    tiers: ProtoOADynamicLeverageTier[]; // Tiers sorted by volume. Last tier's leverage is applied also to volume above specified.
}
/**
 * @generated from protobuf message ProtoOADynamicLeverageTier
 */
export interface ProtoOADynamicLeverageTier {
    /**
     * @generated from protobuf field: int64 volume = 1;
     */
    volume: bigint; // Max USD volume (in cents) of the Open Position (per side) to apply specified leverage. Last tier's leverage is applied also to volume above specified.
    /**
     * @generated from protobuf field: int32 leverage = 2;
     */
    leverage: number; // Applied leverage.
}
/**
 * @generated from protobuf message ProtoOAPositionUnrealizedPnL
 */
export interface ProtoOAPositionUnrealizedPnL {
    /**
     * @generated from protobuf field: int64 positionId = 1;
     */
    positionId: bigint; // The position ID.
    /**
     * @generated from protobuf field: int64 grossUnrealizedPnL = 2;
     */
    grossUnrealizedPnL: bigint; // The gross unrealized PnL of the position denoted in the account deposit currency.
    /**
     * @generated from protobuf field: int64 netUnrealizedPnL = 3;
     */
    netUnrealizedPnL: bigint; // The net unrealized PnL of the position denoted in the account deposit currency. It does not include potential closing commission.
}
/**
 * @generated from protobuf enum ProtoOAPayloadType
 */
export enum ProtoOAPayloadType {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * @generated from protobuf enum value: PROTO_OA_APPLICATION_AUTH_REQ = 2100;
     */
    PROTO_OA_APPLICATION_AUTH_REQ = 2100,
    /**
     * @generated from protobuf enum value: PROTO_OA_APPLICATION_AUTH_RES = 2101;
     */
    PROTO_OA_APPLICATION_AUTH_RES = 2101,
    /**
     * @generated from protobuf enum value: PROTO_OA_ACCOUNT_AUTH_REQ = 2102;
     */
    PROTO_OA_ACCOUNT_AUTH_REQ = 2102,
    /**
     * @generated from protobuf enum value: PROTO_OA_ACCOUNT_AUTH_RES = 2103;
     */
    PROTO_OA_ACCOUNT_AUTH_RES = 2103,
    /**
     * @generated from protobuf enum value: PROTO_OA_VERSION_REQ = 2104;
     */
    PROTO_OA_VERSION_REQ = 2104,
    /**
     * @generated from protobuf enum value: PROTO_OA_VERSION_RES = 2105;
     */
    PROTO_OA_VERSION_RES = 2105,
    /**
     * @generated from protobuf enum value: PROTO_OA_NEW_ORDER_REQ = 2106;
     */
    PROTO_OA_NEW_ORDER_REQ = 2106,
    /**
     * @generated from protobuf enum value: PROTO_OA_TRAILING_SL_CHANGED_EVENT = 2107;
     */
    PROTO_OA_TRAILING_SL_CHANGED_EVENT = 2107,
    /**
     * @generated from protobuf enum value: PROTO_OA_CANCEL_ORDER_REQ = 2108;
     */
    PROTO_OA_CANCEL_ORDER_REQ = 2108,
    /**
     * @generated from protobuf enum value: PROTO_OA_AMEND_ORDER_REQ = 2109;
     */
    PROTO_OA_AMEND_ORDER_REQ = 2109,
    /**
     * @generated from protobuf enum value: PROTO_OA_AMEND_POSITION_SLTP_REQ = 2110;
     */
    PROTO_OA_AMEND_POSITION_SLTP_REQ = 2110,
    /**
     * @generated from protobuf enum value: PROTO_OA_CLOSE_POSITION_REQ = 2111;
     */
    PROTO_OA_CLOSE_POSITION_REQ = 2111,
    /**
     * @generated from protobuf enum value: PROTO_OA_ASSET_LIST_REQ = 2112;
     */
    PROTO_OA_ASSET_LIST_REQ = 2112,
    /**
     * @generated from protobuf enum value: PROTO_OA_ASSET_LIST_RES = 2113;
     */
    PROTO_OA_ASSET_LIST_RES = 2113,
    /**
     * @generated from protobuf enum value: PROTO_OA_SYMBOLS_LIST_REQ = 2114;
     */
    PROTO_OA_SYMBOLS_LIST_REQ = 2114,
    /**
     * @generated from protobuf enum value: PROTO_OA_SYMBOLS_LIST_RES = 2115;
     */
    PROTO_OA_SYMBOLS_LIST_RES = 2115,
    /**
     * @generated from protobuf enum value: PROTO_OA_SYMBOL_BY_ID_REQ = 2116;
     */
    PROTO_OA_SYMBOL_BY_ID_REQ = 2116,
    /**
     * @generated from protobuf enum value: PROTO_OA_SYMBOL_BY_ID_RES = 2117;
     */
    PROTO_OA_SYMBOL_BY_ID_RES = 2117,
    /**
     * @generated from protobuf enum value: PROTO_OA_SYMBOLS_FOR_CONVERSION_REQ = 2118;
     */
    PROTO_OA_SYMBOLS_FOR_CONVERSION_REQ = 2118,
    /**
     * @generated from protobuf enum value: PROTO_OA_SYMBOLS_FOR_CONVERSION_RES = 2119;
     */
    PROTO_OA_SYMBOLS_FOR_CONVERSION_RES = 2119,
    /**
     * @generated from protobuf enum value: PROTO_OA_SYMBOL_CHANGED_EVENT = 2120;
     */
    PROTO_OA_SYMBOL_CHANGED_EVENT = 2120,
    /**
     * @generated from protobuf enum value: PROTO_OA_TRADER_REQ = 2121;
     */
    PROTO_OA_TRADER_REQ = 2121,
    /**
     * @generated from protobuf enum value: PROTO_OA_TRADER_RES = 2122;
     */
    PROTO_OA_TRADER_RES = 2122,
    /**
     * @generated from protobuf enum value: PROTO_OA_TRADER_UPDATE_EVENT = 2123;
     */
    PROTO_OA_TRADER_UPDATE_EVENT = 2123,
    /**
     * @generated from protobuf enum value: PROTO_OA_RECONCILE_REQ = 2124;
     */
    PROTO_OA_RECONCILE_REQ = 2124,
    /**
     * @generated from protobuf enum value: PROTO_OA_RECONCILE_RES = 2125;
     */
    PROTO_OA_RECONCILE_RES = 2125,
    /**
     * @generated from protobuf enum value: PROTO_OA_EXECUTION_EVENT = 2126;
     */
    PROTO_OA_EXECUTION_EVENT = 2126,
    /**
     * @generated from protobuf enum value: PROTO_OA_SUBSCRIBE_SPOTS_REQ = 2127;
     */
    PROTO_OA_SUBSCRIBE_SPOTS_REQ = 2127,
    /**
     * @generated from protobuf enum value: PROTO_OA_SUBSCRIBE_SPOTS_RES = 2128;
     */
    PROTO_OA_SUBSCRIBE_SPOTS_RES = 2128,
    /**
     * @generated from protobuf enum value: PROTO_OA_UNSUBSCRIBE_SPOTS_REQ = 2129;
     */
    PROTO_OA_UNSUBSCRIBE_SPOTS_REQ = 2129,
    /**
     * @generated from protobuf enum value: PROTO_OA_UNSUBSCRIBE_SPOTS_RES = 2130;
     */
    PROTO_OA_UNSUBSCRIBE_SPOTS_RES = 2130,
    /**
     * @generated from protobuf enum value: PROTO_OA_SPOT_EVENT = 2131;
     */
    PROTO_OA_SPOT_EVENT = 2131,
    /**
     * @generated from protobuf enum value: PROTO_OA_ORDER_ERROR_EVENT = 2132;
     */
    PROTO_OA_ORDER_ERROR_EVENT = 2132,
    /**
     * @generated from protobuf enum value: PROTO_OA_DEAL_LIST_REQ = 2133;
     */
    PROTO_OA_DEAL_LIST_REQ = 2133,
    /**
     * @generated from protobuf enum value: PROTO_OA_DEAL_LIST_RES = 2134;
     */
    PROTO_OA_DEAL_LIST_RES = 2134,
    /**
     * @generated from protobuf enum value: PROTO_OA_SUBSCRIBE_LIVE_TRENDBAR_REQ = 2135;
     */
    PROTO_OA_SUBSCRIBE_LIVE_TRENDBAR_REQ = 2135,
    /**
     * @generated from protobuf enum value: PROTO_OA_UNSUBSCRIBE_LIVE_TRENDBAR_REQ = 2136;
     */
    PROTO_OA_UNSUBSCRIBE_LIVE_TRENDBAR_REQ = 2136,
    /**
     * @generated from protobuf enum value: PROTO_OA_GET_TRENDBARS_REQ = 2137;
     */
    PROTO_OA_GET_TRENDBARS_REQ = 2137,
    /**
     * @generated from protobuf enum value: PROTO_OA_GET_TRENDBARS_RES = 2138;
     */
    PROTO_OA_GET_TRENDBARS_RES = 2138,
    /**
     * @generated from protobuf enum value: PROTO_OA_EXPECTED_MARGIN_REQ = 2139;
     */
    PROTO_OA_EXPECTED_MARGIN_REQ = 2139,
    /**
     * @generated from protobuf enum value: PROTO_OA_EXPECTED_MARGIN_RES = 2140;
     */
    PROTO_OA_EXPECTED_MARGIN_RES = 2140,
    /**
     * @generated from protobuf enum value: PROTO_OA_MARGIN_CHANGED_EVENT = 2141;
     */
    PROTO_OA_MARGIN_CHANGED_EVENT = 2141,
    /**
     * @generated from protobuf enum value: PROTO_OA_ERROR_RES = 2142;
     */
    PROTO_OA_ERROR_RES = 2142,
    /**
     * @generated from protobuf enum value: PROTO_OA_CASH_FLOW_HISTORY_LIST_REQ = 2143;
     */
    PROTO_OA_CASH_FLOW_HISTORY_LIST_REQ = 2143,
    /**
     * @generated from protobuf enum value: PROTO_OA_CASH_FLOW_HISTORY_LIST_RES = 2144;
     */
    PROTO_OA_CASH_FLOW_HISTORY_LIST_RES = 2144,
    /**
     * @generated from protobuf enum value: PROTO_OA_GET_TICKDATA_REQ = 2145;
     */
    PROTO_OA_GET_TICKDATA_REQ = 2145,
    /**
     * @generated from protobuf enum value: PROTO_OA_GET_TICKDATA_RES = 2146;
     */
    PROTO_OA_GET_TICKDATA_RES = 2146,
    /**
     * @generated from protobuf enum value: PROTO_OA_ACCOUNTS_TOKEN_INVALIDATED_EVENT = 2147;
     */
    PROTO_OA_ACCOUNTS_TOKEN_INVALIDATED_EVENT = 2147,
    /**
     * @generated from protobuf enum value: PROTO_OA_CLIENT_DISCONNECT_EVENT = 2148;
     */
    PROTO_OA_CLIENT_DISCONNECT_EVENT = 2148,
    /**
     * @generated from protobuf enum value: PROTO_OA_GET_ACCOUNTS_BY_ACCESS_TOKEN_REQ = 2149;
     */
    PROTO_OA_GET_ACCOUNTS_BY_ACCESS_TOKEN_REQ = 2149,
    /**
     * @generated from protobuf enum value: PROTO_OA_GET_ACCOUNTS_BY_ACCESS_TOKEN_RES = 2150;
     */
    PROTO_OA_GET_ACCOUNTS_BY_ACCESS_TOKEN_RES = 2150,
    /**
     * @generated from protobuf enum value: PROTO_OA_GET_CTID_PROFILE_BY_TOKEN_REQ = 2151;
     */
    PROTO_OA_GET_CTID_PROFILE_BY_TOKEN_REQ = 2151,
    /**
     * @generated from protobuf enum value: PROTO_OA_GET_CTID_PROFILE_BY_TOKEN_RES = 2152;
     */
    PROTO_OA_GET_CTID_PROFILE_BY_TOKEN_RES = 2152,
    /**
     * @generated from protobuf enum value: PROTO_OA_ASSET_CLASS_LIST_REQ = 2153;
     */
    PROTO_OA_ASSET_CLASS_LIST_REQ = 2153,
    /**
     * @generated from protobuf enum value: PROTO_OA_ASSET_CLASS_LIST_RES = 2154;
     */
    PROTO_OA_ASSET_CLASS_LIST_RES = 2154,
    /**
     * @generated from protobuf enum value: PROTO_OA_DEPTH_EVENT = 2155;
     */
    PROTO_OA_DEPTH_EVENT = 2155,
    /**
     * @generated from protobuf enum value: PROTO_OA_SUBSCRIBE_DEPTH_QUOTES_REQ = 2156;
     */
    PROTO_OA_SUBSCRIBE_DEPTH_QUOTES_REQ = 2156,
    /**
     * @generated from protobuf enum value: PROTO_OA_SUBSCRIBE_DEPTH_QUOTES_RES = 2157;
     */
    PROTO_OA_SUBSCRIBE_DEPTH_QUOTES_RES = 2157,
    /**
     * @generated from protobuf enum value: PROTO_OA_UNSUBSCRIBE_DEPTH_QUOTES_REQ = 2158;
     */
    PROTO_OA_UNSUBSCRIBE_DEPTH_QUOTES_REQ = 2158,
    /**
     * @generated from protobuf enum value: PROTO_OA_UNSUBSCRIBE_DEPTH_QUOTES_RES = 2159;
     */
    PROTO_OA_UNSUBSCRIBE_DEPTH_QUOTES_RES = 2159,
    /**
     * @generated from protobuf enum value: PROTO_OA_SYMBOL_CATEGORY_REQ = 2160;
     */
    PROTO_OA_SYMBOL_CATEGORY_REQ = 2160,
    /**
     * @generated from protobuf enum value: PROTO_OA_SYMBOL_CATEGORY_RES = 2161;
     */
    PROTO_OA_SYMBOL_CATEGORY_RES = 2161,
    /**
     * @generated from protobuf enum value: PROTO_OA_ACCOUNT_LOGOUT_REQ = 2162;
     */
    PROTO_OA_ACCOUNT_LOGOUT_REQ = 2162,
    /**
     * @generated from protobuf enum value: PROTO_OA_ACCOUNT_LOGOUT_RES = 2163;
     */
    PROTO_OA_ACCOUNT_LOGOUT_RES = 2163,
    /**
     * @generated from protobuf enum value: PROTO_OA_ACCOUNT_DISCONNECT_EVENT = 2164;
     */
    PROTO_OA_ACCOUNT_DISCONNECT_EVENT = 2164,
    /**
     * @generated from protobuf enum value: PROTO_OA_SUBSCRIBE_LIVE_TRENDBAR_RES = 2165;
     */
    PROTO_OA_SUBSCRIBE_LIVE_TRENDBAR_RES = 2165,
    /**
     * @generated from protobuf enum value: PROTO_OA_UNSUBSCRIBE_LIVE_TRENDBAR_RES = 2166;
     */
    PROTO_OA_UNSUBSCRIBE_LIVE_TRENDBAR_RES = 2166,
    /**
     * @generated from protobuf enum value: PROTO_OA_MARGIN_CALL_LIST_REQ = 2167;
     */
    PROTO_OA_MARGIN_CALL_LIST_REQ = 2167,
    /**
     * @generated from protobuf enum value: PROTO_OA_MARGIN_CALL_LIST_RES = 2168;
     */
    PROTO_OA_MARGIN_CALL_LIST_RES = 2168,
    /**
     * @generated from protobuf enum value: PROTO_OA_MARGIN_CALL_UPDATE_REQ = 2169;
     */
    PROTO_OA_MARGIN_CALL_UPDATE_REQ = 2169,
    /**
     * @generated from protobuf enum value: PROTO_OA_MARGIN_CALL_UPDATE_RES = 2170;
     */
    PROTO_OA_MARGIN_CALL_UPDATE_RES = 2170,
    /**
     * @generated from protobuf enum value: PROTO_OA_MARGIN_CALL_UPDATE_EVENT = 2171;
     */
    PROTO_OA_MARGIN_CALL_UPDATE_EVENT = 2171,
    /**
     * @generated from protobuf enum value: PROTO_OA_MARGIN_CALL_TRIGGER_EVENT = 2172;
     */
    PROTO_OA_MARGIN_CALL_TRIGGER_EVENT = 2172,
    /**
     * @generated from protobuf enum value: PROTO_OA_REFRESH_TOKEN_REQ = 2173;
     */
    PROTO_OA_REFRESH_TOKEN_REQ = 2173,
    /**
     * @generated from protobuf enum value: PROTO_OA_REFRESH_TOKEN_RES = 2174;
     */
    PROTO_OA_REFRESH_TOKEN_RES = 2174,
    /**
     * @generated from protobuf enum value: PROTO_OA_ORDER_LIST_REQ = 2175;
     */
    PROTO_OA_ORDER_LIST_REQ = 2175,
    /**
     * @generated from protobuf enum value: PROTO_OA_ORDER_LIST_RES = 2176;
     */
    PROTO_OA_ORDER_LIST_RES = 2176,
    /**
     * @generated from protobuf enum value: PROTO_OA_GET_DYNAMIC_LEVERAGE_REQ = 2177;
     */
    PROTO_OA_GET_DYNAMIC_LEVERAGE_REQ = 2177,
    /**
     * @generated from protobuf enum value: PROTO_OA_GET_DYNAMIC_LEVERAGE_RES = 2178;
     */
    PROTO_OA_GET_DYNAMIC_LEVERAGE_RES = 2178,
    /**
     * @generated from protobuf enum value: PROTO_OA_DEAL_LIST_BY_POSITION_ID_REQ = 2179;
     */
    PROTO_OA_DEAL_LIST_BY_POSITION_ID_REQ = 2179,
    /**
     * @generated from protobuf enum value: PROTO_OA_DEAL_LIST_BY_POSITION_ID_RES = 2180;
     */
    PROTO_OA_DEAL_LIST_BY_POSITION_ID_RES = 2180,
    /**
     * @generated from protobuf enum value: PROTO_OA_ORDER_DETAILS_REQ = 2181;
     */
    PROTO_OA_ORDER_DETAILS_REQ = 2181,
    /**
     * @generated from protobuf enum value: PROTO_OA_ORDER_DETAILS_RES = 2182;
     */
    PROTO_OA_ORDER_DETAILS_RES = 2182,
    /**
     * @generated from protobuf enum value: PROTO_OA_ORDER_LIST_BY_POSITION_ID_REQ = 2183;
     */
    PROTO_OA_ORDER_LIST_BY_POSITION_ID_REQ = 2183,
    /**
     * @generated from protobuf enum value: PROTO_OA_ORDER_LIST_BY_POSITION_ID_RES = 2184;
     */
    PROTO_OA_ORDER_LIST_BY_POSITION_ID_RES = 2184,
    /**
     * @generated from protobuf enum value: PROTO_OA_DEAL_OFFSET_LIST_REQ = 2185;
     */
    PROTO_OA_DEAL_OFFSET_LIST_REQ = 2185,
    /**
     * @generated from protobuf enum value: PROTO_OA_DEAL_OFFSET_LIST_RES = 2186;
     */
    PROTO_OA_DEAL_OFFSET_LIST_RES = 2186,
    /**
     * @generated from protobuf enum value: PROTO_OA_GET_POSITION_UNREALIZED_PNL_REQ = 2187;
     */
    PROTO_OA_GET_POSITION_UNREALIZED_PNL_REQ = 2187,
    /**
     * @generated from protobuf enum value: PROTO_OA_GET_POSITION_UNREALIZED_PNL_RES = 2188;
     */
    PROTO_OA_GET_POSITION_UNREALIZED_PNL_RES = 2188,
    /**
     * @generated from protobuf enum value: PROTO_OA_V1_PNL_CHANGE_EVENT = 2189;
     */
    PROTO_OA_V1_PNL_CHANGE_EVENT = 2189,
    /**
     * @generated from protobuf enum value: PROTO_OA_V1_PNL_CHANGE_SUBSCRIBE_REQ = 2190;
     */
    PROTO_OA_V1_PNL_CHANGE_SUBSCRIBE_REQ = 2190,
    /**
     * @generated from protobuf enum value: PROTO_OA_V1_PNL_CHANGE_SUBSCRIBE_RES = 2191;
     */
    PROTO_OA_V1_PNL_CHANGE_SUBSCRIBE_RES = 2191,
    /**
     * @generated from protobuf enum value: PROTO_OA_V1_PNL_CHANGE_UN_SUBSCRIBE_REQ = 2192;
     */
    PROTO_OA_V1_PNL_CHANGE_UN_SUBSCRIBE_REQ = 2192,
    /**
     * @generated from protobuf enum value: PROTO_OA_V1_PNL_CHANGE_UN_SUBSCRIBE_RES = 2193;
     */
    PROTO_OA_V1_PNL_CHANGE_UN_SUBSCRIBE_RES = 2193
}
/**
 * @generated from protobuf enum ProtoOADayOfWeek
 */
export enum ProtoOADayOfWeek {
    /**
     * @generated from protobuf enum value: NONE = 0;
     */
    NONE = 0,
    /**
     * @generated from protobuf enum value: MONDAY = 1;
     */
    MONDAY = 1,
    /**
     * @generated from protobuf enum value: TUESDAY = 2;
     */
    TUESDAY = 2,
    /**
     * @generated from protobuf enum value: WEDNESDAY = 3;
     */
    WEDNESDAY = 3,
    /**
     * @generated from protobuf enum value: THURSDAY = 4;
     */
    THURSDAY = 4,
    /**
     * @generated from protobuf enum value: FRIDAY = 5;
     */
    FRIDAY = 5,
    /**
     * @generated from protobuf enum value: SATURDAY = 6;
     */
    SATURDAY = 6,
    /**
     * @generated from protobuf enum value: SUNDAY = 7;
     */
    SUNDAY = 7
}
/**
 * * Enum for specifying type of trading commission.
 *
 * @generated from protobuf enum ProtoOACommissionType
 */
export enum ProtoOACommissionType {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * USD per million USD volume - usually used for FX. Example: 50 USD for 1 mil USD of trading volume.
     *
     * @generated from protobuf enum value: USD_PER_MILLION_USD = 1;
     */
    USD_PER_MILLION_USD = 1,
    /**
     * USD per 1 lot - usually used for CFDs and futures for commodities, and indices. Example: 15 USD for 1 contract.
     *
     * @generated from protobuf enum value: USD_PER_LOT = 2;
     */
    USD_PER_LOT = 2,
    /**
     * Percentage of trading volume - usually used for Equities. Example: 0.005% of notional trading volume. Multiplied by 100,000.
     *
     * @generated from protobuf enum value: PERCENTAGE_OF_VALUE = 3;
     */
    PERCENTAGE_OF_VALUE = 3,
    /**
     * Quote ccy of Symbol per 1 lot - will be used for CFDs and futures for commodities, and indices. Example: 15 EUR for 1 contract of DAX.
     *
     * @generated from protobuf enum value: QUOTE_CCY_PER_LOT = 4;
     */
    QUOTE_CCY_PER_LOT = 4
}
/**
 * * Enum for specifying stop loss and take profit distances.
 *
 * @generated from protobuf enum ProtoOASymbolDistanceType
 */
export enum ProtoOASymbolDistanceType {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * @generated from protobuf enum value: SYMBOL_DISTANCE_IN_POINTS = 1;
     */
    SYMBOL_DISTANCE_IN_POINTS = 1,
    /**
     * @generated from protobuf enum value: SYMBOL_DISTANCE_IN_PERCENTAGE = 2;
     */
    SYMBOL_DISTANCE_IN_PERCENTAGE = 2
}
/**
 * * Enum for specifying type of minimum trading commission.
 *
 * @generated from protobuf enum ProtoOAMinCommissionType
 */
export enum ProtoOAMinCommissionType {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * @generated from protobuf enum value: CURRENCY = 1;
     */
    CURRENCY = 1,
    /**
     * @generated from protobuf enum value: QUOTE_CURRENCY = 2;
     */
    QUOTE_CURRENCY = 2
}
/**
 * * Enum for specifying symbol trading mode.
 *
 * @generated from protobuf enum ProtoOATradingMode
 */
export enum ProtoOATradingMode {
    /**
     * @generated from protobuf enum value: ENABLED = 0;
     */
    ENABLED = 0,
    /**
     * @generated from protobuf enum value: DISABLED_WITHOUT_PENDINGS_EXECUTION = 1;
     */
    DISABLED_WITHOUT_PENDINGS_EXECUTION = 1,
    /**
     * @generated from protobuf enum value: DISABLED_WITH_PENDINGS_EXECUTION = 2;
     */
    DISABLED_WITH_PENDINGS_EXECUTION = 2,
    /**
     * @generated from protobuf enum value: CLOSE_ONLY_MODE = 3;
     */
    CLOSE_ONLY_MODE = 3
}
/**
 * * Enum for specifying SWAP calculation type for symbol.
 *
 * @generated from protobuf enum ProtoOASwapCalculationType
 */
export enum ProtoOASwapCalculationType {
    /**
     * Specifies type of SWAP computation as PIPS (0)
     *
     * @generated from protobuf enum value: PIPS = 0;
     */
    PIPS = 0,
    /**
     * Specifies type of SWAP computation as PERCENTAGE (1, annual, in percent)
     *
     * @generated from protobuf enum value: PERCENTAGE = 1;
     */
    PERCENTAGE = 1,
    /**
     * Specifies type of SWAP computation as POINTS (2)
     *
     * @generated from protobuf enum value: POINTS = 2;
     */
    POINTS = 2
}
/**
 * * Enum for specifying access right for a trader.
 *
 * @generated from protobuf enum ProtoOAAccessRights
 */
export enum ProtoOAAccessRights {
    /**
     * Enable all trading.
     *
     * @generated from protobuf enum value: FULL_ACCESS = 0;
     */
    FULL_ACCESS = 0,
    /**
     * Only closing trading request are enabled.
     *
     * @generated from protobuf enum value: CLOSE_ONLY = 1;
     */
    CLOSE_ONLY = 1,
    /**
     * View only access.
     *
     * @generated from protobuf enum value: NO_TRADING = 2;
     */
    NO_TRADING = 2,
    /**
     * No access.
     *
     * @generated from protobuf enum value: NO_LOGIN = 3;
     */
    NO_LOGIN = 3
}
/**
 * * Enum for specifying margin calculation type for an account.
 *
 * @generated from protobuf enum ProtoOATotalMarginCalculationType
 */
export enum ProtoOATotalMarginCalculationType {
    /**
     * @generated from protobuf enum value: MAX = 0;
     */
    MAX = 0,
    /**
     * @generated from protobuf enum value: SUM = 1;
     */
    SUM = 1,
    /**
     * @generated from protobuf enum value: NET = 2;
     */
    NET = 2
}
/**
 * * Enum for specifying type of an account.
 *
 * @generated from protobuf enum ProtoOAAccountType
 */
export enum ProtoOAAccountType {
    /**
     * Allows multiple positions on a trading account for a symbol.
     *
     * @generated from protobuf enum value: HEDGED = 0;
     */
    HEDGED = 0,
    /**
     * Only one position per symbol is allowed on a trading account.
     *
     * @generated from protobuf enum value: NETTED = 1;
     */
    NETTED = 1,
    /**
     * Spread betting type account.
     *
     * @generated from protobuf enum value: SPREAD_BETTING = 2;
     */
    SPREAD_BETTING = 2
}
/**
 * * Position status ENUM.
 *
 * @generated from protobuf enum ProtoOAPositionStatus
 */
export enum ProtoOAPositionStatus {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * @generated from protobuf enum value: POSITION_STATUS_OPEN = 1;
     */
    POSITION_STATUS_OPEN = 1,
    /**
     * @generated from protobuf enum value: POSITION_STATUS_CLOSED = 2;
     */
    POSITION_STATUS_CLOSED = 2,
    /**
     * Empty position is created for pending order.
     *
     * @generated from protobuf enum value: POSITION_STATUS_CREATED = 3;
     */
    POSITION_STATUS_CREATED = 3,
    /**
     * @generated from protobuf enum value: POSITION_STATUS_ERROR = 4;
     */
    POSITION_STATUS_ERROR = 4
}
/**
 * * Trader side ENUM. Used for order, position, deal.
 *
 * @generated from protobuf enum ProtoOATradeSide
 */
export enum ProtoOATradeSide {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * @generated from protobuf enum value: BUY = 1;
     */
    BUY = 1,
    /**
     * @generated from protobuf enum value: SELL = 2;
     */
    SELL = 2
}
/**
 * * Order type ENUM.
 *
 * @generated from protobuf enum ProtoOAOrderType
 */
export enum ProtoOAOrderType {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * @generated from protobuf enum value: MARKET = 1;
     */
    MARKET = 1,
    /**
     * @generated from protobuf enum value: LIMIT = 2;
     */
    LIMIT = 2,
    /**
     * @generated from protobuf enum value: STOP = 3;
     */
    STOP = 3,
    /**
     * @generated from protobuf enum value: STOP_LOSS_TAKE_PROFIT = 4;
     */
    STOP_LOSS_TAKE_PROFIT = 4,
    /**
     * @generated from protobuf enum value: MARKET_RANGE = 5;
     */
    MARKET_RANGE = 5,
    /**
     * @generated from protobuf enum value: STOP_LIMIT = 6;
     */
    STOP_LIMIT = 6
}
/**
 * * Order's time in force ENUM.
 *
 * @generated from protobuf enum ProtoOATimeInForce
 */
export enum ProtoOATimeInForce {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * @generated from protobuf enum value: GOOD_TILL_DATE = 1;
     */
    GOOD_TILL_DATE = 1,
    /**
     * @generated from protobuf enum value: GOOD_TILL_CANCEL = 2;
     */
    GOOD_TILL_CANCEL = 2,
    /**
     * @generated from protobuf enum value: IMMEDIATE_OR_CANCEL = 3;
     */
    IMMEDIATE_OR_CANCEL = 3,
    /**
     * @generated from protobuf enum value: FILL_OR_KILL = 4;
     */
    FILL_OR_KILL = 4,
    /**
     * @generated from protobuf enum value: MARKET_ON_OPEN = 5;
     */
    MARKET_ON_OPEN = 5
}
/**
 * * Order status ENUM.
 *
 * @generated from protobuf enum ProtoOAOrderStatus
 */
export enum ProtoOAOrderStatus {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * Order request validated and accepted for execution.
     *
     * @generated from protobuf enum value: ORDER_STATUS_ACCEPTED = 1;
     */
    ORDER_STATUS_ACCEPTED = 1,
    /**
     * Order is fully filled.
     *
     * @generated from protobuf enum value: ORDER_STATUS_FILLED = 2;
     */
    ORDER_STATUS_FILLED = 2,
    /**
     * Order is rejected due to validation.
     *
     * @generated from protobuf enum value: ORDER_STATUS_REJECTED = 3;
     */
    ORDER_STATUS_REJECTED = 3,
    /**
     * Order expired. Might be valid for orders with partially filled volume that were expired on LP.
     *
     * @generated from protobuf enum value: ORDER_STATUS_EXPIRED = 4;
     */
    ORDER_STATUS_EXPIRED = 4,
    /**
     * Order is cancelled. Might be valid for orders with partially filled volume that were cancelled by LP.
     *
     * @generated from protobuf enum value: ORDER_STATUS_CANCELLED = 5;
     */
    ORDER_STATUS_CANCELLED = 5
}
/**
 * * Stop Order and Stop Lost triggering method ENUM.
 *
 * @generated from protobuf enum ProtoOAOrderTriggerMethod
 */
export enum ProtoOAOrderTriggerMethod {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * Stop Order: buy is triggered by ask, sell by bid; Stop Loss Order: for buy position is triggered by bid and for sell position by ask.
     *
     * @generated from protobuf enum value: TRADE = 1;
     */
    TRADE = 1,
    /**
     * Stop Order: buy is triggered by bid, sell by ask; Stop Loss Order: for buy position is triggered by ask and for sell position by bid.
     *
     * @generated from protobuf enum value: OPPOSITE = 2;
     */
    OPPOSITE = 2,
    /**
     * The same as TRADE, but trigger is checked after the second consecutive tick.
     *
     * @generated from protobuf enum value: DOUBLE_TRADE = 3;
     */
    DOUBLE_TRADE = 3,
    /**
     * The same as OPPOSITE, but trigger is checked after the second consecutive tick.
     *
     * @generated from protobuf enum value: DOUBLE_OPPOSITE = 4;
     */
    DOUBLE_OPPOSITE = 4
}
/**
 * * Execution event type ENUM.
 *
 * @generated from protobuf enum ProtoOAExecutionType
 */
export enum ProtoOAExecutionType {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * Order passed validation.
     *
     * @generated from protobuf enum value: ORDER_ACCEPTED = 2;
     */
    ORDER_ACCEPTED = 2,
    /**
     * Order filled.
     *
     * @generated from protobuf enum value: ORDER_FILLED = 3;
     */
    ORDER_FILLED = 3,
    /**
     * Pending order is changed with a new one.
     *
     * @generated from protobuf enum value: ORDER_REPLACED = 4;
     */
    ORDER_REPLACED = 4,
    /**
     * Order cancelled.
     *
     * @generated from protobuf enum value: ORDER_CANCELLED = 5;
     */
    ORDER_CANCELLED = 5,
    /**
     * Order with GTD time in force is expired.
     *
     * @generated from protobuf enum value: ORDER_EXPIRED = 6;
     */
    ORDER_EXPIRED = 6,
    /**
     * Order is rejected due to validations.
     *
     * @generated from protobuf enum value: ORDER_REJECTED = 7;
     */
    ORDER_REJECTED = 7,
    /**
     * Cancel order request is rejected.
     *
     * @generated from protobuf enum value: ORDER_CANCEL_REJECTED = 8;
     */
    ORDER_CANCEL_REJECTED = 8,
    /**
     * Type related to SWAP execution events.
     *
     * @generated from protobuf enum value: SWAP = 9;
     */
    SWAP = 9,
    /**
     * Type related to event of deposit or withdrawal cash flow operation.
     *
     * @generated from protobuf enum value: DEPOSIT_WITHDRAW = 10;
     */
    DEPOSIT_WITHDRAW = 10,
    /**
     * Order is partially filled.
     *
     * @generated from protobuf enum value: ORDER_PARTIAL_FILL = 11;
     */
    ORDER_PARTIAL_FILL = 11,
    /**
     * Type related to event of bonus deposit or bonus withdrawal.
     *
     * @generated from protobuf enum value: BONUS_DEPOSIT_WITHDRAW = 12;
     */
    BONUS_DEPOSIT_WITHDRAW = 12
}
/**
 * * Bonus operation type ENUM.
 *
 * @generated from protobuf enum ProtoOAChangeBonusType
 */
export enum ProtoOAChangeBonusType {
    /**
     * @generated from protobuf enum value: BONUS_DEPOSIT = 0;
     */
    BONUS_DEPOSIT = 0,
    /**
     * @generated from protobuf enum value: BONUS_WITHDRAW = 1;
     */
    BONUS_WITHDRAW = 1
}
/**
 * * Balance operation entity. Covers all cash movement operations related to account, trading, IB operations, mirroring, etc.
 *
 * @generated from protobuf enum ProtoOAChangeBalanceType
 */
export enum ProtoOAChangeBalanceType {
    /**
     * Cash deposit.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT = 0;
     */
    BALANCE_DEPOSIT = 0,
    /**
     * Cash withdrawal.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW = 1;
     */
    BALANCE_WITHDRAW = 1,
    /**
     * Received mirroring commission.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_STRATEGY_COMMISSION_INNER = 3;
     */
    BALANCE_DEPOSIT_STRATEGY_COMMISSION_INNER = 3,
    /**
     * Paid mirroring commission.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_STRATEGY_COMMISSION_INNER = 4;
     */
    BALANCE_WITHDRAW_STRATEGY_COMMISSION_INNER = 4,
    /**
     * For IB account. Commissions paid by trader.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_IB_COMMISSIONS = 5;
     */
    BALANCE_DEPOSIT_IB_COMMISSIONS = 5,
    /**
     * For IB account. Withdrawal of commissions shared with broker.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_IB_SHARED_PERCENTAGE = 6;
     */
    BALANCE_WITHDRAW_IB_SHARED_PERCENTAGE = 6,
    /**
     * For IB account. Commissions paid by sub-ibs.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_IB_SHARED_PERCENTAGE_FROM_SUB_IB = 7;
     */
    BALANCE_DEPOSIT_IB_SHARED_PERCENTAGE_FROM_SUB_IB = 7,
    /**
     * For IB account. Commissions paid by broker.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_IB_SHARED_PERCENTAGE_FROM_BROKER = 8;
     */
    BALANCE_DEPOSIT_IB_SHARED_PERCENTAGE_FROM_BROKER = 8,
    /**
     * Deposit rebate for trading volume for period.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_REBATE = 9;
     */
    BALANCE_DEPOSIT_REBATE = 9,
    /**
     * Withdrawal of rebate.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_REBATE = 10;
     */
    BALANCE_WITHDRAW_REBATE = 10,
    /**
     * Mirroring commission.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_STRATEGY_COMMISSION_OUTER = 11;
     */
    BALANCE_DEPOSIT_STRATEGY_COMMISSION_OUTER = 11,
    /**
     * Mirroring commission.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_STRATEGY_COMMISSION_OUTER = 12;
     */
    BALANCE_WITHDRAW_STRATEGY_COMMISSION_OUTER = 12,
    /**
     * For IB account. Share commission with the Broker.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_BONUS_COMPENSATION = 13;
     */
    BALANCE_WITHDRAW_BONUS_COMPENSATION = 13,
    /**
     * IB commissions.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_IB_SHARED_PERCENTAGE_TO_BROKER = 14;
     */
    BALANCE_WITHDRAW_IB_SHARED_PERCENTAGE_TO_BROKER = 14,
    /**
     * Deposit dividends payments.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_DIVIDENDS = 15;
     */
    BALANCE_DEPOSIT_DIVIDENDS = 15,
    /**
     * Negative dividend charge for short position.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_DIVIDENDS = 16;
     */
    BALANCE_WITHDRAW_DIVIDENDS = 16,
    /**
     * Charge for guaranteedStopLoss.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_GSL_CHARGE = 17;
     */
    BALANCE_WITHDRAW_GSL_CHARGE = 17,
    /**
     * Charge of rollover fee for Shariah compliant accounts.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_ROLLOVER = 18;
     */
    BALANCE_WITHDRAW_ROLLOVER = 18,
    /**
     * Broker's operation to deposit bonus.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_NONWITHDRAWABLE_BONUS = 19;
     */
    BALANCE_DEPOSIT_NONWITHDRAWABLE_BONUS = 19,
    /**
     * Broker's operation to withdrawal bonus.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_NONWITHDRAWABLE_BONUS = 20;
     */
    BALANCE_WITHDRAW_NONWITHDRAWABLE_BONUS = 20,
    /**
     * Deposits of negative SWAP.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_SWAP = 21;
     */
    BALANCE_DEPOSIT_SWAP = 21,
    /**
     * SWAP charges.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_SWAP = 22;
     */
    BALANCE_WITHDRAW_SWAP = 22,
    /**
     * Mirroring commission.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_MANAGEMENT_FEE = 27;
     */
    BALANCE_DEPOSIT_MANAGEMENT_FEE = 27,
    /**
     * Mirroring commission. Deprecated since 7.1 in favor of BALANCE_WITHDRAW_COPY_FEE (34).
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_MANAGEMENT_FEE = 28;
     */
    BALANCE_WITHDRAW_MANAGEMENT_FEE = 28,
    /**
     * Mirroring commission.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_PERFORMANCE_FEE = 29;
     */
    BALANCE_DEPOSIT_PERFORMANCE_FEE = 29,
    /**
     * Withdraw for subaccount creation (cTrader Copy).
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_FOR_SUBACCOUNT = 30;
     */
    BALANCE_WITHDRAW_FOR_SUBACCOUNT = 30,
    /**
     * Deposit to subaccount on creation (cTrader Copy).
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_TO_SUBACCOUNT = 31;
     */
    BALANCE_DEPOSIT_TO_SUBACCOUNT = 31,
    /**
     * Manual user's withdraw from subaccount (cTrader Copy), to parent account.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_FROM_SUBACCOUNT = 32;
     */
    BALANCE_WITHDRAW_FROM_SUBACCOUNT = 32,
    /**
     * Manual user's deposit to subaccount (cTrader Copy), from parent account.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_FROM_SUBACCOUNT = 33;
     */
    BALANCE_DEPOSIT_FROM_SUBACCOUNT = 33,
    /**
     * Withdrawal fees to Strategy Provider.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_COPY_FEE = 34;
     */
    BALANCE_WITHDRAW_COPY_FEE = 34,
    /**
     * Withdraw of inactivity fee from the balance.
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_INACTIVITY_FEE = 35;
     */
    BALANCE_WITHDRAW_INACTIVITY_FEE = 35,
    /**
     * Deposit within the same server (from another account).
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_TRANSFER = 36;
     */
    BALANCE_DEPOSIT_TRANSFER = 36,
    /**
     * Withdraw within the same server (to another account).
     *
     * @generated from protobuf enum value: BALANCE_WITHDRAW_TRANSFER = 37;
     */
    BALANCE_WITHDRAW_TRANSFER = 37,
    /**
     * Bonus being converted from virtual bonus to real deposit.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_CONVERTED_BONUS = 38;
     */
    BALANCE_DEPOSIT_CONVERTED_BONUS = 38,
    /**
     * Applies if negative balance protection is configured by broker, should make balance = 0.
     *
     * @generated from protobuf enum value: BALANCE_DEPOSIT_NEGATIVE_BALANCE_PROTECTION = 39;
     */
    BALANCE_DEPOSIT_NEGATIVE_BALANCE_PROTECTION = 39
}
/**
 * * Deal status ENUM.
 *
 * @generated from protobuf enum ProtoOADealStatus
 */
export enum ProtoOADealStatus {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * Deal filled.
     *
     * @generated from protobuf enum value: FILLED = 2;
     */
    FILLED = 2,
    /**
     * Deal is partially filled.
     *
     * @generated from protobuf enum value: PARTIALLY_FILLED = 3;
     */
    PARTIALLY_FILLED = 3,
    /**
     * Deal is correct but was rejected by liquidity provider (e.g. no liquidity).
     *
     * @generated from protobuf enum value: REJECTED = 4;
     */
    REJECTED = 4,
    /**
     * Deal rejected by server (e.g. no price quotes).
     *
     * @generated from protobuf enum value: INTERNALLY_REJECTED = 5;
     */
    INTERNALLY_REJECTED = 5,
    /**
     * Deal is rejected by LP due to error (e.g. symbol is unknown).
     *
     * @generated from protobuf enum value: ERROR = 6;
     */
    ERROR = 6,
    /**
     * Liquidity provider did not sent response on the deal during specified execution time period.
     *
     * @generated from protobuf enum value: MISSED = 7;
     */
    MISSED = 7
}
/**
 * * Trendbar period ENUM.
 *
 * @generated from protobuf enum ProtoOATrendbarPeriod
 */
export enum ProtoOATrendbarPeriod {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * @generated from protobuf enum value: M1 = 1;
     */
    M1 = 1,
    /**
     * @generated from protobuf enum value: M2 = 2;
     */
    M2 = 2,
    /**
     * @generated from protobuf enum value: M3 = 3;
     */
    M3 = 3,
    /**
     * @generated from protobuf enum value: M4 = 4;
     */
    M4 = 4,
    /**
     * @generated from protobuf enum value: M5 = 5;
     */
    M5 = 5,
    /**
     * @generated from protobuf enum value: M10 = 6;
     */
    M10 = 6,
    /**
     * @generated from protobuf enum value: M15 = 7;
     */
    M15 = 7,
    /**
     * @generated from protobuf enum value: M30 = 8;
     */
    M30 = 8,
    /**
     * @generated from protobuf enum value: H1 = 9;
     */
    H1 = 9,
    /**
     * @generated from protobuf enum value: H4 = 10;
     */
    H4 = 10,
    /**
     * @generated from protobuf enum value: H12 = 11;
     */
    H12 = 11,
    /**
     * @generated from protobuf enum value: D1 = 12;
     */
    D1 = 12,
    /**
     * @generated from protobuf enum value: W1 = 13;
     */
    W1 = 13,
    /**
     * @generated from protobuf enum value: MN1 = 14;
     */
    MN1 = 14
}
/**
 * * Price quote type.
 *
 * @generated from protobuf enum ProtoOAQuoteType
 */
export enum ProtoOAQuoteType {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * @generated from protobuf enum value: BID = 1;
     */
    BID = 1,
    /**
     * @generated from protobuf enum value: ASK = 2;
     */
    ASK = 2
}
/**
 * * Open API application permission in regards to token ENUM.
 *
 * @generated from protobuf enum ProtoOAClientPermissionScope
 */
export enum ProtoOAClientPermissionScope {
    /**
     * Allows to use only view commends. Trade is prohibited.
     *
     * @generated from protobuf enum value: SCOPE_VIEW = 0;
     */
    SCOPE_VIEW = 0,
    /**
     * Allows to use all commands.
     *
     * @generated from protobuf enum value: SCOPE_TRADE = 1;
     */
    SCOPE_TRADE = 1
}
/**
 * * Type of notification, currently only 3 instances of marginCall are supported.
 *
 * @generated from protobuf enum ProtoOANotificationType
 */
export enum ProtoOANotificationType {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * one of three margin calls, they are all similar.
     *
     * @generated from protobuf enum value: MARGIN_LEVEL_THRESHOLD_1 = 61;
     */
    MARGIN_LEVEL_THRESHOLD_1 = 61,
    /**
     * one of three margin calls, they are all similar.
     *
     * @generated from protobuf enum value: MARGIN_LEVEL_THRESHOLD_2 = 62;
     */
    MARGIN_LEVEL_THRESHOLD_2 = 62,
    /**
     * one of three margin calls, they are all similar.
     *
     * @generated from protobuf enum value: MARGIN_LEVEL_THRESHOLD_3 = 63;
     */
    MARGIN_LEVEL_THRESHOLD_3 = 63
}
/**
 * * Error code ENUM.
 *
 * @generated from protobuf enum ProtoOAErrorCode
 */
export enum ProtoOAErrorCode {
    /**
     * @generated synthetic value - protobuf-ts requires all enums to have a 0 value
     */
    UNSPECIFIED$ = 0,
    /**
     * Authorization
     *
     * When token used for account authorization is expired.
     *
     * @generated from protobuf enum value: OA_AUTH_TOKEN_EXPIRED = 1;
     */
    OA_AUTH_TOKEN_EXPIRED = 1,
    /**
     * When account is not authorized.
     *
     * @generated from protobuf enum value: ACCOUNT_NOT_AUTHORIZED = 2;
     */
    ACCOUNT_NOT_AUTHORIZED = 2,
    /**
     * When such account no longer exists.
     *
     * @generated from protobuf enum value: RET_NO_SUCH_LOGIN = 12;
     */
    RET_NO_SUCH_LOGIN = 12,
    /**
     * When client tries to authorize after it was already authorized.
     *
     * @generated from protobuf enum value: ALREADY_LOGGED_IN = 14;
     */
    ALREADY_LOGGED_IN = 14,
    /**
     * When account is disabled.
     *
     * @generated from protobuf enum value: RET_ACCOUNT_DISABLED = 64;
     */
    RET_ACCOUNT_DISABLED = 64,
    /**
     * Open API client is not activated or wrong client credentials.
     *
     * @generated from protobuf enum value: CH_CLIENT_AUTH_FAILURE = 101;
     */
    CH_CLIENT_AUTH_FAILURE = 101,
    /**
     * When a command is sent for not authorized Open API client.
     *
     * @generated from protobuf enum value: CH_CLIENT_NOT_AUTHENTICATED = 102;
     */
    CH_CLIENT_NOT_AUTHENTICATED = 102,
    /**
     * Client is trying to authenticate twice.
     *
     * @generated from protobuf enum value: CH_CLIENT_ALREADY_AUTHENTICATED = 103;
     */
    CH_CLIENT_ALREADY_AUTHENTICATED = 103,
    /**
     * Access token is invalid.
     *
     * @generated from protobuf enum value: CH_ACCESS_TOKEN_INVALID = 104;
     */
    CH_ACCESS_TOKEN_INVALID = 104,
    /**
     * Trading service is not available.
     *
     * @generated from protobuf enum value: CH_SERVER_NOT_REACHABLE = 105;
     */
    CH_SERVER_NOT_REACHABLE = 105,
    /**
     * Trading account is not found.
     *
     * @generated from protobuf enum value: CH_CTID_TRADER_ACCOUNT_NOT_FOUND = 106;
     */
    CH_CTID_TRADER_ACCOUNT_NOT_FOUND = 106,
    /**
     * Could not find this client id.
     *
     * @generated from protobuf enum value: CH_OA_CLIENT_NOT_FOUND = 107;
     */
    CH_OA_CLIENT_NOT_FOUND = 107,
    /**
     * General
     *
     * Request frequency is reached.
     *
     * @generated from protobuf enum value: REQUEST_FREQUENCY_EXCEEDED = 108;
     */
    REQUEST_FREQUENCY_EXCEEDED = 108,
    /**
     * Server is under maintenance.
     *
     * @generated from protobuf enum value: SERVER_IS_UNDER_MAINTENANCE = 109;
     */
    SERVER_IS_UNDER_MAINTENANCE = 109,
    /**
     * Operations are not allowed for this account.
     *
     * @generated from protobuf enum value: CHANNEL_IS_BLOCKED = 110;
     */
    CHANNEL_IS_BLOCKED = 110,
    /**
     * Limit of connections is reached for this Open API client.
     *
     * @generated from protobuf enum value: CONNECTIONS_LIMIT_EXCEEDED = 67;
     */
    CONNECTIONS_LIMIT_EXCEEDED = 67,
    /**
     * Not allowed to increase risk for Positions with Guaranteed Stop Loss.
     *
     * @generated from protobuf enum value: WORSE_GSL_NOT_ALLOWED = 68;
     */
    WORSE_GSL_NOT_ALLOWED = 68,
    /**
     * Trading disabled because symbol has holiday.
     *
     * @generated from protobuf enum value: SYMBOL_HAS_HOLIDAY = 69;
     */
    SYMBOL_HAS_HOLIDAY = 69,
    /**
     * Pricing
     *
     * When trying to subscribe to depth, trendbars, etc. without spot subscription.
     *
     * @generated from protobuf enum value: NOT_SUBSCRIBED_TO_SPOTS = 112;
     */
    NOT_SUBSCRIBED_TO_SPOTS = 112,
    /**
     * When subscription is requested for an active.
     *
     * @generated from protobuf enum value: ALREADY_SUBSCRIBED = 113;
     */
    ALREADY_SUBSCRIBED = 113,
    /**
     * Symbol not found.
     *
     * @generated from protobuf enum value: SYMBOL_NOT_FOUND = 114;
     */
    SYMBOL_NOT_FOUND = 114,
    /**
     * Note: to be merged with SYMBOL_NOT_FOUND.
     *
     * @generated from protobuf enum value: UNKNOWN_SYMBOL = 115;
     */
    UNKNOWN_SYMBOL = 115,
    /**
     * When requested period (from,to) is too large or invalid values are set to from/to.
     *
     * @generated from protobuf enum value: INCORRECT_BOUNDARIES = 35;
     */
    INCORRECT_BOUNDARIES = 35,
    /**
     * Trading
     *
     * Trading cannot be done as not quotes are available. Applicable for Book B.
     *
     * @generated from protobuf enum value: NO_QUOTES = 117;
     */
    NO_QUOTES = 117,
    /**
     * Not enough funds to allocate margin.
     *
     * @generated from protobuf enum value: NOT_ENOUGH_MONEY = 118;
     */
    NOT_ENOUGH_MONEY = 118,
    /**
     * Max exposure limit is reached for a {trader, symbol, side}.
     *
     * @generated from protobuf enum value: MAX_EXPOSURE_REACHED = 119;
     */
    MAX_EXPOSURE_REACHED = 119,
    /**
     * Position not found.
     *
     * @generated from protobuf enum value: POSITION_NOT_FOUND = 120;
     */
    POSITION_NOT_FOUND = 120,
    /**
     * Order not found.
     *
     * @generated from protobuf enum value: ORDER_NOT_FOUND = 121;
     */
    ORDER_NOT_FOUND = 121,
    /**
     * When trying to close a position that it is not open.
     *
     * @generated from protobuf enum value: POSITION_NOT_OPEN = 122;
     */
    POSITION_NOT_OPEN = 122,
    /**
     * Position in the state that does not allow to perform an operation.
     *
     * @generated from protobuf enum value: POSITION_LOCKED = 123;
     */
    POSITION_LOCKED = 123,
    /**
     * Trading account reached its limit for max number of open positions and orders.
     *
     * @generated from protobuf enum value: TOO_MANY_POSITIONS = 124;
     */
    TOO_MANY_POSITIONS = 124,
    /**
     * Invalid volume.
     *
     * @generated from protobuf enum value: TRADING_BAD_VOLUME = 125;
     */
    TRADING_BAD_VOLUME = 125,
    /**
     * Invalid stop price.
     *
     * @generated from protobuf enum value: TRADING_BAD_STOPS = 126;
     */
    TRADING_BAD_STOPS = 126,
    /**
     * Invalid price (e.g. negative).
     *
     * @generated from protobuf enum value: TRADING_BAD_PRICES = 127;
     */
    TRADING_BAD_PRICES = 127,
    /**
     * Invalid stake volume (e.g. negative).
     *
     * @generated from protobuf enum value: TRADING_BAD_STAKE = 128;
     */
    TRADING_BAD_STAKE = 128,
    /**
     * Invalid protection prices.
     *
     * @generated from protobuf enum value: PROTECTION_IS_TOO_CLOSE_TO_MARKET = 129;
     */
    PROTECTION_IS_TOO_CLOSE_TO_MARKET = 129,
    /**
     * Invalid expiration.
     *
     * @generated from protobuf enum value: TRADING_BAD_EXPIRATION_DATE = 130;
     */
    TRADING_BAD_EXPIRATION_DATE = 130,
    /**
     * Unable to apply changes as position has an order under execution.
     *
     * @generated from protobuf enum value: PENDING_EXECUTION = 131;
     */
    PENDING_EXECUTION = 131,
    /**
     * Trading is blocked for the symbol.
     *
     * @generated from protobuf enum value: TRADING_DISABLED = 132;
     */
    TRADING_DISABLED = 132,
    /**
     * Trading account is in read only mode.
     *
     * @generated from protobuf enum value: TRADING_NOT_ALLOWED = 133;
     */
    TRADING_NOT_ALLOWED = 133,
    /**
     * Unable to cancel order.
     *
     * @generated from protobuf enum value: UNABLE_TO_CANCEL_ORDER = 134;
     */
    UNABLE_TO_CANCEL_ORDER = 134,
    /**
     * Unable to amend order.
     *
     * @generated from protobuf enum value: UNABLE_TO_AMEND_ORDER = 135;
     */
    UNABLE_TO_AMEND_ORDER = 135,
    /**
     * Short selling is not allowed.
     *
     * @generated from protobuf enum value: SHORT_SELLING_NOT_ALLOWED = 136;
     */
    SHORT_SELLING_NOT_ALLOWED = 136,
    /**
     * This session is not subscribed via ProtoOAv1PnLChangeSubscribeReq
     *
     * @generated from protobuf enum value: NOT_SUBSCRIBED_TO_PNL = 137;
     */
    NOT_SUBSCRIBED_TO_PNL = 137
}
/**
 * @generated from protobuf enum ProtoOALimitedRiskMarginCalculationStrategy
 */
export enum ProtoOALimitedRiskMarginCalculationStrategy {
    /**
     * @generated from protobuf enum value: ACCORDING_TO_LEVERAGE = 0;
     */
    ACCORDING_TO_LEVERAGE = 0,
    /**
     * @generated from protobuf enum value: ACCORDING_TO_GSL = 1;
     */
    ACCORDING_TO_GSL = 1,
    /**
     * @generated from protobuf enum value: ACCORDING_TO_GSL_AND_LEVERAGE = 2;
     */
    ACCORDING_TO_GSL_AND_LEVERAGE = 2
}
/**
 * The strategy for choosing which Position to close during a Stop Out
 *
 * @generated from protobuf enum ProtoOAStopOutStrategy
 */
export enum ProtoOAStopOutStrategy {
    /**
     * A Stop Out strategy that closes a Position with the largest Used Margin
     *
     * @generated from protobuf enum value: MOST_MARGIN_USED_FIRST = 0;
     */
    MOST_MARGIN_USED_FIRST = 0,
    /**
     * A Stop Out strategy that closes a Position with the least PnL
     *
     * @generated from protobuf enum value: MOST_LOSING_FIRST = 1;
     */
    MOST_LOSING_FIRST = 1
}
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAAsset$Type extends MessageType<ProtoOAAsset> {
    constructor() {
        super("ProtoOAAsset", [
            { no: 1, name: "assetId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "displayName", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "digits", kind: "scalar", opt: true, T: 5 /*ScalarType.INT32*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAAsset
 */
export const ProtoOAAsset = new ProtoOAAsset$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASymbol$Type extends MessageType<ProtoOASymbol> {
    constructor() {
        super("ProtoOASymbol", [
            { no: 1, name: "symbolId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "digits", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 3, name: "pipPosition", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 4, name: "enableShortSelling", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 5, name: "guaranteedStopLoss", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 6, name: "swapRollover3Days", kind: "enum", opt: true, T: () => ["ProtoOADayOfWeek", ProtoOADayOfWeek] },
            { no: 7, name: "swapLong", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 8, name: "swapShort", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 9, name: "maxVolume", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 10, name: "minVolume", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 11, name: "stepVolume", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 12, name: "maxExposure", kind: "scalar", opt: true, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 13, name: "schedule", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOAInterval },
            { no: 14, name: "commission", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 15, name: "commissionType", kind: "enum", opt: true, T: () => ["ProtoOACommissionType", ProtoOACommissionType] },
            { no: 16, name: "slDistance", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ },
            { no: 17, name: "tpDistance", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ },
            { no: 18, name: "gslDistance", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ },
            { no: 19, name: "gslCharge", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 20, name: "distanceSetIn", kind: "enum", opt: true, T: () => ["ProtoOASymbolDistanceType", ProtoOASymbolDistanceType] },
            { no: 21, name: "minCommission", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 22, name: "minCommissionType", kind: "enum", opt: true, T: () => ["ProtoOAMinCommissionType", ProtoOAMinCommissionType] },
            { no: 23, name: "minCommissionAsset", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 24, name: "rolloverCommission", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 25, name: "skipRolloverDays", kind: "scalar", opt: true, T: 5 /*ScalarType.INT32*/ },
            { no: 26, name: "scheduleTimeZone", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 27, name: "tradingMode", kind: "enum", opt: true, T: () => ["ProtoOATradingMode", ProtoOATradingMode] },
            { no: 28, name: "rolloverCommission3Days", kind: "enum", opt: true, T: () => ["ProtoOADayOfWeek", ProtoOADayOfWeek] },
            { no: 29, name: "swapCalculationType", kind: "enum", opt: true, T: () => ["ProtoOASwapCalculationType", ProtoOASwapCalculationType] },
            { no: 30, name: "lotSize", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 31, name: "preciseTradingCommissionRate", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 32, name: "preciseMinCommission", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 33, name: "holiday", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOAHoliday },
            { no: 34, name: "pnlConversionFeeRate", kind: "scalar", opt: true, T: 5 /*ScalarType.INT32*/ },
            { no: 35, name: "leverageId", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 36, name: "swapPeriod", kind: "scalar", opt: true, T: 5 /*ScalarType.INT32*/ },
            { no: 37, name: "swapTime", kind: "scalar", opt: true, T: 5 /*ScalarType.INT32*/ },
            { no: 38, name: "skipSWAPPeriods", kind: "scalar", opt: true, T: 5 /*ScalarType.INT32*/ },
            { no: 39, name: "chargeSwapAtWeekends", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 40, name: "measurementUnits", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASymbol
 */
export const ProtoOASymbol = new ProtoOASymbol$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOALightSymbol$Type extends MessageType<ProtoOALightSymbol> {
    constructor() {
        super("ProtoOALightSymbol", [
            { no: 1, name: "symbolId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "symbolName", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "enabled", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 4, name: "baseAssetId", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "quoteAssetId", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 6, name: "symbolCategoryId", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 7, name: "description", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 8, name: "sortingNumber", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOALightSymbol
 */
export const ProtoOALightSymbol = new ProtoOALightSymbol$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAArchivedSymbol$Type extends MessageType<ProtoOAArchivedSymbol> {
    constructor() {
        super("ProtoOAArchivedSymbol", [
            { no: 1, name: "symbolId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "utcLastUpdateTimestamp", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "description", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAArchivedSymbol
 */
export const ProtoOAArchivedSymbol = new ProtoOAArchivedSymbol$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOASymbolCategory$Type extends MessageType<ProtoOASymbolCategory> {
    constructor() {
        super("ProtoOASymbolCategory", [
            { no: 1, name: "id", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "assetClassId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "sortingNumber", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOASymbolCategory
 */
export const ProtoOASymbolCategory = new ProtoOASymbolCategory$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAInterval$Type extends MessageType<ProtoOAInterval> {
    constructor() {
        super("ProtoOAInterval", [
            { no: 3, name: "startSecond", kind: "scalar", T: 13 /*ScalarType.UINT32*/ },
            { no: 4, name: "endSecond", kind: "scalar", T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAInterval
 */
export const ProtoOAInterval = new ProtoOAInterval$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOATrader$Type extends MessageType<ProtoOATrader> {
    constructor() {
        super("ProtoOATrader", [
            { no: 1, name: "ctidTraderAccountId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "balance", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "balanceVersion", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "managerBonus", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "ibBonus", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 6, name: "nonWithdrawableBonus", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 7, name: "accessRights", kind: "enum", opt: true, T: () => ["ProtoOAAccessRights", ProtoOAAccessRights] },
            { no: 8, name: "depositAssetId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 9, name: "swapFree", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 10, name: "leverageInCents", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ },
            { no: 11, name: "totalMarginCalculationType", kind: "enum", opt: true, T: () => ["ProtoOATotalMarginCalculationType", ProtoOATotalMarginCalculationType] },
            { no: 12, name: "maxLeverage", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ },
            { no: 13, name: "frenchRisk", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 14, name: "traderLogin", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 15, name: "accountType", kind: "enum", opt: true, T: () => ["ProtoOAAccountType", ProtoOAAccountType] },
            { no: 16, name: "brokerName", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 17, name: "registrationTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 18, name: "isLimitedRisk", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 19, name: "limitedRiskMarginCalculationStrategy", kind: "enum", opt: true, T: () => ["ProtoOALimitedRiskMarginCalculationStrategy", ProtoOALimitedRiskMarginCalculationStrategy] },
            { no: 20, name: "moneyDigits", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ },
            { no: 21, name: "fairStopOut", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 22, name: "stopOutStrategy", kind: "enum", opt: true, T: () => ["ProtoOAStopOutStrategy", ProtoOAStopOutStrategy] }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOATrader
 */
export const ProtoOATrader = new ProtoOATrader$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAPosition$Type extends MessageType<ProtoOAPosition> {
    constructor() {
        super("ProtoOAPosition", [
            { no: 1, name: "positionId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "tradeData", kind: "message", T: () => ProtoOATradeData },
            { no: 3, name: "positionStatus", kind: "enum", T: () => ["ProtoOAPositionStatus", ProtoOAPositionStatus] },
            { no: 4, name: "swap", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "price", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 6, name: "stopLoss", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 7, name: "takeProfit", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 8, name: "utcLastUpdateTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 9, name: "commission", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 10, name: "marginRate", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 11, name: "mirroringCommission", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 12, name: "guaranteedStopLoss", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 13, name: "usedMargin", kind: "scalar", opt: true, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 14, name: "stopLossTriggerMethod", kind: "enum", opt: true, T: () => ["ProtoOAOrderTriggerMethod", ProtoOAOrderTriggerMethod] },
            { no: 15, name: "moneyDigits", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ },
            { no: 16, name: "trailingStopLoss", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAPosition
 */
export const ProtoOAPosition = new ProtoOAPosition$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOATradeData$Type extends MessageType<ProtoOATradeData> {
    constructor() {
        super("ProtoOATradeData", [
            { no: 1, name: "symbolId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "volume", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "tradeSide", kind: "enum", T: () => ["ProtoOATradeSide", ProtoOATradeSide] },
            { no: 4, name: "openTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "label", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 6, name: "guaranteedStopLoss", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 7, name: "comment", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 8, name: "measurementUnits", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 9, name: "closeTimestamp", kind: "scalar", opt: true, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOATradeData
 */
export const ProtoOATradeData = new ProtoOATradeData$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAOrder$Type extends MessageType<ProtoOAOrder> {
    constructor() {
        super("ProtoOAOrder", [
            { no: 1, name: "orderId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "tradeData", kind: "message", T: () => ProtoOATradeData },
            { no: 3, name: "orderType", kind: "enum", T: () => ["ProtoOAOrderType", ProtoOAOrderType] },
            { no: 4, name: "orderStatus", kind: "enum", T: () => ["ProtoOAOrderStatus", ProtoOAOrderStatus] },
            { no: 6, name: "expirationTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 7, name: "executionPrice", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 8, name: "executedVolume", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 9, name: "utcLastUpdateTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 10, name: "baseSlippagePrice", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 11, name: "slippageInPoints", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 12, name: "closingOrder", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 13, name: "limitPrice", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 14, name: "stopPrice", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 15, name: "stopLoss", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 16, name: "takeProfit", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 17, name: "clientOrderId", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 18, name: "timeInForce", kind: "enum", opt: true, T: () => ["ProtoOATimeInForce", ProtoOATimeInForce] },
            { no: 19, name: "positionId", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 20, name: "relativeStopLoss", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 21, name: "relativeTakeProfit", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 22, name: "isStopOut", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 23, name: "trailingStopLoss", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 24, name: "stopTriggerMethod", kind: "enum", opt: true, T: () => ["ProtoOAOrderTriggerMethod", ProtoOAOrderTriggerMethod] }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAOrder
 */
export const ProtoOAOrder = new ProtoOAOrder$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOABonusDepositWithdraw$Type extends MessageType<ProtoOABonusDepositWithdraw> {
    constructor() {
        super("ProtoOABonusDepositWithdraw", [
            { no: 1, name: "operationType", kind: "enum", T: () => ["ProtoOAChangeBonusType", ProtoOAChangeBonusType] },
            { no: 2, name: "bonusHistoryId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "managerBonus", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "managerDelta", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "ibBonus", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 6, name: "ibDelta", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 7, name: "changeBonusTimestamp", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 8, name: "externalNote", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 9, name: "introducingBrokerId", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 10, name: "moneyDigits", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOABonusDepositWithdraw
 */
export const ProtoOABonusDepositWithdraw = new ProtoOABonusDepositWithdraw$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOADepositWithdraw$Type extends MessageType<ProtoOADepositWithdraw> {
    constructor() {
        super("ProtoOADepositWithdraw", [
            { no: 1, name: "operationType", kind: "enum", T: () => ["ProtoOAChangeBalanceType", ProtoOAChangeBalanceType] },
            { no: 2, name: "balanceHistoryId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "balance", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "delta", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "changeBalanceTimestamp", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 6, name: "externalNote", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 7, name: "balanceVersion", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 8, name: "equity", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 9, name: "moneyDigits", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOADepositWithdraw
 */
export const ProtoOADepositWithdraw = new ProtoOADepositWithdraw$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOADeal$Type extends MessageType<ProtoOADeal> {
    constructor() {
        super("ProtoOADeal", [
            { no: 1, name: "dealId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "orderId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "positionId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "volume", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "filledVolume", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 6, name: "symbolId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 7, name: "createTimestamp", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 8, name: "executionTimestamp", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 9, name: "utcLastUpdateTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 10, name: "executionPrice", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 11, name: "tradeSide", kind: "enum", T: () => ["ProtoOATradeSide", ProtoOATradeSide] },
            { no: 12, name: "dealStatus", kind: "enum", T: () => ["ProtoOADealStatus", ProtoOADealStatus] },
            { no: 13, name: "marginRate", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 14, name: "commission", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 15, name: "baseToUsdConversionRate", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 16, name: "closePositionDetail", kind: "message", T: () => ProtoOAClosePositionDetail },
            { no: 17, name: "moneyDigits", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOADeal
 */
export const ProtoOADeal = new ProtoOADeal$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOADealOffset$Type extends MessageType<ProtoOADealOffset> {
    constructor() {
        super("ProtoOADealOffset", [
            { no: 1, name: "dealId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "volume", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "executionTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "executionPrice", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOADealOffset
 */
export const ProtoOADealOffset = new ProtoOADealOffset$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAClosePositionDetail$Type extends MessageType<ProtoOAClosePositionDetail> {
    constructor() {
        super("ProtoOAClosePositionDetail", [
            { no: 1, name: "entryPrice", kind: "scalar", T: 1 /*ScalarType.DOUBLE*/ },
            { no: 2, name: "grossProfit", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "swap", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "commission", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "balance", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 6, name: "quoteToDepositConversionRate", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ },
            { no: 7, name: "closedVolume", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 8, name: "balanceVersion", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 9, name: "moneyDigits", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ },
            { no: 10, name: "pnlConversionFee", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAClosePositionDetail
 */
export const ProtoOAClosePositionDetail = new ProtoOAClosePositionDetail$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOATrendbar$Type extends MessageType<ProtoOATrendbar> {
    constructor() {
        super("ProtoOATrendbar", [
            { no: 3, name: "volume", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "period", kind: "enum", opt: true, T: () => ["ProtoOATrendbarPeriod", ProtoOATrendbarPeriod] },
            { no: 5, name: "low", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 6, name: "deltaOpen", kind: "scalar", opt: true, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 7, name: "deltaClose", kind: "scalar", opt: true, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 8, name: "deltaHigh", kind: "scalar", opt: true, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 9, name: "utcTimestampInMinutes", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOATrendbar
 */
export const ProtoOATrendbar = new ProtoOATrendbar$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAExpectedMargin$Type extends MessageType<ProtoOAExpectedMargin> {
    constructor() {
        super("ProtoOAExpectedMargin", [
            { no: 1, name: "volume", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "buyMargin", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "sellMargin", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAExpectedMargin
 */
export const ProtoOAExpectedMargin = new ProtoOAExpectedMargin$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOATickData$Type extends MessageType<ProtoOATickData> {
    constructor() {
        super("ProtoOATickData", [
            { no: 1, name: "timestamp", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "tick", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOATickData
 */
export const ProtoOATickData = new ProtoOATickData$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOACtidProfile$Type extends MessageType<ProtoOACtidProfile> {
    constructor() {
        super("ProtoOACtidProfile", [
            { no: 1, name: "userId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOACtidProfile
 */
export const ProtoOACtidProfile = new ProtoOACtidProfile$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOACtidTraderAccount$Type extends MessageType<ProtoOACtidTraderAccount> {
    constructor() {
        super("ProtoOACtidTraderAccount", [
            { no: 1, name: "ctidTraderAccountId", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "isLive", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 3, name: "traderLogin", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "lastClosingDealTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "lastBalanceUpdateTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 6, name: "brokerTitleShort", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOACtidTraderAccount
 */
export const ProtoOACtidTraderAccount = new ProtoOACtidTraderAccount$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAAssetClass$Type extends MessageType<ProtoOAAssetClass> {
    constructor() {
        super("ProtoOAAssetClass", [
            { no: 1, name: "id", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "name", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "sortingNumber", kind: "scalar", opt: true, T: 1 /*ScalarType.DOUBLE*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAAssetClass
 */
export const ProtoOAAssetClass = new ProtoOAAssetClass$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOADepthQuote$Type extends MessageType<ProtoOADepthQuote> {
    constructor() {
        super("ProtoOADepthQuote", [
            { no: 1, name: "id", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "size", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "bid", kind: "scalar", opt: true, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "ask", kind: "scalar", opt: true, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOADepthQuote
 */
export const ProtoOADepthQuote = new ProtoOADepthQuote$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAMarginCall$Type extends MessageType<ProtoOAMarginCall> {
    constructor() {
        super("ProtoOAMarginCall", [
            { no: 1, name: "marginCallType", kind: "enum", T: () => ["ProtoOANotificationType", ProtoOANotificationType] },
            { no: 2, name: "marginLevelThreshold", kind: "scalar", T: 1 /*ScalarType.DOUBLE*/ },
            { no: 3, name: "utcLastUpdateTimestamp", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAMarginCall
 */
export const ProtoOAMarginCall = new ProtoOAMarginCall$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAHoliday$Type extends MessageType<ProtoOAHoliday> {
    constructor() {
        super("ProtoOAHoliday", [
            { no: 1, name: "holidayId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "description", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "scheduleTimeZone", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 5, name: "holidayDate", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 6, name: "isRecurring", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 7, name: "startSecond", kind: "scalar", opt: true, T: 5 /*ScalarType.INT32*/ },
            { no: 8, name: "endSecond", kind: "scalar", opt: true, T: 5 /*ScalarType.INT32*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAHoliday
 */
export const ProtoOAHoliday = new ProtoOAHoliday$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOADynamicLeverage$Type extends MessageType<ProtoOADynamicLeverage> {
    constructor() {
        super("ProtoOADynamicLeverage", [
            { no: 1, name: "leverageId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "tiers", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ProtoOADynamicLeverageTier }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOADynamicLeverage
 */
export const ProtoOADynamicLeverage = new ProtoOADynamicLeverage$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOADynamicLeverageTier$Type extends MessageType<ProtoOADynamicLeverageTier> {
    constructor() {
        super("ProtoOADynamicLeverageTier", [
            { no: 1, name: "volume", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "leverage", kind: "scalar", T: 5 /*ScalarType.INT32*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOADynamicLeverageTier
 */
export const ProtoOADynamicLeverageTier = new ProtoOADynamicLeverageTier$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProtoOAPositionUnrealizedPnL$Type extends MessageType<ProtoOAPositionUnrealizedPnL> {
    constructor() {
        super("ProtoOAPositionUnrealizedPnL", [
            { no: 1, name: "positionId", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "grossUnrealizedPnL", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "netUnrealizedPnL", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message ProtoOAPositionUnrealizedPnL
 */
export const ProtoOAPositionUnrealizedPnL = new ProtoOAPositionUnrealizedPnL$Type();
