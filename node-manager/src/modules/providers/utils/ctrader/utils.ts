import {
  ProtoOAOrderType,
  ProtoOAPayloadType,
  ProtoOATimeInForce,
} from 'src/modules/providers/utils/ctrader/interfaces';
import {
  ProtoOASymbolByIdReq,
  ProtoOASymbolByIdRes,
} from 'src/modules/providers/utils/ctrader/messages';
import {
  ORDER_TYPE_TIME,
  ORDER_TYPES,
} from 'src/modules/providers/utils/enums.utils';
import { RawData, WebSocket } from 'ws';
import * as crypto from 'crypto';

export const sendCtraderApiRequest = async <R = any>(
  type: ProtoOAPayloadType,
  payload: any,
  client: WebSocket,
  disableTimeoutError = false,
) => {
  const req = createWsPayload(payload, type);
  const res = await sendMessage<R>(req, client, disableTimeoutError);
  return res;
};

const createWsPayload = (payload: any, payloadType: ProtoOAPayloadType) => {
  delete payload.payloadType;
  return {
    clientMsgId: crypto.randomUUID(),
    payloadType,
    payload,
  };
};

const sendMessage = <T = any>(
  request: any,
  client: WebSocket,
  disableTimeoutError = false,
): Promise<T> => {
  return new Promise((res, rej) => {
    const handleMessage = (data: RawData) => {
      const message = data.toString();
      const response = JSON.parse(message);

      // console.log(
      //   '<======================',
      //   response,
      //   'ctrader',
      //   '=======================>',
      // );

      if (
        response?.payloadType === ProtoOAPayloadType.PROTO_OA_ERROR_RES ||
        response?.payloadType === ProtoOAPayloadType.PROTO_OA_ORDER_ERROR_EVENT
      ) {
        rej(new Error(JSON.stringify(response?.payload ?? {})));
      } else if (
        response.clientMsgId === request.clientMsgId ||
        response?.payloadType === ProtoOAPayloadType.PROTO_OA_SPOT_EVENT
      ) {
        res(response?.payload);  
      }
      removeListeners();
    };

    const removeListeners = () => {
      client.removeListener('message', handleMessage);
      client.removeListener('error', handleError);
    };

    const handleError = (err: any) => {
      rej(err);
      removeListeners();
    };

    client.addListener('message', handleMessage);
    client.addListener('error', handleError);
    client.send(JSON.stringify(request));

    //timeout
    setTimeout(() => {
      removeListeners();
      if (disableTimeoutError === false) rej(new Error('Timeout'));
      else res(undefined);
    }, 10000);
  });
};

export const getSymbolFromId = async (
  symbolId: bigint,
  client: WebSocket,
  accountId: bigint,
) => {
  return await sendCtraderApiRequest<ProtoOASymbolByIdRes>(
    ProtoOAPayloadType.PROTO_OA_SYMBOL_BY_ID_REQ,
    ProtoOASymbolByIdReq.create({
      ctidTraderAccountId: accountId,
      symbolId: [symbolId],
    }),
    client,
  );
};

export function decodePrice(raw: number | bigint, digits: number): number {
  const p = Number(raw) / 1e5;
  if (!Number.isNaN(p)) return Number(p.toFixed(digits) ?? 0);
  else return 0;
}

export const CTRADER_TO_MT5_ORDER_TYPE_MAP = (
  isBuy: boolean,
  type: ProtoOAOrderType,
) => {
  const maps = {
    [ProtoOAOrderType.LIMIT]: isBuy
      ? ORDER_TYPES.ORDER_TYPE_BUY_LIMIT
      : ORDER_TYPES.ORDER_TYPE_SELL_LIMIT,
    [ProtoOAOrderType.STOP]: isBuy
      ? ORDER_TYPES.ORDER_TYPE_BUY_STOP
      : ORDER_TYPES.ORDER_TYPE_SELL_STOP,
    [ProtoOAOrderType.STOP_LIMIT]: isBuy
      ? ORDER_TYPES.ORDER_TYPE_BUY_STOP
      : ORDER_TYPES.ORDER_TYPE_BUY_LIMIT,
    [ProtoOAOrderType.MARKET]: isBuy
      ? ORDER_TYPES.ORDER_TYPE_BUY
      : ORDER_TYPES.ORDER_TYPE_SELL,
  };
  return maps[type];
};
export const CTRADER_TO_MT5_TIME_MAP = {
  [ProtoOATimeInForce.GOOD_TILL_CANCEL]: ORDER_TYPE_TIME.ORDER_TIME_GTC,
  [ProtoOATimeInForce.GOOD_TILL_DATE]: ORDER_TYPE_TIME.ORDER_TIME_SPECIFIED,
  [ProtoOATimeInForce.UNSPECIFIED$]: ORDER_TYPE_TIME.ORDER_TIME_GTC,
};
