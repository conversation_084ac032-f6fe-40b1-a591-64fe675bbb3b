import * as grpc from '@grpc/grpc-js';
import { MemCache } from 'src/modules/terminals/terminals.service';
import {
  bridgeRpcServiceDefinition,
  IBridgeRpcService,
} from 'src/utils/bridge/bridge.grpc-server';

import { generateRandomId } from 'src/utils/functions';
const startWsPort = Number(process.env.START_WS_PORT) || 8010;
const startRpcPort = Number(process.env.START_RPC_PORT) || 50050;

export const startServer = async (
  service: IBridgeRpcService,
  metadata: any,
) => {
  const auth_interceptor: grpc.ServerInterceptor = (c, d) => {
    return new grpc.ServerInterceptingCall(d);
  };

  const server = new grpc.Server({ interceptors: [auth_interceptor] });
  server.addService(bridgeRpcServiceDefinition, service);

  const findFreePort = () => {
    const lastPort = MemCache.activePorts[MemCache.activePorts.length - 1];
    const newPort = lastPort === undefined ? startWsPort : lastPort + 1;
    const rpcPort = startRpcPort + (newPort - startWsPort);
    return { newPort, rpcPort };
  };

  const { newPort, rpcPort } = findFreePort();
  const providerId = generateRandomId();

  MemCache.activePorts.push(newPort);
  MemCache.totalConnected++;
  MemCache.clients[providerId] = {
    rpc_port: rpcPort,
    bridge_port: newPort,
  };
  MemCache.providers[providerId] = {
    id: providerId,
    metadata,
  };

  await new Promise((res, rej) => {
    server.bindAsync(
      `0.0.0.0:${rpcPort}`,
      grpc.ServerCredentials.createInsecure(),
      (err: Error | null, port: number) => {
        if (err) {
          console.error(`Server error: ${err.message}`);
          rej(err);
        } else {
          console.log(`Server bound on port: ${port}`);
          res(port);
        }
      },
    );
  });

  return {
    rpcPort,
    server,
    providerId,
  };
};

export const assert = (value: any, error: string) => {
  if (value === null || value === undefined || value === '') {
    throw new Error(error);
  }
};
