import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsBoolean, IsN<PERSON>ber } from 'class-validator';

export class InitHyperliquidProviderDto {
  @IsString()
  @IsOptional()
  private_key: string;

  @IsString()
  @IsNotEmpty()
  wallet_address: string;

  @IsString()
  @IsNotEmpty()
  auth_token: string;

  @IsBoolean()
  @IsOptional()
  testnet: boolean;
}
export class InitCtraderProviderDto {
  @IsString()
  @IsNotEmpty()
  client_id: string;

  @IsString()
  @IsNotEmpty()
  client_secret: string;

  @IsString()
  @IsNotEmpty()
  access_token: string;

  @IsNumber()
  @IsNotEmpty()
  account_id: bigint;

  @IsBoolean()
  @IsOptional()
  is_demo: boolean;
}

export class KillProviderDto{
  @IsString()
  @IsNotEmpty()
  provider_id: string;
}


