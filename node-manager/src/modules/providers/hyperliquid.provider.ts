import { IBridgeRpcService } from 'src/utils/bridge/bridge.grpc-server';

import { InternalServerErrorException } from '@nestjs/common';
import {
  AllMids,
  FrontendOpenOrders,
  Hyperliquid,
  Meta,
  Order,
  OrderRequest,
  UserFills,
  WsBook,
} from 'hyperliquid';
import { startServer } from 'src/modules/providers/utils';
import {
  AccountOrdersType,
  AccountPositionType,
} from 'src/utils/bridge/bridge';
import { hexToInt, intToHex } from 'src/utils/functions';
import { ORDER_TYPES } from './utils/enums.utils';
import { IOrderStatus } from './utils/hyperliquid/interfaces';

export const StartHyperliquidProviderServer = async (
  privateKey: string,
  walletAddress: string,
  auth_token: string,
  testnet = false,
) => {
  const sdk = new Hyperliquid({
    privateKey,
    testnet,
    enableWs: true,
    walletAddress,
  });

  try {
    await sdk.connect();
  } catch (e) {
    throw new InternalServerErrorException(e);
  }

  const bridgeService = getService(sdk, walletAddress, testnet);
  return await startServer(bridgeService, {
    privateKey,
    walletAddress,
    testnet,
    auth_token,
  });
};

const getService = (sdk: Hyperliquid, user: string, isTestnet = false) => {
  const precision = 4;
  const tickConfig = {
    coin: 'BTC-PERP',
    isTicking: false,
  };
  const bridgeService: IBridgeRpcService = {
    placeTrade: (request, callback) => {
      (async () => {
        const {
          actionType,
          symbol,
          volume,
          price,
          deviation,
          magic,
          takeProfit,
          stopLoss,
        } = request.request;

        try {
          const slippagePrice = async (
            name: string,
            isBuy: boolean,
            slippage: number,
            px?: number,
          ) => {
            const mid = await getCoinPrice(sdk, name, true);
            if (mid) {
              px = mid;
              px *= isBuy ? 1 + slippage : 1 - slippage;
              px = Number.parseFloat(px.toFixed(precision));
              return px;
            }
          };

          const cloid = magic ? intToHex(magic) : undefined;
          const defaultSlippage = deviation ?? 0.05;
          const action = Number(actionType) as ORDER_TYPES;
          const payload: Order = {
            is_buy: true,
            coin: symbol,
            sz: volume, // amount of the asset you want to buy example 40 units of base coin
            limit_px: 0,
            order_type: {
              limit: {
                tif: 'Ioc',
              },
            },
            reduce_only: false,
            cloid,
          };

          let stopLossPayload: Order;
          let takeProfitPayload: Order;
          let grouping: OrderRequest['grouping'];

          // determine buy direction
          if (
            [
              ORDER_TYPES.ORDER_TYPE_BUY,
              ORDER_TYPES.ORDER_TYPE_BUY_LIMIT,
              ORDER_TYPES.ORDER_TYPE_BUY_STOP,
            ].includes(action)
          ) {
            payload.is_buy = true;
          } else {
            payload.is_buy = false;
          }

          // normal or pending orders
          if (
            [ORDER_TYPES.ORDER_TYPE_BUY, ORDER_TYPES.ORDER_TYPE_SELL].includes(
              action,
            )
          ) {
            const price = await slippagePrice(
              payload.coin,
              payload.is_buy,
              Number(defaultSlippage),
            );
            payload.limit_px = price;
            payload.order_type.limit.tif = 'Ioc';

            if (stopLoss || takeProfit) {
              grouping = 'normalTpsl';
            }
          } else {
            payload.limit_px = Number.parseFloat(price.toFixed(precision));
            payload.order_type.limit.tif = 'Gtc';
            if (stopLoss || takeProfit) {
              grouping = 'positionTpsl';
            }
          }

          if (stopLoss) {
            stopLossPayload = {
              ...{ ...payload, is_buy: !payload.is_buy, reduce_only: true },
              order_type: {
                trigger: {
                  isMarket: true,
                  tpsl: 'sl',
                  triggerPx: Number.parseFloat(stopLoss.toFixed(precision)),
                },
              },
              cloid,
            };
          }

          if (takeProfit) {
            takeProfitPayload = {
              ...{ ...payload, is_buy: !payload.is_buy, reduce_only: true },
              order_type: {
                trigger: {
                  isMarket: true,
                  tpsl: 'tp',
                  triggerPx: Number.parseFloat(takeProfit.toFixed(precision)),
                },
              },
              cloid,
            };
          }

          const order: IOrderStatus = await sdk.exchange.placeOrder({
            orders: [
              payload,
              ...[stopLossPayload, takeProfitPayload].filter(Boolean),
            ],
            grouping: grouping ?? 'na',
          });

          const oid =
            order.response.data.statuses[0].filled?.oid ??
            order.response.data.statuses[0].resting?.oid;

          callback(null, {
            message: 'Trade placed',
            status: BigInt(1),
            ticket: oid.toString(),
          });
        } catch (e) {
          callback(e);
        }
      })();
    },
    closeTrade: (request, callback) => {
      (async () => {
        const { ticket } = request.request;

        // get current position
        const o = await sdk.info.getOrderStatus(user, Number(ticket));

        const state = await sdk.info.perpetuals.getClearinghouseState(user);
        if (o) {
          const order = o.order.order as FrontendOpenOrders[0];
          await sdk.custom.marketClose(order.coin);

          callback(null, { message: 'Order closed', status: BigInt(1) });
        } else {
          callback(new Error('Order not found'));
        }
      })();
    },
    getOrders: (_, callback) => {
      (async () => {
        try {
          const openOrders = await sdk.info.getFrontendOpenOrders(user);
          // get orders for stop loss and take profit
          const filteredTriggers = openOrders.filter((o) => o.isTrigger);
          const groupedTriggers = filteredTriggers.reduce(
            (acc, curr) => {
              const coin = curr.coin;
              const type = curr.orderType;

              if (type == 'Take Profit Market') {
                acc[coin] = {
                  ...(acc[coin] ?? {}),
                  tp: curr,
                };
              } else if (type == 'Stop Market') {
                acc[coin] = {
                  ...(acc[coin] ?? {}),
                  sl: curr,
                };
              }
              return acc;
            },
            {} as Record<
              string,
              { sl?: FrontendOpenOrders[0]; tp?: FrontendOpenOrders[0] }
            >,
          );

          // find all orders that are not triggers (sl & tp)
          const filteredOpenOrders = openOrders.filter((o) => !o.isTrigger);

          const allOrders = await sdk.info.getHistoricalOrders(user);
          const filteredAllOrders = allOrders.filter(
            (o) =>
              filteredOpenOrders.findIndex((f) => f.oid === o.order.oid) > -1,
          );

          for (const { order } of filteredAllOrders) {
            await getCoinPrice(sdk, order.coin);
          }

          const orders = await Promise.all(
            filteredAllOrders.map(
              async ({ order }) =>
                ({
                  comment: 'None',
                  magic: BigInt(order.cloid ? hexToInt(order.cloid) : 0),
                  priceCurrent: await getCoinPrice(sdk, order.coin),
                  priceOpen: Number.parseFloat(order.limitPx),
                  symbol: order.coin,
                  ticket: BigInt(order.oid),
                  timeExpiration: BigInt(0),
                  volume: Number.parseFloat(order.sz),
                  timeSetup: BigInt(0),
                  type: BigInt(
                    order.side === 'B'
                      ? ORDER_TYPES.ORDER_TYPE_BUY_LIMIT
                      : ORDER_TYPES.ORDER_TYPE_SELL_LIMIT,
                  ),
                  stopLoss: Number.parseFloat(
                    groupedTriggers?.[order.coin]?.sl?.triggerPx ?? '0.0',
                  ),
                  takeProfit: Number.parseFloat(
                    groupedTriggers?.[order.coin]?.tp?.triggerPx ?? '0.0',
                  ),
                }) as AccountOrdersType,
            ),
          );

          callback(null, {
            orders,
            totalOrders: BigInt(filteredAllOrders.length),
          });
        } catch (e) {
          callback(e);
        }
      })();
    },
    getPositions: (_, callback) => {
      (async () => {
        try {
          const userFills = await sdk.info.getUserFills(user);
          function getOpenPositions(fills: UserFills): UserFills {
            type Fill = UserFills[0];
            // Group fills by coin
            const coinGroups: { [coin: string]: Fill[] } = {};
            for (const fill of fills) {
              if (!coinGroups[fill.coin]) {
                coinGroups[fill.coin] = [];
              }
              coinGroups[fill.coin].push(fill);
            }

            const openPositions: Fill[] = [];

            // Process each coin group separately
            for (const coin in coinGroups) {
              const coinFills = coinGroups[coin].slice();
              // Sort the fills in ascending order of time
              coinFills.sort((a, b) => a.time - b.time);

              // Use a FIFO queue to match opens with closes.
              type OpenEntry = { fill: Fill; remainingSize: number };
              const openQueue: OpenEntry[] = [];

              for (const fill of coinFills) {
                if (fill.dir === 'Open Long') {
                  openQueue.push({ fill, remainingSize: parseFloat(fill.sz) });
                } else if (fill.dir === 'Close Long') {
                  // Use the close order to offset open orders
                  let closeSize = parseFloat(fill.sz);
                  while (closeSize > 0 && openQueue.length > 0) {
                    const openEntry = openQueue[0];
                    if (openEntry.remainingSize > closeSize) {
                      openEntry.remainingSize -= closeSize;
                      closeSize = 0;
                    } else {
                      closeSize -= openEntry.remainingSize;
                      openQueue.shift();
                    }
                  }
                }
              }

              // Map any remaining open entries back to fills with the updated size.
              for (const entry of openQueue) {
                openPositions.push({
                  ...entry.fill,
                  sz: entry.remainingSize.toString(),
                });
              }
            }

            return openPositions;
          }
          const info = await sdk.info.perpetuals.getClearinghouseState(user);
          const agg = getOpenPositions(userFills);

          // get orders for stop loss and take profit
          const openOrders = await sdk.info.getFrontendOpenOrders(user);
          const filteredTriggers = openOrders.filter((o) => o.isTrigger);
          const groupedTriggers = filteredTriggers.reduce(
            (acc, curr) => {
              const coin = curr.coin;
              const type = curr.orderType;

              if (type == 'Take Profit Market') {
                acc[coin] = {
                  ...(acc[coin] ?? {}),
                  tp: curr,
                };
              } else if (type == 'Stop Market') {
                acc[coin] = {
                  ...(acc[coin] ?? {}),
                  sl: curr,
                };
              }
              return acc;
            },
            {} as Record<
              string,
              { sl?: FrontendOpenOrders[0]; tp?: FrontendOpenOrders[0] }
            >,
          );

          for (const order of agg) {
            await getCoinPrice(sdk, order.coin);
          }

          const positions = await Promise.all(
            agg.map(
              async (order) =>
                ({
                  comment: '',
                  magic: BigInt(
                    (order as any).cloid ? hexToInt((order as any).cloid) : 0,
                  ),
                  currentPrice: await getCoinPrice(sdk, order.coin),
                  openPrice: Number.parseFloat(order.px),
                  symbol: order.coin,
                  ticket: BigInt(order.oid),
                  timeExpiration: BigInt(0),
                  volume: Number.parseFloat(order.sz),
                  timeSetup: BigInt(0),
                  type: BigInt(
                    order.side === 'B'
                      ? ORDER_TYPES.ORDER_TYPE_BUY_LIMIT
                      : ORDER_TYPES.ORDER_TYPE_SELL_LIMIT,
                  ),
                  stopLoss: Number.parseFloat(
                    groupedTriggers?.[order.coin]?.sl?.triggerPx ?? '0.0',
                  ),
                  takeProfit: Number.parseFloat(
                    groupedTriggers?.[order.coin]?.tp?.triggerPx ?? '0.0',
                  ),
                  profit: Number.parseFloat(
                    info.assetPositions.find(
                      (p) => p.position.coin === order.coin,
                    )?.position.unrealizedPnl ?? '0',
                  ),
                }) as AccountPositionType,
            ),
          );

          callback(null, { totalPositions: BigInt(agg.length), positions });
        } catch (e) {
          callback(e);
        }
      })();
    },
    getTerminalError: (_, callback) => {
      (async () => {
        callback(null, { status: BigInt(1), message: 'OK' });
      })();
    },
    getSymbolTick: (request, callback) => {
      (async () => {
        try {
          const { symbol } = request.request;
          const l2 = await sdk.info.getL2Book(symbol);
          callback(null, {
            bid: Number.parseFloat(l2.levels[0][0].px),
            ask: Number.parseFloat(l2.levels[1][0].px),
            time: BigInt(Math.round((l2 as any).time / 1000)),
          });
        } catch (e) {
          callback(e);
        }
      })();
    },
    getTicksFrom: (request, callback) => {
      (async () => {
        try {
          const { length, startDate, symbol } = request.request;
          const l2 = await sdk.info.getCandleSnapshot(
            symbol,
            '1m',
            Number(startDate),
            Date.now(),
          );

          const sliced =
            l2?.length > Number(length) ? l2.slice(0, Number(length)) : l2;
          callback(null, {
            totalTicks: BigInt(sliced.length),
            ticks: sliced.map((t) => ({
              ask: Number.parseFloat(t.o),
              bid: Number.parseFloat(t.o),
              time: BigInt(Math.round(t.t / 1000)),
            })),
          });
        } catch (e) {
          callback(e);
        }
      })();
    },
    getTicksRange: (request, callback) => {
      (async () => {
        try {
          const { startDate, endDate, length, symbol } = request.request;
          const l2 = await sdk.info.getCandleSnapshot(
            symbol,
            '1m',
            Number(startDate),
            Number(endDate),
          );
          const sliced =
            l2?.length > Number(length) ? l2.slice(0, Number(length)) : l2;

          callback(null, {
            totalTicks: BigInt(sliced.length),
            ticks: sliced.map((t) => ({
              ask: Number.parseFloat(t.o),
              bid: Number.parseFloat(t.o),
              time: BigInt(Math.round(t.t / 1000)),
            })),
          });
        } catch (e) {
          callback(e);
        }
      })();
    },
    getAccount: (_, callback) => {
      (async () => {
        try {
          const portfolio =
            await sdk.info.perpetuals.getClearinghouseState(user);

          const totalProfit = portfolio.assetPositions.reduce((acc, curr) => {
            return Number.parseFloat(curr.position?.unrealizedPnl) + acc;
          }, 0.0);

          const equity = Number.parseFloat(
            portfolio.crossMarginSummary.accountValue,
          );
          const margin = Number.parseFloat(
            portfolio.crossMarginSummary.totalMarginUsed,
          );
          const freeMargin = equity - margin;
          const marginLevel = (margin / equity) * 100;
          const balance = Number.parseFloat(portfolio.withdrawable);
          const tradeMode = BigInt(isTestnet ? 0 : 2);

          callback(null, {
            balance,
            equity,
            margin,
            freeMargin,
            marginLevel,
            profit: totalProfit,
            server: `HyperliquidL-${isTestnet ? 'Testnet' : 'Mainnet'}`,
            tradeMode,
          });
        } catch (e) {
          callback(e);
        }
      })();
    },
    getAvailableSymbols: (_, callback) => {
      (async () => {
        try {
          const mids = await sdk.info.getAllMids();
          const midsKeys = Object.keys(mids);
          const symbols = midsKeys.filter((s) => s.includes('-PERP'));

          const info = await sdk.info.perpetuals.getMeta();
          const assets = info.universe.reduce(
            (acc, curr) => {
              acc[curr.name] = curr;
              return acc;
            },
            {} as Record<string, Meta['universe'][0]>,
          );

          callback(null, {
            totalSymbols: BigInt(symbols.length),
            symbols: symbols.map((s) => ({
              description: `${s}-hyperliquid perpetual asset`,
              digits: BigInt(assets[s]?.szDecimals ?? 0),
              name: s,
              path: 'Crypto\\',
              time: BigInt(Date.now()),
              spread: 0.0,
            })),
          });
        } catch (e) {
          callback(e);
        }
      })();
    },
    getTradeHistory: (request, callback) => {
      (async () => {
        try {
          const { endDate, startDate } = request.request;
          const history = await sdk.info.getUserFillsByTime(
            user,
            Number(startDate),
            Number(endDate),
          );
          const filtered = history.filter((h) =>
            h.dir.toLowerCase().includes('close'),
          );

          callback(null, {
            totalDeals: BigInt(filtered.length),
            deals: filtered.map((h) => ({
              comment: '',
              magic: BigInt((h as any).cloid ? hexToInt((h as any).cloid) : 0),
              profit: Number.parseFloat(h.closedPnl),
              entry: Number.parseFloat(h.px),
              price: Number.parseFloat(h.px),
              symbol: h.coin,
              ticket: BigInt(h.oid),
              time: BigInt(h.time),
              volume: Number.parseFloat(h.sz),
              order: BigInt(0),
              commission: Number.parseFloat((h as any).fee ?? '0'),
              swap: 0,
              type: BigInt(
                h.side === 'sell'
                  ? ORDER_TYPES.ORDER_TYPE_SELL
                  : ORDER_TYPES.ORDER_TYPE_BUY,
              ),
            })),
          });
        } catch (e) {
          callback(e);
        }
      })();
    },
    modifyTrade: (request, callback) => {
      (async () => {
        try {
          const { stopLoss, takeProfit, ticket, price } = request.request;

          const {
            order: { order },
          } = (await sdk.info.getOrderStatus(user, Number(ticket))) as {
            order: { order: FrontendOpenOrders[0] };
          };

          if (order) {
            // get orders for stop loss and take profit
            const openOrders = await sdk.info.getFrontendOpenOrders(user);
            const filteredTriggers = openOrders.filter((o) => o.isTrigger);
            const groupedTriggers = filteredTriggers.reduce(
              (acc, curr) => {
                const coin = curr.coin;
                const type = curr.orderType;

                if (type == 'Take Profit Market') {
                  acc[coin] = {
                    ...(acc[coin] ?? {}),
                    tp: curr,
                  };
                } else if (type == 'Stop Market') {
                  acc[coin] = {
                    ...(acc[coin] ?? {}),
                    sl: curr,
                  };
                }
                return acc;
              },
              {} as Record<
                string,
                { sl?: FrontendOpenOrders[0]; tp?: FrontendOpenOrders[0] }
              >,
            );

            const coin = order.coin;

            const sl = groupedTriggers[coin]?.sl;
            const tp = groupedTriggers[coin]?.tp;

            const newOrders: Order[] = [];
            const updateOrders: { oid: number; order: Order }[] = [];

            if (price > 0) {
              updateOrders.push({
                oid: order.oid,
                order: {
                  coin: order.coin,
                  is_buy: order.side === 'buy',
                  limit_px: price,
                  sz: Number.parseFloat(order.sz),

                  order_type: {
                    limit: {
                      tif: 'Gtc',
                    },
                  },
                  reduce_only: true,
                },
              });
            }

            if ((stopLoss === 0 && sl) || (takeProfit === 0 && tp)) {
              if (sl) await sdk.exchange.cancelOrder([{ coin, o: sl.oid }]);
              if (tp) await sdk.exchange.cancelOrder([{ coin, o: tp.oid }]);
            } else if (stopLoss > 0 || takeProfit > 0) {
              if (sl) {
                updateOrders.push({
                  oid: sl.oid,
                  order: {
                    coin: sl.coin,
                    is_buy: sl.side === 'buy',
                    limit_px: Number.parseFloat(sl.limitPx),
                    sz: Number.parseFloat(sl.sz),
                    order_type: {
                      trigger: {
                        isMarket: true,
                        tpsl: 'sl',
                        triggerPx: Number.parseFloat(
                          stopLoss.toFixed(precision),
                        ),
                      },
                    },
                    reduce_only: true,
                  },
                });
              } else if (stopLoss) {
                // create
                newOrders.push({
                  coin: order.coin,
                  is_buy: !(order.side === 'buy'),
                  limit_px: Number.parseFloat(order.limitPx),
                  reduce_only: true,
                  sz: Number.parseFloat(order.sz),
                  order_type: {
                    trigger: {
                      isMarket: true,
                      tpsl: 'sl',
                      triggerPx: Number.parseFloat(stopLoss.toFixed(precision)),
                    },
                  },
                });
              }

              if (tp) {
                updateOrders.push({
                  oid: tp.oid,
                  order: {
                    coin: tp.coin,
                    is_buy: tp.side === 'buy',
                    limit_px: Number.parseFloat(tp.limitPx),
                    sz: Number.parseFloat(tp.sz),
                    order_type: {
                      trigger: {
                        isMarket: true,
                        tpsl: 'tp',
                        triggerPx: Number.parseFloat(
                          takeProfit.toFixed(precision),
                        ),
                      },
                    },
                    reduce_only: true,
                  },
                });
              } else if (takeProfit) {
                console.log(order);
                // create
                newOrders.push({
                  coin: order.coin,
                  is_buy: !(order.side === 'buy'),
                  limit_px: Number.parseFloat(order.limitPx),
                  reduce_only: true,
                  sz: Number(order.sz),
                  order_type: {
                    trigger: {
                      isMarket: true,
                      tpsl: 'tp',
                      triggerPx: Number.parseFloat(
                        takeProfit.toFixed(precision),
                      ),
                    },
                  },
                });
              }
            }

            if (updateOrders?.length > 0)
              await sdk.exchange.batchModifyOrders(updateOrders);

            if (newOrders?.length > 0) {
              const res = await sdk.exchange.placeOrder({
                orders: newOrders,
                grouping: 'na',
              });
            }
            callback(null, { message: 'Order modified', status: BigInt(1) });
          }
        } catch (e) {
          callback(e);
        }
      })();
    },
    manageSymbol: (request, callback) => {
      (async () => {
        const { symbol } = request.request;
        tickConfig.coin = symbol;
        callback(null, { message: 'Symbol modified', status: BigInt(1) });
      })();
    },
    tickStart: (stream) => {
      (async () => {
        let lastWrite = Date.now();
        let { rate, symbol } = stream.request;

        if (tickConfig.isTicking && tickConfig.coin === symbol) {
          return;
        }

        if (tickConfig.isTicking && tickConfig.coin !== symbol) {
          tickConfig.coin = symbol;
          return;
        }

        tickConfig.coin = symbol;
        tickConfig.isTicking = true;

        const subCallBack = async (
          data: WsBook & {
            coin: string;
          },
        ) => {
          if (tickConfig.coin !== symbol) {
            await sdk.subscriptions.unsubscribeFromL2Book(symbol);
            symbol = tickConfig.coin;
            await sdk.subscriptions.subscribeToL2Book(symbol, subCallBack);

          }
          if (tickConfig.isTicking !== true) {
            await sdk.subscriptions.unsubscribeFromL2Book(symbol);
            if (!stream.closed) stream.end();
          }

          const ask = Number.parseFloat(data.levels[1][0].px);
          const bid = Number.parseFloat(data.levels[0][0].px);
          const time = BigInt(Math.round(data.time / 1000));

          if (Date.now() - lastWrite > rate * 1000) {
            stream.write({
              ask: ask === 0 ? bid : ask,
              bid: bid === 0 ? ask : bid,
              time,
            });
            lastWrite = Date.now();
          }
        };

        await sdk.subscriptions.subscribeToL2Book(symbol, subCallBack);

        stream.on('close', async () => {
          console.log('Stream closed');
          await sdk.subscriptions.unsubscribeFromL2Book(symbol);
        });

        stream.on('error', async () => {
          await sdk.subscriptions.unsubscribeFromL2Book(symbol);
        });
      })();
    },
    tickStop: (_, callback) => {
      (async () => {
        tickConfig.isTicking = false;
        callback(null, { message: 'Stopped tick', status: BigInt(1) });
      })();
    },
  };

  return bridgeService;
};

const coinCache: {
  mids: AllMids;
} = { mids: null };

const getCoinPrice = async (
  sdk: Hyperliquid,
  coin: string,
  refresh = false,
) => {
  if (refresh || coinCache.mids === null) {
    coinCache.mids = await sdk.info.getAllMids();
  }

  const mid = coinCache.mids[coin];
  if (mid) {
    const price = Number.parseFloat(mid);
    return price;
  }
  return 0.0;
};
