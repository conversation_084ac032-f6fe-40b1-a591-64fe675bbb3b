/* eslint-disable @typescript-eslint/no-unused-vars */
import { PreconditionFailedException } from '@nestjs/common';
import { assert, startServer } from 'src/modules/providers/utils';
import {
  ProtoOAOrderStatus,
  ProtoOAOrderType,
  ProtoOAPayloadType,
  ProtoOAQuoteType,
  ProtoOATradeSide,
  ProtoOATrendbarPeriod,
} from 'src/modules/providers/utils/ctrader/interfaces';
import {
  ProtoOAAccountAuthReq,
  ProtoOAAccountAuthRes,
  ProtoOAAmendOrderReq,
  ProtoOAAmendPositionSLTPReq,
  ProtoOAApplicationAuthReq,
  ProtoOAAssetClassListReq,
  ProtoOAAssetClassListRes,
  ProtoOAClosePositionReq,
  ProtoOADealListByPositionIdReq,
  ProtoOADealListByPositionIdRes,
  ProtoOADealListReq,
  ProtoOADealListRes,
  ProtoOAExecutionEvent,
  ProtoOAGetAccountListByAccessTokenReq,
  ProtoOAGetAccountListByAccessTokenRes,
  ProtoOAGetPositionUnrealizedPnLReq,
  ProtoOAGetPositionUnrealizedPnLRes,
  ProtoOAGetTickDataReq,
  ProtoOAGetTickDataRes,
  ProtoOAGetTrendbarsReq,
  ProtoOAGetTrendbarsRes,
  ProtoOANewOrderReq,
  ProtoOAOrderDetailsReq,
  ProtoOAOrderDetailsRes,
  ProtoOAOrderListByPositionIdReq,
  ProtoOAOrderListByPositionIdRes,
  ProtoOAOrderListReq,
  ProtoOAOrderListRes,
  ProtoOAReconcileReq,
  ProtoOAReconcileRes,
  ProtoOASpotEvent,
  ProtoOASubscribeSpotsReq,
  ProtoOASymbolsListReq,
  ProtoOASymbolsListRes,
  ProtoOATraderRes,
  ProtoOAUnsubscribeSpotsReq,
} from 'src/modules/providers/utils/ctrader/messages';
import {
  CTRADER_TO_MT5_ORDER_TYPE_MAP,
  CTRADER_TO_MT5_TIME_MAP,
  decodePrice,
  getSymbolFromId,
  sendCtraderApiRequest,
} from 'src/modules/providers/utils/ctrader/utils';
import {
  ORDER_TYPE_TIME,
  ORDER_TYPES,
} from 'src/modules/providers/utils/enums.utils';
import {
  GetOrdersResponse,
  GetPositionsResponse,
  GetTradeHistoryRequest,
  GetTradeHistoryResponse,
} from 'src/utils/bridge/bridge';
import { IBridgeRpcService } from 'src/utils/bridge/bridge.grpc-server';
import { delay } from 'src/utils/functions';
import { RawData, WebSocket } from 'ws';

export const StartCtraderProviderServer = async (
  clientId: string,
  clientSecret: string,
  accessToken: string,
  accountId: bigint,
  isDemo = false,
) => {
  const url = `wss://${isDemo ? 'demo' : 'live'}.ctraderapi.com:5036`;
  const client = new WebSocket(url);
  const opened = new Promise((resolve, reject) => {
    client.on('open', () => {
      resolve(true);
    });
    client.on('error', (err) => {
      reject(err);
    });
  });
  await opened;

  try {
    // app auth
    await sendCtraderApiRequest<ProtoOAAccountAuthRes>(
      ProtoOAPayloadType.PROTO_OA_APPLICATION_AUTH_REQ,
      ProtoOAApplicationAuthReq.create({
        clientId,
        clientSecret,
      }),
      client,
    );

    // get accounts
    const accountsRes =
      await sendCtraderApiRequest<ProtoOAGetAccountListByAccessTokenRes>(
        ProtoOAPayloadType.PROTO_OA_GET_ACCOUNTS_BY_ACCESS_TOKEN_REQ,
        ProtoOAGetAccountListByAccessTokenReq.create({
          accessToken,
        }),
        client,
      );

    // account auth
    const account =
      accountsRes?.ctidTraderAccount?.find(
        (a) => a.ctidTraderAccountId == accountId,
      ) ?? accountsRes?.ctidTraderAccount?.[0];

    if (account) {
      await sendCtraderApiRequest<ProtoOAAccountAuthRes>(
        ProtoOAPayloadType.PROTO_OA_ACCOUNT_AUTH_REQ,
        ProtoOAAccountAuthReq.create({
          accessToken,
          ctidTraderAccountId: account.ctidTraderAccountId,
        }),
        client,
      );

      const bridgeService = getService(
        client,
        account.ctidTraderAccountId,
        accessToken,
      );

      const res = await startServer(bridgeService, {
        clientId,
        clientSecret,
        accessToken,
        accountId,
        isDemo,
      });

      const it = setInterval(async () => {
        await sendCtraderApiRequest(51 as any, {}, client, true);
        // console.log('heartbeat event');
      }, 6000);

      return {
        ...res,
        cleanup: () => {
          console.log('cleaning provider');
          clearInterval(it);
        },
      };
    }
  } catch (error) {
    throw new PreconditionFailedException(error);
  }
};

const getService = (client: WebSocket, accountId: bigint, _: string) => {
  const tickConfig = {
    symbol: 'EURUSD',
    isTicking: false,
  };

  const bridgeService: IBridgeRpcService = {
    getAvailableSymbols: (_, callback) => {
      (async () => {
        try {
          // get symbols
          const symbolsRes = await sendCtraderApiRequest<ProtoOASymbolsListRes>(
            ProtoOAPayloadType.PROTO_OA_SYMBOLS_LIST_REQ,
            ProtoOASymbolsListReq.create({
              ctidTraderAccountId: accountId,
            }),
            client,
          );

          const symbolCategoriesRes =
            await sendCtraderApiRequest<ProtoOAAssetClassListRes>(
              ProtoOAPayloadType.PROTO_OA_ASSET_CLASS_LIST_REQ,
              ProtoOAAssetClassListReq.create({
                ctidTraderAccountId: accountId,
              }),
              client,
            );

          callback(null, {
            totalSymbols: BigInt(symbolsRes.symbol.length),
            symbols: symbolsRes.symbol.map((s) => ({
              description: s.description,
              name: s.symbolName,
              path: `${symbolCategoriesRes?.assetClass?.find((a) => a.id === s.symbolCategoryId)?.name ?? 'Forex'}\\${s.symbolName}`,
              // todo:
              digits: BigInt(0),
              spread: 0,
              time: BigInt(Date.now()),
            })),
          });
        } catch (e) {
          callback(e);
        }
      })();
    },
    placeTrade: ({ request }, callback) => {
      (async () => {
        try {
          const {
            actionType,
            symbol,
            volume,
            price,
            deviation,
            magic,
            takeProfit,
            comment,
            expiration,
            stopLimit,
            stopLoss,
          } = request;

          const symbolsRes = await sendCtraderApiRequest<ProtoOASymbolsListRes>(
            ProtoOAPayloadType.PROTO_OA_SYMBOLS_LIST_REQ,
            ProtoOASymbolsListReq.create({
              ctidTraderAccountId: accountId,
            }),
            client,
          );

          const symbolId = symbolsRes.symbol.find(
            (s) => s.symbolName === symbol,
          )?.symbolId;

          assert(symbolId, 'Invalid Symbol');

          let side: ProtoOATradeSide;
          let orderType: ProtoOAOrderType;

          switch (Number(actionType)) {
            case ORDER_TYPES.ORDER_TYPE_BUY:
              side = ProtoOATradeSide.BUY;
              orderType = ProtoOAOrderType.MARKET;
              break;

            case ORDER_TYPES.ORDER_TYPE_SELL:
              side = ProtoOATradeSide.SELL;
              orderType = ProtoOAOrderType.MARKET;
              break;

            case ORDER_TYPES.ORDER_TYPE_BUY_LIMIT:
              side = ProtoOATradeSide.BUY;
              orderType = ProtoOAOrderType.LIMIT;
              break;

            case ORDER_TYPES.ORDER_TYPE_SELL_LIMIT:
              side = ProtoOATradeSide.SELL;
              orderType = ProtoOAOrderType.LIMIT;
              break;

            case ORDER_TYPES.ORDER_TYPE_BUY_STOP:
              side = ProtoOATradeSide.BUY;
              orderType = ProtoOAOrderType.STOP;
              break;

            case ORDER_TYPES.ORDER_TYPE_SELL_STOP:
              side = ProtoOATradeSide.SELL;
              orderType = ProtoOAOrderType.STOP;
              break;
          }

          if (symbolId) {
            let orderInfo = await sendCtraderApiRequest<ProtoOAExecutionEvent>(
              ProtoOAPayloadType.PROTO_OA_NEW_ORDER_REQ,
              ProtoOANewOrderReq.create({
                ctidTraderAccountId: accountId,
                symbolId: symbolId,
                volume: BigInt(volume),
                orderType: orderType,
                tradeSide: side,
                comment: comment,
                clientOrderId: magic.toString(),
                label: magic.toString(),
                expirationTimestamp: expiration,
                stopPrice: stopLimit,
                limitPrice: price,
                slippageInPoints: deviation ? Number(deviation) : undefined,
              }),
              client,
            );
            await delay(1000);
            if (takeProfit || stopLoss) {
              orderInfo = await sendCtraderApiRequest<ProtoOAExecutionEvent>(
                ProtoOAPayloadType.PROTO_OA_AMEND_POSITION_SLTP_REQ,
                ProtoOAAmendPositionSLTPReq.create({
                  ctidTraderAccountId: accountId,
                  positionId: orderInfo.order.positionId,
                  stopLoss,
                  takeProfit,
                }),
                client,
              );
            }

            callback(null, {
              message: 'Trade placed',
              status: BigInt(1),
              ticket: orderInfo.order?.positionId.toString(),
            });
          } else {
            throw new PreconditionFailedException('Symbol does not exist');
          }
        } catch (e) {
          callback(e);
        }
      })();
    },
    closeTrade: (request, callback) => {
      (async () => {
        try {
          const { ticket, volume } = request.request;

          await sendCtraderApiRequest<ProtoOAExecutionEvent>(
            ProtoOAPayloadType.PROTO_OA_CLOSE_POSITION_REQ,
            ProtoOAClosePositionReq.create({
              ctidTraderAccountId: accountId,
              positionId: ticket,
              volume: BigInt(volume),
            }),
            client,
            true,
          );

          callback(null, { message: 'Order closed', status: BigInt(1) });
        } catch (e) {
          callback(e);
        }
      })();
    },
    getOrders: (_, callback) => {
      (async () => {
        const ordersRes = await sendCtraderApiRequest<ProtoOAReconcileRes>(
          ProtoOAPayloadType.PROTO_OA_RECONCILE_REQ,
          ProtoOAReconcileReq.create({
            ctidTraderAccountId: accountId,
          }),
          client,
        );

        if (ordersRes.order?.length > 0) {
          const profitRes =
            await sendCtraderApiRequest<ProtoOAGetPositionUnrealizedPnLRes>(
              ProtoOAPayloadType.PROTO_OA_GET_POSITION_UNREALIZED_PNL_REQ,
              ProtoOAGetPositionUnrealizedPnLReq.create({
                ctidTraderAccountId: accountId,
              }),
              client,
            );

          const symbolsRes = await sendCtraderApiRequest<ProtoOASymbolsListRes>(
            ProtoOAPayloadType.PROTO_OA_SYMBOLS_LIST_REQ,
            ProtoOASymbolsListReq.create({
              ctidTraderAccountId: accountId,
            }),
            client,
          );

          const orders = await Promise.all(
            ordersRes.order.map(async (o) => {
              let magic = Number.parseInt(o.tradeData.label);
              magic = Number.isNaN(magic) ? 0 : magic;

              const s = symbolsRes.symbol.find(
                (s) => s.symbolId === o.tradeData.symbolId,
              );

              let posProfit = profitRes.positionUnrealizedPnL.find(
                (pr) => pr.positionId === o.positionId,
              );

              const profit =
                Number(posProfit.netUnrealizedPnL) /
                10 ** profitRes.moneyDigits;

              return {
                comment: o.tradeData.comment ?? '',
                magic: BigInt(magic),
                priceCurrent: 0,
                priceOpen: o.stopPrice ?? o.limitPrice,
                stopLoss: o.stopLoss ?? 0,
                takeProfit: o.takeProfit ?? 0,
                symbol: s.symbolName,
                ticket: o.positionId,
                timeExpiration: o.expirationTimestamp ?? 0,
                timeSetup:
                  CTRADER_TO_MT5_TIME_MAP[o.timeInForce] ??
                  ORDER_TYPE_TIME.ORDER_TIME_GTC,
                type:
                  CTRADER_TO_MT5_ORDER_TYPE_MAP(
                    o.tradeData.tradeSide === ProtoOATradeSide.BUY,
                    o.orderType,
                  ) ?? 0,
                volume: Number(o.tradeData.volume ?? 0),
              } as GetOrdersResponse['orders'][0];
            }),
          );

          callback(null, {
            orders: orders,
            totalOrders: BigInt(orders.length),
          });
        } else {
          callback(null, {
            orders: [],
            totalOrders: BigInt(0),
          });
        }

        try {
        } catch (e) {
          callback(e);
        }
      })();
    },
    getPositions: (_, callback) => {
      (async () => {
        const ordersRes = await sendCtraderApiRequest<ProtoOAReconcileRes>(
          ProtoOAPayloadType.PROTO_OA_RECONCILE_REQ,
          ProtoOAReconcileReq.create({
            ctidTraderAccountId: accountId,
          }),
          client,
        );

        if (ordersRes.position?.length > 0) {
          const symbolsRes = await sendCtraderApiRequest<ProtoOASymbolsListRes>(
            ProtoOAPayloadType.PROTO_OA_SYMBOLS_LIST_REQ,
            ProtoOASymbolsListReq.create({
              ctidTraderAccountId: accountId,
            }),
            client,
          );

          const profitRes =
            await sendCtraderApiRequest<ProtoOAGetPositionUnrealizedPnLRes>(
              ProtoOAPayloadType.PROTO_OA_GET_POSITION_UNREALIZED_PNL_REQ,
              ProtoOAGetPositionUnrealizedPnLReq.create({
                ctidTraderAccountId: accountId,
              }),
              client,
            );

          const positions = await Promise.all(
            ordersRes.position.map(async (p) => {
              let magic = Number.parseInt(p.tradeData.label);
              magic = Number.isNaN(magic) ? 0 : magic;

              const s = symbolsRes.symbol.find(
                (s) => s.symbolId === p.tradeData.symbolId,
              );

              let posProfit = profitRes.positionUnrealizedPnL.find(
                (pr) => pr.positionId === p.positionId,
              );

              const profit =
                Number(posProfit.netUnrealizedPnL) /
                10 ** profitRes.moneyDigits;

              return {
                comment: p.tradeData.comment ?? '',
                magic: BigInt(magic),
                currentPrice: 0,
                openPrice: p.price,
                profit: Number(profit),
                symbol: s.symbolName,
                stopLoss: p.stopLoss ?? 0,
                takeProfit: p.takeProfit ?? 0,
                ticket: p.positionId,
                volume: Number(p.tradeData.volume),
                type: BigInt(
                  p.tradeData.tradeSide === ProtoOATradeSide.BUY
                    ? ORDER_TYPES.ORDER_TYPE_BUY
                    : ORDER_TYPES.ORDER_TYPE_SELL,
                ),
              } as GetPositionsResponse['positions'][0];
            }),
          );

          callback(null, {
            positions: positions,
            totalPositions: BigInt(positions.length),
          });
        } else {
          callback(null, {
            positions: [],
            totalPositions: BigInt(0),
          });
        }

        try {
        } catch (e) {
          callback(e);
        }
      })();
    },
    getTerminalError: (_, callback) => {
      (async () => {
        callback(null, { status: BigInt(1), message: 'OK' });
      })();
    },
    getTicksFrom: ({ request }, callback) => {
      (async () => {
        try {
          const { startDate, symbol, length } = request;
          const symbolsRes = await sendCtraderApiRequest<ProtoOASymbolsListRes>(
            ProtoOAPayloadType.PROTO_OA_SYMBOLS_LIST_REQ,
            ProtoOASymbolsListReq.create({
              ctidTraderAccountId: accountId,
            }),
            client,
          );

          const symbolId = symbolsRes.symbol.find(
            (s) => s.symbolName === symbol,
          )?.symbolId;

          assert(symbolId, 'Invalid Symbol');

          const symbolInfo = await getSymbolFromId(symbolId, client, accountId);
          const digits = symbolInfo.symbol[0].digits;

          const res = await sendCtraderApiRequest<ProtoOAGetTrendbarsRes>(
            ProtoOAPayloadType.PROTO_OA_GET_TRENDBARS_REQ,
            ProtoOAGetTrendbarsReq.create({
              ctidTraderAccountId: accountId,
              fromTimestamp: startDate,
              toTimestamp: BigInt(Date.now()),
              count: Number(length),
              period: ProtoOATrendbarPeriod.M1,
              symbolId,
            }),
            client,
          );

          callback(null, {
            ticks: res.trendbar.map((t) => ({
              ask: decodePrice(t.deltaOpen + t.low, digits),
              bid: decodePrice(t.deltaOpen + t.low, digits),
              time: BigInt(t.utcTimestampInMinutes * 60),
            })),
            totalTicks: BigInt(res.trendbar.length),
          });
        } catch (e) {
          callback(e);
        }
      })();
    },
    getTicksRange: ({ request }, callback) => {
      (async () => {
        try {
          const { endDate, startDate, symbol } = request;
          const symbolsRes = await sendCtraderApiRequest<ProtoOASymbolsListRes>(
            ProtoOAPayloadType.PROTO_OA_SYMBOLS_LIST_REQ,
            ProtoOASymbolsListReq.create({
              ctidTraderAccountId: accountId,
            }),
            client,
          );

          const symbolId = symbolsRes.symbol.find(
            (s) => s.symbolName === symbol,
          )?.symbolId;

          assert(symbolId, 'Invalid Symbol');

          const symbolInfo = await getSymbolFromId(symbolId, client, accountId);
          const digits = symbolInfo.symbol[0].digits;

          const res = await sendCtraderApiRequest<ProtoOAGetTickDataRes>(
            ProtoOAPayloadType.PROTO_OA_GET_TICKDATA_REQ,
            ProtoOAGetTickDataReq.create({
              ctidTraderAccountId: accountId,
              fromTimestamp: startDate,
              toTimestamp: endDate,
              symbolId,
              type: ProtoOAQuoteType.BID,
            }),
            client,
          );

          callback(null, {
            totalTicks: BigInt(res.tickData.length),
            ticks: res.tickData.map((t, index) => {
              if (index < res.tickData.length - 1) {
                const next = res.tickData[index + 1];
                res.tickData[index + 1] = {
                  tick: next.tick + t.tick,
                  timestamp: next.timestamp + t.timestamp,
                };
              }
              return {
                ask: decodePrice(t.tick, digits),
                bid: decodePrice(t.tick, digits),
                time: BigInt(Math.ceil(Number(t.timestamp) / 1000)),
              };
            }),
          });
        } catch (e) {
          callback(e);
        }
      })();
    },
    getAccount: (_, callback) => {
      (async () => {
        try {
          const { trader } = await sendCtraderApiRequest<ProtoOATraderRes>(
            ProtoOAPayloadType.PROTO_OA_TRADER_REQ,
            ProtoOAGetTrendbarsReq.create({
              ctidTraderAccountId: accountId,
            }),
            client,
          );

          callback(null, {
            balance: Number(trader.balance),
            equity: Number(trader.balance),
            freeMargin: Number(trader.balance),
            margin: 0,
            marginLevel: 0,
            profit: 0,
            server: trader.brokerName,
            tradeMode: BigInt(1),
          });
        } catch (e) {
          callback(e);
        }
      })();
    },
    getTradeHistory: ({ request }, callback) => {
      (async () => {
        try {
          const { endDate, startDate } = request;

          const orders = await sendCtraderApiRequest<ProtoOAOrderListRes>(
            ProtoOAPayloadType.PROTO_OA_ORDER_LIST_REQ,
            ProtoOAOrderListReq.create({
              ctidTraderAccountId: accountId,
              ...(startDate ? { startDate } : {}),
              ...(endDate ? { endDate } : {}),
            }),
            client,
          );

          if (orders?.order?.length > 0) {
            const filteredOrders = orders.order.filter(
              (o) => o.orderStatus === ProtoOAOrderStatus.ORDER_STATUS_FILLED,
            );
            const symbolsRes =
              await sendCtraderApiRequest<ProtoOASymbolsListRes>(
                ProtoOAPayloadType.PROTO_OA_SYMBOLS_LIST_REQ,
                ProtoOASymbolsListReq.create({
                  ctidTraderAccountId: accountId,
                }),
                client,
              );

            const profitRes =
              await sendCtraderApiRequest<ProtoOAGetPositionUnrealizedPnLRes>(
                ProtoOAPayloadType.PROTO_OA_GET_POSITION_UNREALIZED_PNL_REQ,
                ProtoOAGetPositionUnrealizedPnLReq.create({
                  ctidTraderAccountId: accountId,
                }),
                client,
              );

            const positions = await Promise.all(
              filteredOrders.map(async (o) => {
                let magic = Number.parseInt(o.tradeData.label);
                magic = Number.isNaN(magic) ? 0 : magic;

                const s = symbolsRes.symbol.find(
                  (s) => s.symbolId === o.tradeData.symbolId,
                );

                let posProfit = profitRes.positionUnrealizedPnL.find(
                  (pr) => pr.positionId === o.positionId,
                );

                const profit =
                  Number(posProfit?.netUnrealizedPnL ?? 0) /
                  10 ** profitRes.moneyDigits;

                return {
                  comment: o.tradeData.comment ?? '',
                  magic: BigInt(magic),
                  profit: profit,
                  ticket: o.positionId,
                  symbol: s.symbolName,
                  volume: Number(o.tradeData.volume),
                  entry: o.executionPrice,
                  commission: 0,
                  swap: 0,
                  type:
                    CTRADER_TO_MT5_ORDER_TYPE_MAP(
                      o.tradeData.tradeSide === ProtoOATradeSide.BUY,
                      o.orderType,
                    ) ?? 0,
                  price: o.executionPrice,
                  order: BigInt(0),
                  time: o.tradeData.closeTimestamp,
                } as GetTradeHistoryResponse['deals'][0];
              }),
            );

            callback(null, {
              totalDeals: BigInt(positions.length),
              deals: positions,
            });
          } else {
            callback(null, {
              deals: [],
              totalDeals: BigInt(0),
            });
          }
        } catch (e) {
          callback(e);
        }
      })();
    },
    modifyTrade: ({ request }, callback) => {
      (async () => {
        const { stopLoss, takeProfit, ticket, expiration, price } = request;
        try {
          const ordersRes = await sendCtraderApiRequest<ProtoOAReconcileRes>(
            ProtoOAPayloadType.PROTO_OA_RECONCILE_REQ,
            ProtoOAReconcileReq.create({
              ctidTraderAccountId: accountId,
            }),
            client,
          );

          const order = ordersRes?.order?.find((o) => o.positionId == ticket);
          const position = ordersRes?.position?.find(
            (p) => p.positionId == ticket,
          );

          if (position) {
            await sendCtraderApiRequest<ProtoOAExecutionEvent>(
              ProtoOAPayloadType.PROTO_OA_AMEND_POSITION_SLTP_REQ,
              ProtoOAAmendPositionSLTPReq.create({
                ctidTraderAccountId: accountId,
                positionId: position.positionId,
                ...(stopLoss ? { stopLoss } : {}),
                ...(takeProfit ? { takeProfit } : {}),
              }),
              client,
            );
          } else if (order) {
            await sendCtraderApiRequest<ProtoOAExecutionEvent>(
              ProtoOAPayloadType.PROTO_OA_AMEND_ORDER_REQ,
              ProtoOAAmendOrderReq.create({
                ctidTraderAccountId: accountId,
                orderId: order.orderId,
                ...(expiration ? { expirationTimestamp: expiration } : {}),
                ...(stopLoss ? { stopLoss } : {}),
                ...(takeProfit ? { takeProfit } : {}),
                ...(order.orderType === ProtoOAOrderType.LIMIT && price
                  ? { limitPrice: price }
                  : {}),
                ...((order.orderType === ProtoOAOrderType.STOP ||
                  order.orderType === ProtoOAOrderType.STOP_LIMIT) &&
                price
                  ? { stopPrice: price }
                  : {}),
              }),
              client,
            );
          }
          callback(null, { message: 'Order modified', status: BigInt(1) });
        } catch (e) {
          callback(e);
        }
      })();
    },
    getSymbolTick: ({ request }, callback) => {
      (async () => {
        try {
          const { symbol } = request;

          const symbolsRes = await sendCtraderApiRequest<ProtoOASymbolsListRes>(
            ProtoOAPayloadType.PROTO_OA_SYMBOLS_LIST_REQ,
            ProtoOASymbolsListReq.create({
              ctidTraderAccountId: accountId,
            }),
            client,
          );

          const symbolId = symbolsRes.symbol.find(
            (s) => s.symbolName === symbol,
          )?.symbolId;

          assert(symbolId, 'Invalid Symbol');

          const symbolInfo = await getSymbolFromId(symbolId, client, accountId);
          const digits = symbolInfo.symbol[0].digits;

          const event = await sendCtraderApiRequest<ProtoOASpotEvent>(
            ProtoOAPayloadType.PROTO_OA_SUBSCRIBE_SPOTS_REQ,
            ProtoOASubscribeSpotsReq.create({
              ctidTraderAccountId: accountId,
              subscribeToSpotTimestamp: true,
              symbolId: [symbolId],
            }),
            client,
          );

          callback(null, {
            ask: decodePrice(event.ask, digits),
            bid: decodePrice(event.bid, digits),
            time: BigInt(Math.round(Number(event.timestamp) / 1000)),
          });

          await sendCtraderApiRequest(
            ProtoOAPayloadType.PROTO_OA_UNSUBSCRIBE_SPOTS_REQ,
            ProtoOAUnsubscribeSpotsReq.create({
              ctidTraderAccountId: accountId,
              symbolId: [symbolId],
            }),
            client,
          );
        } catch (e) {
          callback(e);
        }
      })();
    },
    manageSymbol: (request, callback) => {
      (async () => {
        const { symbol } = request.request;
        const symbolsRes = await sendCtraderApiRequest<ProtoOASymbolsListRes>(
          ProtoOAPayloadType.PROTO_OA_SYMBOLS_LIST_REQ,
          ProtoOASymbolsListReq.create({
            ctidTraderAccountId: accountId,
          }),
          client,
        );

        const symbolId = symbolsRes.symbol.find(
          (s) => s.symbolName === symbol,
        )?.symbolId;

        assert(symbolId, 'Invalid Symbol');
        tickConfig.symbol = symbol;
        callback(null, { message: 'Symbol modified', status: BigInt(1) });
      })();
    },
    tickStart: (stream) => {
      (async () => {
        const subscribe = async () => {
          const symbolsRes = await sendCtraderApiRequest<ProtoOASymbolsListRes>(
            ProtoOAPayloadType.PROTO_OA_SYMBOLS_LIST_REQ,
            ProtoOASymbolsListReq.create({
              ctidTraderAccountId: accountId,
            }),
            client,
          );

          const symbolId = symbolsRes.symbol.find(
            (s) => s.symbolName === symbol,
          )?.symbolId;

          assert(symbolId, 'Invalid Symbol');

          const symbolInfo = await getSymbolFromId(symbolId, client, accountId);
          const digits = symbolInfo.symbol[0].digits;

          await sendCtraderApiRequest<ProtoOASpotEvent>(
            ProtoOAPayloadType.PROTO_OA_SUBSCRIBE_SPOTS_REQ,
            ProtoOASubscribeSpotsReq.create({
              ctidTraderAccountId: accountId,
              subscribeToSpotTimestamp: true,
              symbolId: [symbolId],
            }),
            client,
          );
          return { symbolId, digits };
        };

        const unsubscribe = async () => {
          await sendCtraderApiRequest(
            ProtoOAPayloadType.PROTO_OA_UNSUBSCRIBE_SPOTS_REQ,
            ProtoOAUnsubscribeSpotsReq.create({
              ctidTraderAccountId: accountId,
              symbolId: [symbolId],
            }),
            client,
            true,
          );
          tickConfig.isTicking = false;
        };

        const handleEvent = async (data: RawData) => {
          const message = data.toString();
          const response = JSON.parse(message);

          if (
            response?.payloadType === ProtoOAPayloadType.PROTO_OA_SPOT_EVENT
          ) {
            const event = response.payload as ProtoOASpotEvent;

            if (tickConfig.isTicking === false) {
              console.log('Tick stopped');
              await unsubscribe();
              if (!stream.closed) stream.end();
              client.removeListener('message', handleEvent);
              return;
            }

            if (tickConfig.symbol !== symbol) {
              symbol = tickConfig.symbol;
              await unsubscribe();
              const { digits: d, symbolId: s } = await subscribe();
              digits = d;
              symbolId = s;
            }

            if (Date.now() - lastWrite > rate * 1000) {
              lastWrite = Date.now();

              const ask = decodePrice(event.ask, digits);
              const bid = decodePrice(event.bid, digits);

              stream.write({
                ask: ask === 0 ? bid : ask,
                bid: bid === 0 ? ask : bid,
                time: BigInt(Math.round(Number(event.timestamp) / 1000)),
              });
            }
          }
        };

        let { rate, symbol } = stream.request;
        let lastWrite = Date.now();

        if (tickConfig.isTicking && tickConfig.symbol === symbol) {
          return;
        }

        if (tickConfig.isTicking && tickConfig.symbol !== symbol) {
          tickConfig.symbol = symbol;
          return;
        }

        tickConfig.symbol = symbol;
        tickConfig.isTicking = true;
        
        let { digits, symbolId } = await subscribe();

        client.addListener('message', handleEvent);

        stream.on('close', async () => {
          tickConfig.isTicking = false;
          // stream.end();
        });

        stream.on('error', async (e) => {
          tickConfig.isTicking = false;
        });
      })();
    },
    tickStop: (_, callback) => {
      (async () => {
        tickConfig.isTicking = false;
        callback(null, { message: 'Stopped tick', status: BigInt(1) });
      })();
    },
  };

  return bridgeService;
};
