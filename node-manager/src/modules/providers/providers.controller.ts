import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { AdminHttpGuard } from 'src/guards/auth.guard';
import { ProvidersService } from './providers.service';
import { InitCtraderProviderDto, InitHyperliquidProviderDto, KillProviderDto } from './utils/dto.utils';

@Controller('providers')
export class ProvidersController {
  constructor(private providersService: ProvidersService) {}

  @UseGuards(AdminHttpGuard)
  @Post('init-hyperliquid')
  async initNewHyperliquidProvider(@Body() body: InitHyperliquidProviderDto) {
    const data = await this.providersService.initNewHyperliquidProvider(body);
    return {
      data,
      message: 'Started hyperliquid provider',
    };
  }

  @UseGuards(AdminHttpGuard)
  @Post('init-ctrader')
  async initCtraderProvider(@Body() body: InitCtraderProviderDto) {
    const data = await this.providersService.initCtraderProvider(body);
    return {
      data,
      message: 'Started ctrader provider',
    };
  }

  @UseGuards(AdminHttpGuard)
  @Post('kill')
  async killProvider(@Body() body: KillProviderDto) {
    const data = await this.providersService.killProvider(body);
    return {
      data,
      message: 'Killed provider',
    };
  }
}
