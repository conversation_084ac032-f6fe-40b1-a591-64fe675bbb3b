import {
  Injectable,
  Logger,
  PreconditionFailedException,
} from '@nestjs/common';
import { StartHyperliquidProviderServer } from './hyperliquid.provider';
import {
  InitCtraderProviderDto,
  InitHyperliquidProviderDto,
  KillProviderDto,
} from './utils/dto.utils';
import { Server } from '@grpc/grpc-js';
import { MemCache } from '../terminals/terminals.service';
import { StartCtraderProviderServer } from 'src/modules/providers/ctrader.provider';

@Injectable()
export class ProvidersService {
  providers: Record<
    string,
    {
      server: Server;
      cleanup?: VoidFunction;
    }
  > = {};
  constructor(private logger: Logger) {}

  async initNewHyperliquidProvider(data: InitHyperliquidProviderDto) {
    try {
      const { rpcPort, providerId, server } =
        await StartHyperliquidProviderServer(
          data.private_key,
          data.wallet_address,
          data.auth_token,
          data.testnet,
        );

      this.providers[providerId] = { server };

      return {
        rpcPort,
        providerId,
      };
    } catch (e) {
      this.logger.error(e);
      throw new PreconditionFailedException(e);
    }
  }

  async initCtraderProvider({
    access_token,
    account_id,
    client_id,
    client_secret,
    is_demo,
  }: InitCtraderProviderDto) {
    try {
      const { rpcPort, providerId, server, cleanup } =
        await StartCtraderProviderServer(
          client_id,
          client_secret,
          access_token,
          account_id,
          is_demo,
        );
      this.providers[providerId] = { server, cleanup };

      return {
        rpcPort,
        providerId,
      };
    } catch (e) {
      this.logger.error(e);
      throw new PreconditionFailedException(e);
    }
  }

  async killProvider(data: KillProviderDto) {
    const provider = this.providers[data.provider_id];
    if (!provider)
      throw new PreconditionFailedException('Provider does not exist');

    const { bridge_port: ws_port } = MemCache.clients[data.provider_id];

    MemCache.activePorts = MemCache.activePorts.filter((p) => p !== ws_port);
    MemCache.totalConnected--;

    delete MemCache.clients[data.provider_id];
    delete MemCache.providers[data.provider_id];

    provider.server.forceShutdown();
    provider.cleanup();
    delete this.providers[data.provider_id];
  }
}
