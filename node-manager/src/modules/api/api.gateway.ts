import {
  Logger,
  Req
} from '@nestjs/common';
import {
  MessageBody,
  OnGatewayConnection,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { validateSync } from 'class-validator';
import { Server } from 'ws';
import { ApiController } from './api.controller';
import { ApiService } from './api.service';
import {
  CloseTradeDto,
  ModifyTradeDto,
  PlaceTradeDto,
} from './utils/dto.utils';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
  path: '/api',
})
export class ApiGateway implements OnGatewayConnection, OnGatewayInit {
  controllers: ApiController;
  constructor(
    private apiService: ApiService,
    private readonly logger: Logger,
  ) {}

  @WebSocketServer()
  server: Server;

  afterInit(_) {
    this.logger.log('Initialized');
    this.server.on('connection', (socket, request) => {
      socket['request'] = request;
    });
  }

  handleConnection(_) {
    this.logger.log('Client connected');
  }

  @SubscribeMessage('place-trade')
  async handlePlaceTrade(
    @MessageBody() dto: any,
    @Req() request: any,
  ) {
    try {
      this.validatePayload(dto, new PlaceTradeDto());
      return await this.apiService.placeTrade(this.getApiKey(request), dto);
    } catch (e) {
      return {
        message: 'Bad Request',
        status: 0,
        errors: e.message,
      };
    }
  }

  @SubscribeMessage('modify-trade')
  async handleModifyTrade(@MessageBody() dto: any, @Req() request: any) {
    try {
      this.validatePayload(dto, new ModifyTradeDto());
      return await this.apiService.modifyTrade(this.getApiKey(request), dto);
    } catch (e) {
      return {
        message: 'Bad Request',
        status: 0,
        errors: e.message,
      };
    }
  }

  @SubscribeMessage('close-trade')
  async handleCloseTrade(@MessageBody() dto: any, @Req() request: any) {
    try {
      this.validatePayload(dto, new CloseTradeDto());
      return await this.apiService.closeTrade(this.getApiKey(request), dto);
    } catch (e) {
      return {
        message: 'Bad Request',
        status: 0,
        errors: e.message,
      };
    }
  }

  getApiKey(request: any) {
    return (request?.request?.headers?.authorization as string)
      ?.replace('Bearer', '')
      .trim();
  }

  validatePayload(payload: any, dto: Object) {
    Object.assign(dto, payload);
    const errors = validateSync(dto, {});
    if (errors?.length > 0) {
      const d = errors.reduce(
        (p, c) => {
          p.errors = [...p.errors, ...Object.values(c.constraints)];
          return p;
        },
        {
          errors: [],
        },
      );
      throw new Error(d.errors.toString());
    }
  }
}
