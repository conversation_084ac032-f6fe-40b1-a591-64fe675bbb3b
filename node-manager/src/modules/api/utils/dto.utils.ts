import { IsNotEmpty, <PERSON>N<PERSON><PERSON>, IsN<PERSON>berString, IsOptional, IsString } from 'class-validator';

export class ApiConnectDto {
  @IsNumber()
  @IsNotEmpty()
  rpc_port: number;

  @IsString()
  @IsNotEmpty()
  authorization: string;
}
export class ApiDisconnectDto {
  @IsString()
  @IsNotEmpty()
  api_key: string;
}

export class TickStartDto {
  @IsNumberString()
  @IsNotEmpty()
  rate: string;

  @IsString()
  @IsNotEmpty()
  symbol: string;
}

export class GetTradeHistoryDto {
  @IsNumber()
  @IsNotEmpty()
  start_date: number;

  @IsNumber()
  @IsNotEmpty()
  end_date: number;
}

export class GetTicksFromDto {
  @IsNumber()
  @IsNotEmpty()
  length: number;

  @IsNumber()
  @IsNotEmpty()
  start_date: number;

  @IsString()
  @IsNotEmpty()
  symbol: string;
}

export class GetTicksRangeDto {
  @IsNumber()
  @IsNotEmpty()
  end_date: number;

  @IsNumber()
  @IsNotEmpty()
  start_date: number;

  @IsNumber()
  @IsNotEmpty()
  length: number;

  @IsString()
  @IsNotEmpty()
  symbol: string;
}

export class CloseTradeDto {
  @IsNumber()
  @IsNotEmpty()
  ticket: number;

  @IsNumber()
  @IsNotEmpty()
  price: number;

  @IsNumber()
  @IsNotEmpty()
  volume: number;

  @IsNumber()
  @IsOptional()
  slippage?: number;
}
export class ModifyTradeDto {
  @IsNumber()
  @IsNotEmpty()
  ticket: number;

  @IsNumber()
  @IsOptional()
  price: number;

  @IsNumber()
  @IsOptional()
  take_profit: number;

  @IsNumber()
  @IsOptional()
  stop_loss: number;

  @IsNumber()
  @IsOptional()
  expiration?: number;
}

export class PlaceTradeDto {
  @IsNumber()
  @IsNotEmpty()
  action_type: number;

  @IsNumber()
  @IsNotEmpty()
  volume: number;

  @IsString()
  @IsNotEmpty()
  symbol: string;

  // optional

  @IsNumber()
  @IsOptional()
  price: number;

  @IsNumber()
  @IsOptional()
  take_profit: number;

  @IsNumber()
  @IsOptional()
  stop_loss: number;

  @IsNumber()
  @IsOptional()
  stop_limit: number;

  @IsNumber()
  @IsOptional()
  expiration?: number;

  @IsNumber()
  @IsOptional()
  deviation?: number;

  @IsNumber()
  @IsOptional()
  magic?: number;

  @IsNumber()
  @IsOptional()
  order?: number;

  @IsNumber()
  @IsOptional()
  position?: number;

  @IsNumber()
  @IsOptional()
  position_by?: number;

  @IsString()
  @IsOptional()
  comment?: string;
}

export class ManageSymbolDto {
  @IsString()
  @IsNotEmpty()
  action: string;

  @IsString()
  @IsNotEmpty()
  symbol: string;
}
export class GetSymbolTickDto {
  @IsString()
  @IsNotEmpty()
  symbol: string;
}
