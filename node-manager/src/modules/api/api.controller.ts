import { ChannelCredentials } from '@grpc/grpc-js';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Headers,
  Logger,
  Post,
  Query,
  Res,
  Sse,
  UseGuards,
} from '@nestjs/common';
import { GrpcTransport } from '@protobuf-ts/grpc-transport';
import { Observable } from 'rxjs';
import { AdminHttpGuard } from 'src/guards/auth.guard';
import { TickType } from '../../utils/bridge/bridge';
import { BridgeRpcServiceClient } from '../../utils/bridge/bridge.client';
import {
  ApiConnectDto,
  ApiDisconnectDto,
  CloseTradeDto,
  GetSymbolTickDto,
  GetTicksFromDto,
  GetTicksRangeDto,
  GetTradeHistoryDto,
  ManageSymbolDto,
  ModifyTradeDto,
  PlaceTradeDto,
  TickStartDto,
} from './utils/dto.utils';
import AbortController from 'abort-controller';
import e, { Response } from 'express';
import { ApiService, MemCache } from './api.service';

@Controller('api')
export class ApiController {
  private tickEvents: Record<string, Observable<any>> = {};
  private abortControllers: Record<string, AbortController> = {};
  constructor(
    private readonly logger: Logger,
    private apiService: ApiService,
  ) {}

  @UseGuards(AdminHttpGuard)
  @Post('connect')
  async CreateConnection(@Body() data: ApiConnectDto) {
    if (MemCache.activePorts.includes(data.rpc_port)) {
      throw new BadRequestException('Port already connected');
    }
    const transport = new GrpcTransport({
      host: `localhost:${data.rpc_port}`,
      channelCredentials: ChannelCredentials.createInsecure(),
      meta: {
        authorization: data.authorization,
      },
    });
    const apiKey = '1739826201470-ifnrb6fg'; // generateRandomId();
    const client = new BridgeRpcServiceClient(transport);

    MemCache.totalConnected++;
    MemCache.clients[apiKey] = {
      client,
      id: apiKey,
      port: data.rpc_port,
    };
    MemCache.activePorts.push(data.rpc_port);

    return {
      api_key: apiKey,
    };
  }
  @UseGuards(AdminHttpGuard)
  @Post('disconnect')
  async RemoveConnection(@Body() data: ApiDisconnectDto) {
    if (!MemCache.clients[data.api_key]) {
      throw new BadRequestException('client is not connected');
    }

    MemCache.totalConnected--;
    const client = MemCache.clients[data.api_key];
    MemCache.activePorts = MemCache.activePorts.filter(
      (p) => p !== client.port,
    );
    delete MemCache.clients[data.api_key];

    return {
      clients: {
        ...MemCache,
      },
      message: 'successfully removed client',
    };
  }

  @Post('tick-stop')
  async TickStop(@Headers('Authorization') api_key: string) {
    delete this.tickEvents[api_key];
    this.abortControllers[api_key]?.abort();

    console.log(this.abortControllers);

    return {
      message: '',
    };
  }

  @Sse('tick-start')
  async TickStart(
    @Headers('Authorization') api_key: string,
    @Query() dto: TickStartDto,
    @Res() response: Response,
  ): Promise<Observable<TickType>> {
    response.setHeader(
      'Access-Control-Allow-Headers',
      'authorization,content-type',
    );
    response.setHeader('Access-Control-Allow-Origin', '*');
    response.setHeader('Access-Control-Allow-Credentials', 'true');

    if (this.tickEvents[api_key]) {
      return this.tickEvents[api_key];
    }

    const client = this.getClient(api_key);

    // aborts the grpc call
    const abort = new AbortController();
    this.abortControllers[api_key] = abort;

    const ticks = client.tickStart(
      {
        rate: Number.parseFloat(dto.rate),
        symbol: dto.symbol,
      },
      { abort: abort.signal as any },
    );

    // on disconnect or finish from grpc server close client conn
    ticks
      .then(() => {
        console.log('attempting to end response');
        if (response.writableEnded) return;
        response?.end();
      })
      .catch(() => response?.end());

    if (!this.tickEvents[api_key]) {
      this.tickEvents[api_key] = new Observable((subscriber) => {
        ticks.responses.onNext((res) => {
          subscriber.next(JSON.stringify(res));
        });
      });
    }

    // on disconnect from client call abort
    response.on('close', async () => {
      console.log('response closing');
      this.abortControllers[api_key].abort();
      delete this.tickEvents[api_key];
    });

    return this.tickEvents[api_key];
  }

  @Post('get-account')
  async GetAccount(@Headers('Authorization') api_key: string) {
    const client = this.getClient(api_key);
    const account = await client.getAccount({});
    const res = account.response;
    return {
      message: '',
      data: res,
    };
  }

  @Post('get-positions')
  async GetPositions(@Headers('Authorization') api_key: string) {
    const client = this.getClient(api_key);
    const positions = await client.getPositions({});
    const res = positions.response;
    return {
      message: '',
      data: res,
    };
  }

  @Post('get-orders')
  async GetOrders(@Headers('Authorization') api_key: string) {
    const client = this.getClient(api_key);
    const orders = await client.getOrders({});
    const res = orders.response;
    return {
      message: '',
      data: res,
    };
  }

  @Post('get-symbols')
  async GetSymbols(@Headers('Authorization') api_key: string) {
    const client = this.getClient(api_key);
    const symbols = await client.getAvailableSymbols({});
    const res = symbols.response;
    return {
      message: '',
      data: res,
    };
  }

  @Post('get-trade-history')
  async GetTradeHistory(
    @Headers('Authorization') api_key: string,
    @Query() dto: GetTradeHistoryDto,
  ) {
    const client = this.getClient(api_key);
    const tradeHistory = await client.getTradeHistory({
      endDate: BigInt(dto.end_date),
      startDate: BigInt(dto.start_date),
    });
    const res = tradeHistory.response;
    return {
      message: '',
      data: res,
    };
  }

  @Post('get-ticks-from')
  async GetTicksFrom(
    @Headers('Authorization') api_key: string,
    @Body() dto: GetTicksFromDto,
  ) {
    const client = this.getClient(api_key);
    const ticks = await client.getTicksFrom({
      symbol: dto.symbol,
      startDate: BigInt(dto.start_date),
      length: BigInt(dto.length),
    });
    const res = ticks.response;
    return {
      message: '',
      data: res,
    };
  }

  @Post('get-ticks-range')
  async GetTicksRange(
    @Headers('Authorization') api_key: string,
    @Body() dto: GetTicksRangeDto,
  ) {
    const client = this.getClient(api_key);
    const ticks = await client.getTicksRange({
      endDate: BigInt(dto.end_date),
      startDate: BigInt(dto.start_date),
      length: BigInt(dto.length),
      symbol: dto.symbol,
    });
    const res = ticks.response;
    return {
      message: '',
      data: res,
    };
  }

  @Post('get-symbol-tick')
  async GetSymbol(
    @Headers('Authorization') api_key: string,
    @Query() dto: GetSymbolTickDto,
  ) {
    const client = this.getClient(api_key);
    const tick = await client.getSymbolTick({
      symbol: dto.symbol,
    });
    const res = tick.response;
    return {
      message: '',
      data: res,
    };
  }

  @Post('get-error')
  async GetError(@Headers('Authorization') api_key: string) {
    const client = this.getClient(api_key);
    const error = await client.getTerminalError({});
    const res = error.response;
    return {
      message: '',
      data: res,
    };
  }

  @Post('close-trade')
  async CloseTrade(
    @Headers('Authorization') api_key: string,
    @Body() dto: CloseTradeDto,
  ) {
    return await this.apiService.closeTrade(api_key, dto);
  }

  @Post('modify-trade')
  async ModifyTrade(
    @Headers('Authorization') api_key: string,
    @Body() dto: ModifyTradeDto,
  ) {
    return await this.apiService.modifyTrade(api_key, dto);
  }

  @Post('place-trade')
  async PlaceTrade(
    @Headers('Authorization') api_key: string,
    @Body() dto: PlaceTradeDto,
  ) {
    return await this.apiService.placeTrade(api_key, dto);
  }

  @Post('manage-symbol')
  async ManageSymbol(
    @Headers('Authorization') api_key: string,
    @Body() dto: ManageSymbolDto,
  ) {
    const client = this.getClient(api_key);
    const symbol = await client.manageSymbol({
      action: dto.action,
      symbol: dto.symbol,
    });
    const res = symbol.response;
    return {
      message: '',
      data: res,
    };
  }

  getClient(api_key: string) {
    const client =
      MemCache.clients[api_key?.replace('Bearer ', '')?.trim()]?.client;
    if (client) {
      return client;
    }
    throw new BadRequestException('Client not found');
  }
}
