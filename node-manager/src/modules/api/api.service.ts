import {
  BadRequestException,
  Body,
  forwardRef,
  Inject,
  Injectable,
} from '@nestjs/common';
import { CommonService } from '../common/common.service';
import {
  CloseTradeDto,
  ModifyTradeDto,
  PlaceTradeDto,
} from './utils/dto.utils';
import { BridgeRpcServiceClient } from 'src/utils/bridge/bridge.client';

export const MemCache = {
  totalConnected: 0,
  activePorts: [] as number[],
  clients: {} as {
    [key: string]: {
      client: BridgeRpcServiceClient;
      id: string;
      port: number;
    };
  },
};
@Injectable()
export class ApiService {
  constructor() {}

  async placeTrade(api_key: string, dto: PlaceTradeDto) {
    const client = this.getClient(api_key);
    const trade = await client.placeTrade({
      actionType: BigInt(dto.action_type),
      volume: dto.volume,
      symbol: dto.symbol,
      price: dto.price,
      takeProfit: dto.take_profit,
      stopLoss: dto.stop_loss,
      stopLimit: dto.stop_limit,
      expiration: dto.expiration ? BigInt(dto.expiration) : undefined,
      deviation: dto.deviation ? BigInt(dto.deviation) : undefined,
      magic: dto.magic ? BigInt(dto.magic) : undefined,
      position: dto.position ? BigInt(dto.position) : undefined,
      positionBy: dto.position_by ? BigInt(dto.position_by) : undefined,
      comment: dto.comment ?? 'default comment',
      order: dto.order ? BigInt(dto.order) : undefined,
    });
    const res = trade.response;
    return {
      message: '',
      data: res,
    };
  }

  async modifyTrade(api_key: string, dto: ModifyTradeDto) {
    const client = this.getClient(api_key);
    const trade = await client.modifyTrade({
      ticket: BigInt(dto.ticket),
      price: dto.price ?? 0,
      stopLoss: dto.stop_loss ?? 0,
      takeProfit: dto.take_profit ?? 0,
      expiration: BigInt(dto.expiration ?? 0),
    });
    const res = trade.response;
    return {
      message: '',
      data: res,
    };
  }

  async closeTrade(api_key: string, dto: CloseTradeDto) {
    const client = this.getClient(api_key);
    const trade = await client.closeTrade({
      ticket: BigInt(dto.ticket),
      price: dto.price,
      slippage: dto.slippage,
      volume: dto.volume,
    });
    const res = trade.response;
    return {
      message: '',
      data: res,
    };
  }

  getClient(api_key: string) {
    const client =
      MemCache.clients[api_key?.replace('Bearer ', '')?.trim()]?.client;
    if (client) {
      return client;
    }
    throw new BadRequestException('Client not found');
  }
}
