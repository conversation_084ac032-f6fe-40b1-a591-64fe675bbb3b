import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MailRepository } from 'src/repos/mail.repo';
import { CommonService } from './common.service';
@Module({
  imports: [
    ConfigModule.forRoot(),
    
  ],
  providers: [
    MailRepository,
    Logger,
    CommonService,
  ],
  exports: [
    MailRepository,
    ConfigModule,
    Logger,
    CommonService,
  ],
})
export class CommonModule {}
