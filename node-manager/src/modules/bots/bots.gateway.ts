import { Logger, UseGuards } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, WebSocket } from 'ws';
import { AdminHttpGuard, AdminWsGuard } from 'src/guards/auth.guard';
import { delay } from 'src/utils/functions';
import { LocalIpGuard, LocalWsIpGuard } from 'src/guards/ip.guard';
import { BotCallbackDto } from './utils/dto.utils';
import { MemCache } from './bots.service';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
  path: '/bots',
})
export class BotsGateway implements OnGatewayConnection, OnGatewayInit {
  internalAuthToken: string;
  constructor(private readonly logger: Logger) {
    this.internalAuthToken = process.env.ADMIN_AUTH_TOKEN;
  }

  @WebSocketServer()
  server: Server;

  afterInit(server) {
    this.logger.log('Initialized');
    this.server.on('connection', (socket, request) => {
      socket['request'] = request;
    });
  }

  handleConnection(client: WebSocket) {
    this.logger.log('Client connected');
  }

  @UseGuards(AdminWsGuard)
  @SubscribeMessage('events')
  async handleEvents(@MessageBody() data: any) {
    console.log(data);
  }

  @UseGuards(LocalWsIpGuard)
  @SubscribeMessage('bot:init')
  async handleBotInit(
    @MessageBody() data: BotCallbackDto,
    @ConnectedSocket() client: WebSocket,
  ) {
    const bot = MemCache.bots[data.id]
    if(bot){
      MemCache.totalRunning++
      MemCache.bots[data.id].client = client
    }
  }
}
