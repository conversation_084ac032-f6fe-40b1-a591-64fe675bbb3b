import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';

enum BOT_STATE_CMDS {
  PAUSE = 'PAUSE',
  START = 'START',
}
export class InitBotDto {
  @IsString()
  @IsOptional()
  name: string;

  @IsString()
  @IsOptional()
  url: string;

  @IsBoolean()
  @IsNotEmpty()
  is_internal: boolean;

  @IsNotEmpty()
  @IsObject()
  config: Object;

  @IsString()
  @IsNotEmpty()
  rpc_server: string;

  @IsString()
  @IsNotEmpty()
  ws_server: string;

  @IsString()
  @IsNotEmpty()
  auth_token: string;

  @IsString()
  @IsNotEmpty()
  id: string;
}
export class ToggleBotStateDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  @IsEnum(BOT_STATE_CMDS)
  cmd: BOT_STATE_CMDS;
}

export class Bot<PERSON>allbackDto {
  config: any;
  state: any;
  id: string;
}
