import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Empt<PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';
import { TERMINAL_TYPE } from './enums.utils';

export class InitTerminalDto {
  @IsString()
  @IsOptional()
  callback: string;

  @IsString()
  @IsNotEmpty()
  login: string;

  @IsString()
  @IsNotEmpty()
  password: string;

  @IsString()
  @IsNotEmpty()
  server: string;

  @IsString()
  @IsNotEmpty()
  auth_token: string;

  @IsString()
  @IsNotEmpty()
  @IsEnum(TERMINAL_TYPE)
  terminal_type: TERMINAL_TYPE;
}



export class TerminalLoginDto {
  @IsString()
  ws_port: string;

  @IsString()
  rpc_port: string;

  @IsString()
  terminal_id: number;

  @IsString()
  message: string;

  @IsString()
  @IsString()
  status: string;
}

export class KillTerminalDto {
  @IsString()
  terminal_id: string;
}
