import { Logger, UseGuards } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, WebSocket } from 'ws';
import { AdminHttpGuard, AdminWsGuard } from 'src/guards/auth.guard';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
  path: '/terminals',
})
export class TerminalsGateway implements OnGatewayConnection, OnGatewayInit {
  internalAuthToken: string;
  constructor(private readonly logger: Logger) {
    this.internalAuthToken = process.env.ADMIN_AUTH_TOKEN;
  }

  @WebSocketServer()
  server: Server;

  afterInit(server) {
    this.logger.log('Initialized');
  }

  handleConnection(client: WebSocket) {
    this.logger.log('Client connected');
  }
}
