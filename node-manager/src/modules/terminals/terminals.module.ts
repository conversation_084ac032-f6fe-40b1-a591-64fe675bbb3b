import { Module } from '@nestjs/common';
import { TerminalsService } from './terminals.service';
import { TerminalsController } from './terminals.controller';
import { TerminalsGateway } from './terminals.gateway';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [CommonModule],
  providers: [TerminalsService, TerminalsGateway],
  controllers: [TerminalsController],
})
export class TerminalsModule {}
