global.WebSocket = require('ws');

import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { AdminHttpGuard } from 'src/guards/auth.guard';
import { LocalIpGuard } from 'src/guards/ip.guard';
import { TerminalsService } from './terminals.service';
import {
  InitTerminalDto,
  KillTerminalDto,
  TerminalLoginDto,
} from './utils/dto.utils';

@Controller('terminals')
export class TerminalsController {
  constructor(private terminalsService: TerminalsService) {}

  @UseGuards(AdminHttpGuard)
  @Post('init')
  async initNewTerminal(@Body() body: InitTerminalDto) {
    const data = await this.terminalsService.initNewTerminal(body);
    return {
      data,
      message: 'Creating terminal',
    };
  }

  @UseGuards(AdminHttpGuard)
  @Post('reset-all')
  async handleResetTerminals() {
    const data = await this.terminalsService.handleKillAllTerminals();
    return {
      data,
      message: 'Resetting all terminals',
    };
  }


  @UseGuards(AdminHttpGuard)
  @Post('restart')
  async restartTerminal(@Body() body: KillTerminalDto) {
    await this.terminalsService.restartTerminal(body);
    return {
      message: 'Restarting terminal',
    };
  }

  @UseGuards(AdminHttpGuard)
  @Post('set')
  async setTerminals(@Body() body: any) {
    await this.terminalsService.setTerminals(body);
    return {
      message: 'Setting terminals',
    };
  }

  @UseGuards(AdminHttpGuard)
  @Get('')
  async getTerminals() {
    const terminals = await this.terminalsService.getTerminals();
    return {
      terminals,
      message: 'Getting terminals',
    };
  }

  @UseGuards(AdminHttpGuard)
  @Post('kill')
  async killTerminal(@Body() body: KillTerminalDto) {
    await this.terminalsService.killTerminal(body);
    return {
      message: 'Killing terminal',
    };
  }

  // callbacks

  @UseGuards(LocalIpGuard)
  @Post('callbacks/login')
  async handleTerminalLogin(@Body() body: TerminalLoginDto) {
    const data = await this.terminalsService.terminalLoginCallback(body);
    return {
      data,
    };
  }

  @UseGuards(LocalIpGuard)
  @Post('callbacks/kill')
  async handleKillTerminal(@Body() body: KillTerminalDto) {
    const data = await this.terminalsService.killTerminalCallback(body);
    return {
      data,
    };
  }
}
