import {
  BadRequestException,
  Injectable,
  PreconditionFailedException,
} from '@nestjs/common';
import {
  InitTerminalDto,
  KillTerminalDto,
  TerminalLoginDto,
} from './utils/dto.utils';
import axios, { AxiosInstance } from 'axios';
import { delay, exec, generateRandomId } from 'src/utils/functions';
import { isProduction } from 'src/utils/constants';
import { serverPort } from 'src/main';

export const MemCache = {
  totalConnected: 0,
  activePorts: [] as number[],
  clients: {} as {
    [key: string]: {
      callback?: string;
      id?: string;
      bridge_port?: number;
      rpc_port?: number;
      terminal_type?: string;
      terminal_port?: number;
      login_info?: InitTerminalDto;
      isRestart?: boolean;
      connected?: boolean;
      metadata?: any;
    };
  },
  providers: {} as {
    [key: string]: {
      id: string;
      metadata: any;
    };
  },
};

@Injectable()
export class TerminalsService {
  private axios: AxiosInstance;
  private maxConnections: number;
  private startWsPort: number;
  private startRpcPort: number;
  private startTerminalPort: number;
  private scriptDir: string;

  constructor() {
    this.axios = axios.create({});
    this.maxConnections = Number(process.env.MAX_CONNECTIONS) || 10;
    this.startWsPort = Number(process.env.START_WS_PORT) || 8100;
    this.startRpcPort = Number(process.env.START_RPC_PORT) || 50050;
    this.startTerminalPort = Number(process.env.START_TERMINAL_PORT) || 5010;

    this.scriptDir = isProduction
      ? './node-manager/dist/scripts'
      : 'scripts/local';

    console.log(this.scriptDir, '');
  }

  async initNewTerminal(dto: InitTerminalDto, isRestart = false) {
    if (MemCache.totalConnected > this.maxConnections) {
      throw new PreconditionFailedException('Max connections reached');
    }

    const findFreePort = () => {
      for (let i = 0; i < this.maxConnections; i++) {
        const port = this.startWsPort + i;
        if (!MemCache.activePorts.includes(port)) {
          return port;
        }
      }
    };

    const bridgePort = findFreePort()!;
    const rpcPort = this.startRpcPort + (bridgePort - this.startWsPort);
    const terminalPort =
      this.startTerminalPort + (bridgePort - this.startWsPort);
    const terminalId = generateRandomId();

    function shQuote(str: string) {
      // wrap in single-quotes and escape any embedded single-quote
      return `'${str.replace(/'/g, `'\\''`)}'`;
    }

    const args = {
      terminal_id: terminalId,
      login: dto.login,
      password: shQuote(dto.password),
      server: dto.server,
      login_callback: `http://localhost:${serverPort}/terminals/callbacks/login`,
      terminate_callback: `http://localhost:${serverPort}/terminals/callbacks/kill`,
      bridge_port: bridgePort, // for talking to the python bridge (shutting down the python server)
      rpc_port: rpcPort, // for talking to the rpc server
      auth_token: dto.auth_token,
      terminal_type: dto.terminal_type,
      terminal_port: terminalPort, // websocket server port for mt4 expert advisor,
    };

    await exec('sudo chmod ', this.scriptDir, ['+x', ' ./launcher.sh']);
    exec(
      `${isProduction ? 'sudo' : ''} ./launcher.sh`,
      this.scriptDir,
      [...Object.values(args).map((v) => `${v}`)],
      undefined,
      true,
    );

    MemCache.totalConnected++;
    MemCache.clients[terminalId] = {
      callback: dto.callback,
      id: terminalId,
      bridge_port: bridgePort,
      rpc_port: rpcPort,
      terminal_port: 8222,
      terminal_type: dto.terminal_type,
      login_info: { ...dto },
      isRestart,
      connected: false,
    };
    MemCache.activePorts.push(bridgePort);
  }

  async terminalLoginCallback(dto: TerminalLoginDto) {
    console.log(dto, 'terminal login');
    const client = MemCache.clients[dto.terminal_id];
    if (client) {
      MemCache.clients[dto.terminal_id].connected = true;
      const callback = client.callback;
      this.axios.post(callback, {
        ws_port: dto.ws_port,
        rpc_port: dto.rpc_port,
        terminal_id: dto.terminal_id,
        status: 1,
        message: 'Connected',
      });
    }
  }

  async killTerminal(dto: KillTerminalDto) {
    const client = MemCache.clients[dto.terminal_id];
    if (client) {
      const port = client?.bridge_port;
      const data = await fetch(`http://localhost:${port}/shutdown`);
      await this.killTerminalCallback(dto, false);
      return data;
    }
  }

  async killTerminalCallback(dto: KillTerminalDto, postToCallback = true) {
    const client = MemCache.clients[dto.terminal_id];
    if (client) {
      MemCache.activePorts = MemCache.activePorts.filter(
        (p) => p.toString() !== client.bridge_port.toString(),
      );

      delete MemCache.clients[client.id];
      MemCache.totalConnected--;

      await exec('sudo chmod ', this.scriptDir, ['+x', ' ./terminate.sh']);
      await exec(
        `sudo ./terminate.sh ${client.bridge_port} ${client.terminal_type}`,
        this.scriptDir,
      );

      if (!postToCallback) return;

      const callback = client.callback;
      this.axios.post(callback, {
        ws_port: client.bridge_port,
        rpc_port: client.rpc_port,
        terminal_id: dto.terminal_id,
        status: 0,
        message: 'Login Failed',
      });
    }
  }

  async restartTerminal(dto: KillTerminalDto) {
    const terminalData = MemCache.clients[dto.terminal_id];
    if (terminalData) {
      const terminalDataCopy = { ...terminalData };
      await this.killTerminal(dto);
      await this.killTerminalCallback(dto);
      await delay(5000);
      await this.initNewTerminal(terminalDataCopy.login_info, true);
    } else throw new BadRequestException('Terminal does not exist');
  }

  async getTerminals() {
    return { ...MemCache, providers: undefined };
  }

  async setTerminals(data: any) {
    Object.assign(MemCache, data);
  }

  async handleKillAllTerminals() {
    try {
      const clients = Object.values(MemCache.clients);
      for (const client of clients) {
        await this.killTerminalCallback({ terminal_id: client.id });
      }
      await exec(`${isProduction ? 'sudo' : ''} wineserver -k`, './');
    } catch (error) {
      if (error.code) {
        return {
          message: 'No active programs running',
          details: {
            code: error.code,
            stdout: error.stdout,
            stderr: error.stderr,
          },
        };
      } else {
        return {
          message: 'No active programs running',
          error: error.message,
        };
      }
    }
  }
}
