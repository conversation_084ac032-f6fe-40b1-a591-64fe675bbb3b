// @generated by protobuf-ts 2.9.4 with parameter server_grpc1,client_none,optimize_code_size
// @generated from protobuf file "bridge.proto" (package "bridge", syntax proto3)
// tslint:disable
import { GetSymbolTickRequest } from "./bridge";
import { ManageSymbolResponse } from "./bridge";
import { ManageSymbolRequest } from "./bridge";
import { PlaceTradeResponse } from "./bridge";
import { PlaceTradeRequest } from "./bridge";
import { ModifyTradeRequest } from "./bridge";
import { CloseTradeRequest } from "./bridge";
import { GetTicksRangeRequest } from "./bridge";
import { GetTicksResponse } from "./bridge";
import { GetTicksFromRequest } from "./bridge";
import { GetTradeHistoryResponse } from "./bridge";
import { GetTradeHistoryRequest } from "./bridge";
import { GetAvailableSymbolsResponse } from "./bridge";
import { GetOrdersResponse } from "./bridge";
import { GetPositionsResponse } from "./bridge";
import { GetAccountResponse } from "./bridge";
import { GenericResponseType } from "./bridge";
import { EmptyType } from "./bridge";
import { TickType } from "./bridge";
import { TickStartRequest } from "./bridge";
import type * as grpc from "@grpc/grpc-js";
/**
 * service
 *
 * @generated from protobuf service bridge.BridgeRpcService
 */
export interface IBridgeRpcService extends grpc.UntypedServiceImplementation {
    /**
     * @generated from protobuf rpc: TickStart(bridge.TickStartRequest) returns (stream bridge.TickType);
     */
    tickStart: grpc.handleServerStreamingCall<TickStartRequest, TickType>;
    /**
     * @generated from protobuf rpc: TickStop(bridge.EmptyType) returns (bridge.GenericResponseType);
     */
    tickStop: grpc.handleUnaryCall<EmptyType, GenericResponseType>;
    /**
     * @generated from protobuf rpc: GetAccount(bridge.EmptyType) returns (bridge.GetAccountResponse);
     */
    getAccount: grpc.handleUnaryCall<EmptyType, GetAccountResponse>;
    /**
     * @generated from protobuf rpc: GetPositions(bridge.EmptyType) returns (bridge.GetPositionsResponse);
     */
    getPositions: grpc.handleUnaryCall<EmptyType, GetPositionsResponse>;
    /**
     * @generated from protobuf rpc: GetOrders(bridge.EmptyType) returns (bridge.GetOrdersResponse);
     */
    getOrders: grpc.handleUnaryCall<EmptyType, GetOrdersResponse>;
    /**
     * @generated from protobuf rpc: GetAvailableSymbols(bridge.EmptyType) returns (bridge.GetAvailableSymbolsResponse);
     */
    getAvailableSymbols: grpc.handleUnaryCall<EmptyType, GetAvailableSymbolsResponse>;
    /**
     * @generated from protobuf rpc: GetTradeHistory(bridge.GetTradeHistoryRequest) returns (bridge.GetTradeHistoryResponse);
     */
    getTradeHistory: grpc.handleUnaryCall<GetTradeHistoryRequest, GetTradeHistoryResponse>;
    /**
     * @generated from protobuf rpc: GetTicksFrom(bridge.GetTicksFromRequest) returns (bridge.GetTicksResponse);
     */
    getTicksFrom: grpc.handleUnaryCall<GetTicksFromRequest, GetTicksResponse>;
    /**
     * @generated from protobuf rpc: GetTicksRange(bridge.GetTicksRangeRequest) returns (bridge.GetTicksResponse);
     */
    getTicksRange: grpc.handleUnaryCall<GetTicksRangeRequest, GetTicksResponse>;
    /**
     * @generated from protobuf rpc: CloseTrade(bridge.CloseTradeRequest) returns (bridge.GenericResponseType);
     */
    closeTrade: grpc.handleUnaryCall<CloseTradeRequest, GenericResponseType>;
    /**
     * @generated from protobuf rpc: ModifyTrade(bridge.ModifyTradeRequest) returns (bridge.GenericResponseType);
     */
    modifyTrade: grpc.handleUnaryCall<ModifyTradeRequest, GenericResponseType>;
    /**
     * @generated from protobuf rpc: PlaceTrade(bridge.PlaceTradeRequest) returns (bridge.PlaceTradeResponse);
     */
    placeTrade: grpc.handleUnaryCall<PlaceTradeRequest, PlaceTradeResponse>;
    /**
     * @generated from protobuf rpc: ManageSymbol(bridge.ManageSymbolRequest) returns (bridge.ManageSymbolResponse);
     */
    manageSymbol: grpc.handleUnaryCall<ManageSymbolRequest, ManageSymbolResponse>;
    /**
     * @generated from protobuf rpc: GetTerminalError(bridge.EmptyType) returns (bridge.GenericResponseType);
     */
    getTerminalError: grpc.handleUnaryCall<EmptyType, GenericResponseType>;
    /**
     * @generated from protobuf rpc: GetSymbolTick(bridge.GetSymbolTickRequest) returns (bridge.TickType);
     */
    getSymbolTick: grpc.handleUnaryCall<GetSymbolTickRequest, TickType>;
}
/**
 * @grpc/grpc-js definition for the protobuf service bridge.BridgeRpcService.
 *
 * Usage: Implement the interface IBridgeRpcService and add to a grpc server.
 *
 * ```typescript
 * const server = new grpc.Server();
 * const service: IBridgeRpcService = ...
 * server.addService(bridgeRpcServiceDefinition, service);
 * ```
 */
export const bridgeRpcServiceDefinition: grpc.ServiceDefinition<IBridgeRpcService> = {
    tickStart: {
        path: "/bridge.BridgeRpcService/TickStart",
        originalName: "TickStart",
        requestStream: false,
        responseStream: true,
        responseDeserialize: bytes => TickType.fromBinary(bytes),
        requestDeserialize: bytes => TickStartRequest.fromBinary(bytes),
        responseSerialize: value => Buffer.from(TickType.toBinary(value)),
        requestSerialize: value => Buffer.from(TickStartRequest.toBinary(value))
    },
    tickStop: {
        path: "/bridge.BridgeRpcService/TickStop",
        originalName: "TickStop",
        requestStream: false,
        responseStream: false,
        responseDeserialize: bytes => GenericResponseType.fromBinary(bytes),
        requestDeserialize: bytes => EmptyType.fromBinary(bytes),
        responseSerialize: value => Buffer.from(GenericResponseType.toBinary(value)),
        requestSerialize: value => Buffer.from(EmptyType.toBinary(value))
    },
    getAccount: {
        path: "/bridge.BridgeRpcService/GetAccount",
        originalName: "GetAccount",
        requestStream: false,
        responseStream: false,
        responseDeserialize: bytes => GetAccountResponse.fromBinary(bytes),
        requestDeserialize: bytes => EmptyType.fromBinary(bytes),
        responseSerialize: value => Buffer.from(GetAccountResponse.toBinary(value)),
        requestSerialize: value => Buffer.from(EmptyType.toBinary(value))
    },
    getPositions: {
        path: "/bridge.BridgeRpcService/GetPositions",
        originalName: "GetPositions",
        requestStream: false,
        responseStream: false,
        responseDeserialize: bytes => GetPositionsResponse.fromBinary(bytes),
        requestDeserialize: bytes => EmptyType.fromBinary(bytes),
        responseSerialize: value => Buffer.from(GetPositionsResponse.toBinary(value)),
        requestSerialize: value => Buffer.from(EmptyType.toBinary(value))
    },
    getOrders: {
        path: "/bridge.BridgeRpcService/GetOrders",
        originalName: "GetOrders",
        requestStream: false,
        responseStream: false,
        responseDeserialize: bytes => GetOrdersResponse.fromBinary(bytes),
        requestDeserialize: bytes => EmptyType.fromBinary(bytes),
        responseSerialize: value => Buffer.from(GetOrdersResponse.toBinary(value)),
        requestSerialize: value => Buffer.from(EmptyType.toBinary(value))
    },
    getAvailableSymbols: {
        path: "/bridge.BridgeRpcService/GetAvailableSymbols",
        originalName: "GetAvailableSymbols",
        requestStream: false,
        responseStream: false,
        responseDeserialize: bytes => GetAvailableSymbolsResponse.fromBinary(bytes),
        requestDeserialize: bytes => EmptyType.fromBinary(bytes),
        responseSerialize: value => Buffer.from(GetAvailableSymbolsResponse.toBinary(value)),
        requestSerialize: value => Buffer.from(EmptyType.toBinary(value))
    },
    getTradeHistory: {
        path: "/bridge.BridgeRpcService/GetTradeHistory",
        originalName: "GetTradeHistory",
        requestStream: false,
        responseStream: false,
        responseDeserialize: bytes => GetTradeHistoryResponse.fromBinary(bytes),
        requestDeserialize: bytes => GetTradeHistoryRequest.fromBinary(bytes),
        responseSerialize: value => Buffer.from(GetTradeHistoryResponse.toBinary(value)),
        requestSerialize: value => Buffer.from(GetTradeHistoryRequest.toBinary(value))
    },
    getTicksFrom: {
        path: "/bridge.BridgeRpcService/GetTicksFrom",
        originalName: "GetTicksFrom",
        requestStream: false,
        responseStream: false,
        responseDeserialize: bytes => GetTicksResponse.fromBinary(bytes),
        requestDeserialize: bytes => GetTicksFromRequest.fromBinary(bytes),
        responseSerialize: value => Buffer.from(GetTicksResponse.toBinary(value)),
        requestSerialize: value => Buffer.from(GetTicksFromRequest.toBinary(value))
    },
    getTicksRange: {
        path: "/bridge.BridgeRpcService/GetTicksRange",
        originalName: "GetTicksRange",
        requestStream: false,
        responseStream: false,
        responseDeserialize: bytes => GetTicksResponse.fromBinary(bytes),
        requestDeserialize: bytes => GetTicksRangeRequest.fromBinary(bytes),
        responseSerialize: value => Buffer.from(GetTicksResponse.toBinary(value)),
        requestSerialize: value => Buffer.from(GetTicksRangeRequest.toBinary(value))
    },
    closeTrade: {
        path: "/bridge.BridgeRpcService/CloseTrade",
        originalName: "CloseTrade",
        requestStream: false,
        responseStream: false,
        responseDeserialize: bytes => GenericResponseType.fromBinary(bytes),
        requestDeserialize: bytes => CloseTradeRequest.fromBinary(bytes),
        responseSerialize: value => Buffer.from(GenericResponseType.toBinary(value)),
        requestSerialize: value => Buffer.from(CloseTradeRequest.toBinary(value))
    },
    modifyTrade: {
        path: "/bridge.BridgeRpcService/ModifyTrade",
        originalName: "ModifyTrade",
        requestStream: false,
        responseStream: false,
        responseDeserialize: bytes => GenericResponseType.fromBinary(bytes),
        requestDeserialize: bytes => ModifyTradeRequest.fromBinary(bytes),
        responseSerialize: value => Buffer.from(GenericResponseType.toBinary(value)),
        requestSerialize: value => Buffer.from(ModifyTradeRequest.toBinary(value))
    },
    placeTrade: {
        path: "/bridge.BridgeRpcService/PlaceTrade",
        originalName: "PlaceTrade",
        requestStream: false,
        responseStream: false,
        responseDeserialize: bytes => PlaceTradeResponse.fromBinary(bytes),
        requestDeserialize: bytes => PlaceTradeRequest.fromBinary(bytes),
        responseSerialize: value => Buffer.from(PlaceTradeResponse.toBinary(value)),
        requestSerialize: value => Buffer.from(PlaceTradeRequest.toBinary(value))
    },
    manageSymbol: {
        path: "/bridge.BridgeRpcService/ManageSymbol",
        originalName: "ManageSymbol",
        requestStream: false,
        responseStream: false,
        responseDeserialize: bytes => ManageSymbolResponse.fromBinary(bytes),
        requestDeserialize: bytes => ManageSymbolRequest.fromBinary(bytes),
        responseSerialize: value => Buffer.from(ManageSymbolResponse.toBinary(value)),
        requestSerialize: value => Buffer.from(ManageSymbolRequest.toBinary(value))
    },
    getTerminalError: {
        path: "/bridge.BridgeRpcService/GetTerminalError",
        originalName: "GetTerminalError",
        requestStream: false,
        responseStream: false,
        responseDeserialize: bytes => GenericResponseType.fromBinary(bytes),
        requestDeserialize: bytes => EmptyType.fromBinary(bytes),
        responseSerialize: value => Buffer.from(GenericResponseType.toBinary(value)),
        requestSerialize: value => Buffer.from(EmptyType.toBinary(value))
    },
    getSymbolTick: {
        path: "/bridge.BridgeRpcService/GetSymbolTick",
        originalName: "GetSymbolTick",
        requestStream: false,
        responseStream: false,
        responseDeserialize: bytes => TickType.fromBinary(bytes),
        requestDeserialize: bytes => GetSymbolTickRequest.fromBinary(bytes),
        responseSerialize: value => Buffer.from(TickType.toBinary(value)),
        requestSerialize: value => Buffer.from(GetSymbolTickRequest.toBinary(value))
    }
};
