// @generated by protobuf-ts 2.9.4
// @generated from protobuf file "bridge.proto" (package "bridge", syntax proto3)
// tslint:disable
import type { RpcTransport } from "@protobuf-ts/runtime-rpc";
import type { ServiceInfo } from "@protobuf-ts/runtime-rpc";
import { BridgeRpcService } from "./bridge";
import type { GetSymbolTickRequest } from "./bridge";
import type { ManageSymbolResponse } from "./bridge";
import type { ManageSymbolRequest } from "./bridge";
import type { PlaceTradeResponse } from "./bridge";
import type { PlaceTradeRequest } from "./bridge";
import type { ModifyTradeRequest } from "./bridge";
import type { CloseTradeRequest } from "./bridge";
import type { GetTicksRangeRequest } from "./bridge";
import type { GetTicksResponse } from "./bridge";
import type { GetTicksFromRequest } from "./bridge";
import type { GetTradeHistoryResponse } from "./bridge";
import type { GetTradeHistoryRequest } from "./bridge";
import type { GetAvailableSymbolsResponse } from "./bridge";
import type { GetOrdersResponse } from "./bridge";
import type { GetPositionsResponse } from "./bridge";
import type { GetAccountResponse } from "./bridge";
import type { GenericResponseType } from "./bridge";
import type { EmptyType } from "./bridge";
import type { UnaryCall } from "@protobuf-ts/runtime-rpc";
import { stackIntercept } from "@protobuf-ts/runtime-rpc";
import type { TickType } from "./bridge";
import type { TickStartRequest } from "./bridge";
import type { ServerStreamingCall } from "@protobuf-ts/runtime-rpc";
import type { RpcOptions } from "@protobuf-ts/runtime-rpc";
/**
 * service
 *
 * @generated from protobuf service bridge.BridgeRpcService
 */
export interface IBridgeRpcServiceClient {
    /**
     * @generated from protobuf rpc: TickStart(bridge.TickStartRequest) returns (stream bridge.TickType);
     */
    tickStart(input: TickStartRequest, options?: RpcOptions): ServerStreamingCall<TickStartRequest, TickType>;
    /**
     * @generated from protobuf rpc: TickStop(bridge.EmptyType) returns (bridge.GenericResponseType);
     */
    tickStop(input: EmptyType, options?: RpcOptions): UnaryCall<EmptyType, GenericResponseType>;
    /**
     * @generated from protobuf rpc: GetAccount(bridge.EmptyType) returns (bridge.GetAccountResponse);
     */
    getAccount(input: EmptyType, options?: RpcOptions): UnaryCall<EmptyType, GetAccountResponse>;
    /**
     * @generated from protobuf rpc: GetPositions(bridge.EmptyType) returns (bridge.GetPositionsResponse);
     */
    getPositions(input: EmptyType, options?: RpcOptions): UnaryCall<EmptyType, GetPositionsResponse>;
    /**
     * @generated from protobuf rpc: GetOrders(bridge.EmptyType) returns (bridge.GetOrdersResponse);
     */
    getOrders(input: EmptyType, options?: RpcOptions): UnaryCall<EmptyType, GetOrdersResponse>;
    /**
     * @generated from protobuf rpc: GetAvailableSymbols(bridge.EmptyType) returns (bridge.GetAvailableSymbolsResponse);
     */
    getAvailableSymbols(input: EmptyType, options?: RpcOptions): UnaryCall<EmptyType, GetAvailableSymbolsResponse>;
    /**
     * @generated from protobuf rpc: GetTradeHistory(bridge.GetTradeHistoryRequest) returns (bridge.GetTradeHistoryResponse);
     */
    getTradeHistory(input: GetTradeHistoryRequest, options?: RpcOptions): UnaryCall<GetTradeHistoryRequest, GetTradeHistoryResponse>;
    /**
     * @generated from protobuf rpc: GetTicksFrom(bridge.GetTicksFromRequest) returns (bridge.GetTicksResponse);
     */
    getTicksFrom(input: GetTicksFromRequest, options?: RpcOptions): UnaryCall<GetTicksFromRequest, GetTicksResponse>;
    /**
     * @generated from protobuf rpc: GetTicksRange(bridge.GetTicksRangeRequest) returns (bridge.GetTicksResponse);
     */
    getTicksRange(input: GetTicksRangeRequest, options?: RpcOptions): UnaryCall<GetTicksRangeRequest, GetTicksResponse>;
    /**
     * @generated from protobuf rpc: CloseTrade(bridge.CloseTradeRequest) returns (bridge.GenericResponseType);
     */
    closeTrade(input: CloseTradeRequest, options?: RpcOptions): UnaryCall<CloseTradeRequest, GenericResponseType>;
    /**
     * @generated from protobuf rpc: ModifyTrade(bridge.ModifyTradeRequest) returns (bridge.GenericResponseType);
     */
    modifyTrade(input: ModifyTradeRequest, options?: RpcOptions): UnaryCall<ModifyTradeRequest, GenericResponseType>;
    /**
     * @generated from protobuf rpc: PlaceTrade(bridge.PlaceTradeRequest) returns (bridge.PlaceTradeResponse);
     */
    placeTrade(input: PlaceTradeRequest, options?: RpcOptions): UnaryCall<PlaceTradeRequest, PlaceTradeResponse>;
    /**
     * @generated from protobuf rpc: ManageSymbol(bridge.ManageSymbolRequest) returns (bridge.ManageSymbolResponse);
     */
    manageSymbol(input: ManageSymbolRequest, options?: RpcOptions): UnaryCall<ManageSymbolRequest, ManageSymbolResponse>;
    /**
     * @generated from protobuf rpc: GetTerminalError(bridge.EmptyType) returns (bridge.GenericResponseType);
     */
    getTerminalError(input: EmptyType, options?: RpcOptions): UnaryCall<EmptyType, GenericResponseType>;
    /**
     * @generated from protobuf rpc: GetSymbolTick(bridge.GetSymbolTickRequest) returns (bridge.TickType);
     */
    getSymbolTick(input: GetSymbolTickRequest, options?: RpcOptions): UnaryCall<GetSymbolTickRequest, TickType>;
}
/**
 * service
 *
 * @generated from protobuf service bridge.BridgeRpcService
 */
export class BridgeRpcServiceClient implements IBridgeRpcServiceClient, ServiceInfo {
    typeName = BridgeRpcService.typeName;
    methods = BridgeRpcService.methods;
    options = BridgeRpcService.options;
    constructor(private readonly _transport: RpcTransport) {
    }
    /**
     * @generated from protobuf rpc: TickStart(bridge.TickStartRequest) returns (stream bridge.TickType);
     */
    tickStart(input: TickStartRequest, options?: RpcOptions): ServerStreamingCall<TickStartRequest, TickType> {
        const method = this.methods[0], opt = this._transport.mergeOptions(options);
        return stackIntercept<TickStartRequest, TickType>("serverStreaming", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: TickStop(bridge.EmptyType) returns (bridge.GenericResponseType);
     */
    tickStop(input: EmptyType, options?: RpcOptions): UnaryCall<EmptyType, GenericResponseType> {
        const method = this.methods[1], opt = this._transport.mergeOptions(options);
        return stackIntercept<EmptyType, GenericResponseType>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: GetAccount(bridge.EmptyType) returns (bridge.GetAccountResponse);
     */
    getAccount(input: EmptyType, options?: RpcOptions): UnaryCall<EmptyType, GetAccountResponse> {
        const method = this.methods[2], opt = this._transport.mergeOptions(options);
        return stackIntercept<EmptyType, GetAccountResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: GetPositions(bridge.EmptyType) returns (bridge.GetPositionsResponse);
     */
    getPositions(input: EmptyType, options?: RpcOptions): UnaryCall<EmptyType, GetPositionsResponse> {
        const method = this.methods[3], opt = this._transport.mergeOptions(options);
        return stackIntercept<EmptyType, GetPositionsResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: GetOrders(bridge.EmptyType) returns (bridge.GetOrdersResponse);
     */
    getOrders(input: EmptyType, options?: RpcOptions): UnaryCall<EmptyType, GetOrdersResponse> {
        const method = this.methods[4], opt = this._transport.mergeOptions(options);
        return stackIntercept<EmptyType, GetOrdersResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: GetAvailableSymbols(bridge.EmptyType) returns (bridge.GetAvailableSymbolsResponse);
     */
    getAvailableSymbols(input: EmptyType, options?: RpcOptions): UnaryCall<EmptyType, GetAvailableSymbolsResponse> {
        const method = this.methods[5], opt = this._transport.mergeOptions(options);
        return stackIntercept<EmptyType, GetAvailableSymbolsResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: GetTradeHistory(bridge.GetTradeHistoryRequest) returns (bridge.GetTradeHistoryResponse);
     */
    getTradeHistory(input: GetTradeHistoryRequest, options?: RpcOptions): UnaryCall<GetTradeHistoryRequest, GetTradeHistoryResponse> {
        const method = this.methods[6], opt = this._transport.mergeOptions(options);
        return stackIntercept<GetTradeHistoryRequest, GetTradeHistoryResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: GetTicksFrom(bridge.GetTicksFromRequest) returns (bridge.GetTicksResponse);
     */
    getTicksFrom(input: GetTicksFromRequest, options?: RpcOptions): UnaryCall<GetTicksFromRequest, GetTicksResponse> {
        const method = this.methods[7], opt = this._transport.mergeOptions(options);
        return stackIntercept<GetTicksFromRequest, GetTicksResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: GetTicksRange(bridge.GetTicksRangeRequest) returns (bridge.GetTicksResponse);
     */
    getTicksRange(input: GetTicksRangeRequest, options?: RpcOptions): UnaryCall<GetTicksRangeRequest, GetTicksResponse> {
        const method = this.methods[8], opt = this._transport.mergeOptions(options);
        return stackIntercept<GetTicksRangeRequest, GetTicksResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: CloseTrade(bridge.CloseTradeRequest) returns (bridge.GenericResponseType);
     */
    closeTrade(input: CloseTradeRequest, options?: RpcOptions): UnaryCall<CloseTradeRequest, GenericResponseType> {
        const method = this.methods[9], opt = this._transport.mergeOptions(options);
        return stackIntercept<CloseTradeRequest, GenericResponseType>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: ModifyTrade(bridge.ModifyTradeRequest) returns (bridge.GenericResponseType);
     */
    modifyTrade(input: ModifyTradeRequest, options?: RpcOptions): UnaryCall<ModifyTradeRequest, GenericResponseType> {
        const method = this.methods[10], opt = this._transport.mergeOptions(options);
        return stackIntercept<ModifyTradeRequest, GenericResponseType>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: PlaceTrade(bridge.PlaceTradeRequest) returns (bridge.PlaceTradeResponse);
     */
    placeTrade(input: PlaceTradeRequest, options?: RpcOptions): UnaryCall<PlaceTradeRequest, PlaceTradeResponse> {
        const method = this.methods[11], opt = this._transport.mergeOptions(options);
        return stackIntercept<PlaceTradeRequest, PlaceTradeResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: ManageSymbol(bridge.ManageSymbolRequest) returns (bridge.ManageSymbolResponse);
     */
    manageSymbol(input: ManageSymbolRequest, options?: RpcOptions): UnaryCall<ManageSymbolRequest, ManageSymbolResponse> {
        const method = this.methods[12], opt = this._transport.mergeOptions(options);
        return stackIntercept<ManageSymbolRequest, ManageSymbolResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: GetTerminalError(bridge.EmptyType) returns (bridge.GenericResponseType);
     */
    getTerminalError(input: EmptyType, options?: RpcOptions): UnaryCall<EmptyType, GenericResponseType> {
        const method = this.methods[13], opt = this._transport.mergeOptions(options);
        return stackIntercept<EmptyType, GenericResponseType>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: GetSymbolTick(bridge.GetSymbolTickRequest) returns (bridge.TickType);
     */
    getSymbolTick(input: GetSymbolTickRequest, options?: RpcOptions): UnaryCall<GetSymbolTickRequest, TickType> {
        const method = this.methods[14], opt = this._transport.mergeOptions(options);
        return stackIntercept<GetSymbolTickRequest, TickType>("unary", this._transport, method, opt, input);
    }
}
