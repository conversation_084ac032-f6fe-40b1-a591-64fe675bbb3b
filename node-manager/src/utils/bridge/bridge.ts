// @generated by protobuf-ts 2.9.4 with parameter server_grpc1,client_none,optimize_code_size
// @generated from protobuf file "bridge.proto" (package "bridge", syntax proto3)
// tslint:disable
import { ServiceType } from "@protobuf-ts/runtime-rpc";
import { MessageType } from "@protobuf-ts/runtime";
/**
 * types
 *
 * @generated from protobuf message bridge.EmptyType
 */
export interface EmptyType {
}
/**
 * @generated from protobuf message bridge.TickType
 */
export interface TickType {
    /**
     * @generated from protobuf field: float ask = 1;
     */
    ask: number;
    /**
     * @generated from protobuf field: float bid = 2;
     */
    bid: number;
    /**
     * @generated from protobuf field: int64 time = 3;
     */
    time: bigint;
}
/**
 * @generated from protobuf message bridge.AccountPositionType
 */
export interface AccountPositionType {
    /**
     * @generated from protobuf field: int64 ticket = 1;
     */
    ticket: bigint;
    /**
     * @generated from protobuf field: string symbol = 2;
     */
    symbol: string;
    /**
     * @generated from protobuf field: int64 type = 3;
     */
    type: bigint;
    /**
     * @generated from protobuf field: float volume = 4;
     */
    volume: number;
    /**
     * @generated from protobuf field: float open_price = 5;
     */
    openPrice: number;
    /**
     * @generated from protobuf field: float current_price = 6;
     */
    currentPrice: number;
    /**
     * @generated from protobuf field: float stop_loss = 7;
     */
    stopLoss: number;
    /**
     * @generated from protobuf field: float take_profit = 8;
     */
    takeProfit: number;
    /**
     * @generated from protobuf field: float profit = 9;
     */
    profit: number;
    /**
     * @generated from protobuf field: string comment = 10;
     */
    comment: string;
    /**
     * @generated from protobuf field: int64 magic = 11;
     */
    magic: bigint;
}
/**
 * @generated from protobuf message bridge.AccountOrdersType
 */
export interface AccountOrdersType {
    /**
     * @generated from protobuf field: int64 ticket = 1;
     */
    ticket: bigint;
    /**
     * @generated from protobuf field: string symbol = 2;
     */
    symbol: string;
    /**
     * @generated from protobuf field: int64 type = 3;
     */
    type: bigint;
    /**
     * @generated from protobuf field: float volume = 4;
     */
    volume: number;
    /**
     * @generated from protobuf field: float price_open = 5;
     */
    priceOpen: number;
    /**
     * @generated from protobuf field: float price_current = 6;
     */
    priceCurrent: number;
    /**
     * @generated from protobuf field: float stop_loss = 7;
     */
    stopLoss: number;
    /**
     * @generated from protobuf field: float take_profit = 8;
     */
    takeProfit: number;
    /**
     * @generated from protobuf field: string comment = 9;
     */
    comment: string;
    /**
     * @generated from protobuf field: int64 time_setup = 10;
     */
    timeSetup: bigint;
    /**
     * @generated from protobuf field: int64 time_expiration = 11;
     */
    timeExpiration: bigint;
    /**
     * @generated from protobuf field: int64 magic = 12;
     */
    magic: bigint;
}
/**
 * @generated from protobuf message bridge.SymbolsType
 */
export interface SymbolsType {
    /**
     * @generated from protobuf field: string name = 1;
     */
    name: string;
    /**
     * @generated from protobuf field: string description = 2;
     */
    description: string;
    /**
     * @generated from protobuf field: string path = 3;
     */
    path: string;
    /**
     * @generated from protobuf field: int64 digits = 4;
     */
    digits: bigint;
    /**
     * @generated from protobuf field: float spread = 5;
     */
    spread: number;
    /**
     * @generated from protobuf field: int64 time = 6;
     */
    time: bigint;
}
/**
 * @generated from protobuf message bridge.TradeDealType
 */
export interface TradeDealType {
    /**
     * @generated from protobuf field: int64 ticket = 1;
     */
    ticket: bigint;
    /**
     * @generated from protobuf field: int64 order = 2;
     */
    order: bigint;
    /**
     * @generated from protobuf field: int64 time = 3;
     */
    time: bigint;
    /**
     * @generated from protobuf field: int64 type = 4;
     */
    type: bigint;
    /**
     * @generated from protobuf field: float entry = 5;
     */
    entry: number;
    /**
     * @generated from protobuf field: string symbol = 6;
     */
    symbol: string;
    /**
     * @generated from protobuf field: float volume = 7;
     */
    volume: number;
    /**
     * @generated from protobuf field: float price = 8;
     */
    price: number;
    /**
     * @generated from protobuf field: float commission = 9;
     */
    commission: number;
    /**
     * @generated from protobuf field: float swap = 10;
     */
    swap: number;
    /**
     * @generated from protobuf field: float profit = 11;
     */
    profit: number;
    /**
     * @generated from protobuf field: int64 magic = 12;
     */
    magic: bigint;
    /**
     * @generated from protobuf field: string comment = 13;
     */
    comment: string;
}
// request messages

/**
 * @generated from protobuf message bridge.GetSymbolTickRequest
 */
export interface GetSymbolTickRequest {
    /**
     * @generated from protobuf field: string symbol = 1;
     */
    symbol: string;
}
/**
 * @generated from protobuf message bridge.TickStartRequest
 */
export interface TickStartRequest {
    /**
     * @generated from protobuf field: string symbol = 1;
     */
    symbol: string;
    /**
     * @generated from protobuf field: float rate = 2;
     */
    rate: number;
}
/**
 * @generated from protobuf message bridge.CloseTradeRequest
 */
export interface CloseTradeRequest {
    /**
     * @generated from protobuf field: int64 ticket = 1;
     */
    ticket: bigint;
    /**
     * @generated from protobuf field: optional float volume = 2;
     */
    volume?: number;
    /**
     * @generated from protobuf field: optional float price = 3;
     */
    price?: number;
    /**
     * @generated from protobuf field: optional float slippage = 4;
     */
    slippage?: number;
}
/**
 * @generated from protobuf message bridge.GetTradeHistoryRequest
 */
export interface GetTradeHistoryRequest {
    /**
     * @generated from protobuf field: int64 start_date = 1;
     */
    startDate: bigint;
    /**
     * @generated from protobuf field: int64 end_date = 2;
     */
    endDate: bigint;
}
/**
 * @generated from protobuf message bridge.GetTicksFromRequest
 */
export interface GetTicksFromRequest {
    /**
     * @generated from protobuf field: string symbol = 1;
     */
    symbol: string;
    /**
     * @generated from protobuf field: int64 start_date = 2;
     */
    startDate: bigint;
    /**
     * @generated from protobuf field: int64 length = 3;
     */
    length: bigint;
}
/**
 * @generated from protobuf message bridge.GetTicksRangeRequest
 */
export interface GetTicksRangeRequest {
    /**
     * @generated from protobuf field: string symbol = 1;
     */
    symbol: string;
    /**
     * @generated from protobuf field: int64 start_date = 2;
     */
    startDate: bigint;
    /**
     * @generated from protobuf field: int64 end_date = 3;
     */
    endDate: bigint;
    /**
     * @generated from protobuf field: int64 length = 4;
     */
    length: bigint;
}
/**
 * @generated from protobuf message bridge.ModifyTradeRequest
 */
export interface ModifyTradeRequest {
    /**
     * @generated from protobuf field: int64 ticket = 1;
     */
    ticket: bigint;
    /**
     * @generated from protobuf field: float stop_loss = 2;
     */
    stopLoss: number;
    /**
     * @generated from protobuf field: float take_profit = 3;
     */
    takeProfit: number;
    /**
     * @generated from protobuf field: optional float price = 4;
     */
    price?: number;
    /**
     * @generated from protobuf field: optional int64 expiration = 5;
     */
    expiration?: bigint;
}
/**
 * @generated from protobuf message bridge.PlaceTradeRequest
 */
export interface PlaceTradeRequest {
    /**
     * @generated from protobuf field: string symbol = 1;
     */
    symbol: string;
    /**
     * @generated from protobuf field: int64 action_type = 2;
     */
    actionType: bigint;
    /**
     * @generated from protobuf field: float volume = 3;
     */
    volume: number;
    /**
     * @generated from protobuf field: optional float stop_loss = 4;
     */
    stopLoss?: number;
    /**
     * @generated from protobuf field: optional float take_profit = 5;
     */
    takeProfit?: number;
    /**
     * @generated from protobuf field: optional string comment = 6;
     */
    comment?: string;
    /**
     * @generated from protobuf field: optional float price = 7;
     */
    price?: number;
    /**
     * @generated from protobuf field: optional int64 order = 8;
     */
    order?: bigint;
    /**
     * @generated from protobuf field: optional int64 magic = 9;
     */
    magic?: bigint;
    /**
     * @generated from protobuf field: optional float stop_limit = 10;
     */
    stopLimit?: number;
    /**
     * @generated from protobuf field: optional int64 expiration = 11;
     */
    expiration?: bigint;
    /**
     * @generated from protobuf field: optional int64 position = 12;
     */
    position?: bigint;
    /**
     * @generated from protobuf field: optional int64 position_by = 13;
     */
    positionBy?: bigint;
    /**
     * @generated from protobuf field: optional int64 deviation = 14;
     */
    deviation?: bigint;
}
/**
 * @generated from protobuf message bridge.ManageSymbolRequest
 */
export interface ManageSymbolRequest {
    /**
     * @generated from protobuf field: string symbol = 1;
     */
    symbol: string;
    /**
     * @generated from protobuf field: string action = 2;
     */
    action: string;
}
// response messages

/**
 * @generated from protobuf message bridge.GenericResponseType
 */
export interface GenericResponseType {
    /**
     * @generated from protobuf field: int64 status = 1;
     */
    status: bigint;
    /**
     * @generated from protobuf field: string message = 2;
     */
    message: string;
}
/**
 * @generated from protobuf message bridge.GetAccountResponse
 */
export interface GetAccountResponse {
    /**
     * @generated from protobuf field: float balance = 1;
     */
    balance: number;
    /**
     * @generated from protobuf field: float equity = 2;
     */
    equity: number;
    /**
     * @generated from protobuf field: float margin = 3;
     */
    margin: number;
    /**
     * @generated from protobuf field: float free_margin = 4;
     */
    freeMargin: number;
    /**
     * @generated from protobuf field: float margin_level = 5;
     */
    marginLevel: number;
    /**
     * @generated from protobuf field: float profit = 6;
     */
    profit: number;
    /**
     * @generated from protobuf field: string server = 7;
     */
    server: string;
    /**
     * @generated from protobuf field: int64 trade_mode = 8;
     */
    tradeMode: bigint;
}
/**
 * @generated from protobuf message bridge.GetPositionsResponse
 */
export interface GetPositionsResponse {
    /**
     * @generated from protobuf field: repeated bridge.AccountPositionType positions = 1;
     */
    positions: AccountPositionType[];
    /**
     * @generated from protobuf field: int64 total_positions = 2;
     */
    totalPositions: bigint;
}
/**
 * @generated from protobuf message bridge.GetOrdersResponse
 */
export interface GetOrdersResponse {
    /**
     * @generated from protobuf field: repeated bridge.AccountOrdersType orders = 1;
     */
    orders: AccountOrdersType[];
    /**
     * @generated from protobuf field: int64 total_orders = 2;
     */
    totalOrders: bigint;
}
/**
 * @generated from protobuf message bridge.GetAvailableSymbolsResponse
 */
export interface GetAvailableSymbolsResponse {
    /**
     * @generated from protobuf field: repeated bridge.SymbolsType symbols = 1;
     */
    symbols: SymbolsType[];
    /**
     * @generated from protobuf field: int64 total_symbols = 2;
     */
    totalSymbols: bigint;
}
/**
 * @generated from protobuf message bridge.GetTradeHistoryResponse
 */
export interface GetTradeHistoryResponse {
    /**
     * @generated from protobuf field: repeated bridge.TradeDealType deals = 1;
     */
    deals: TradeDealType[];
    /**
     * @generated from protobuf field: int64 total_deals = 2;
     */
    totalDeals: bigint;
}
/**
 * @generated from protobuf message bridge.GetTicksResponse
 */
export interface GetTicksResponse {
    /**
     * @generated from protobuf field: repeated bridge.TickType ticks = 1;
     */
    ticks: TickType[];
    /**
     * @generated from protobuf field: int64 total_ticks = 2;
     */
    totalTicks: bigint;
}
/**
 * @generated from protobuf message bridge.PlaceTradeResponse
 */
export interface PlaceTradeResponse {
    /**
     * @generated from protobuf field: int64 status = 1;
     */
    status: bigint;
    /**
     * @generated from protobuf field: string message = 2;
     */
    message: string;
    /**
     * @generated from protobuf field: optional string ticket = 3;
     */
    ticket?: string;
}
/**
 * @generated from protobuf message bridge.ManageSymbolResponse
 */
export interface ManageSymbolResponse {
    /**
     * @generated from protobuf field: int64 status = 1;
     */
    status: bigint;
    /**
     * @generated from protobuf field: string message = 2;
     */
    message: string;
    /**
     * @generated from protobuf field: optional bridge.SymbolsType symbol = 3;
     */
    symbol?: SymbolsType;
}
// @generated message type with reflection information, may provide speed optimized methods
class EmptyType$Type extends MessageType<EmptyType> {
    constructor() {
        super("bridge.EmptyType", []);
    }
}
/**
 * @generated MessageType for protobuf message bridge.EmptyType
 */
export const EmptyType = new EmptyType$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TickType$Type extends MessageType<TickType> {
    constructor() {
        super("bridge.TickType", [
            { no: 1, name: "ask", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 2, name: "bid", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 3, name: "time", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.TickType
 */
export const TickType = new TickType$Type();
// @generated message type with reflection information, may provide speed optimized methods
class AccountPositionType$Type extends MessageType<AccountPositionType> {
    constructor() {
        super("bridge.AccountPositionType", [
            { no: 1, name: "ticket", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "symbol", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "type", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "volume", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 5, name: "open_price", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 6, name: "current_price", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 7, name: "stop_loss", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 8, name: "take_profit", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 9, name: "profit", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 10, name: "comment", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 11, name: "magic", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.AccountPositionType
 */
export const AccountPositionType = new AccountPositionType$Type();
// @generated message type with reflection information, may provide speed optimized methods
class AccountOrdersType$Type extends MessageType<AccountOrdersType> {
    constructor() {
        super("bridge.AccountOrdersType", [
            { no: 1, name: "ticket", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "symbol", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "type", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "volume", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 5, name: "price_open", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 6, name: "price_current", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 7, name: "stop_loss", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 8, name: "take_profit", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 9, name: "comment", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 10, name: "time_setup", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 11, name: "time_expiration", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 12, name: "magic", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.AccountOrdersType
 */
export const AccountOrdersType = new AccountOrdersType$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SymbolsType$Type extends MessageType<SymbolsType> {
    constructor() {
        super("bridge.SymbolsType", [
            { no: 1, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "description", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "path", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "digits", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "spread", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 6, name: "time", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.SymbolsType
 */
export const SymbolsType = new SymbolsType$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TradeDealType$Type extends MessageType<TradeDealType> {
    constructor() {
        super("bridge.TradeDealType", [
            { no: 1, name: "ticket", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "order", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "time", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "type", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "entry", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 6, name: "symbol", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 7, name: "volume", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 8, name: "price", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 9, name: "commission", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 10, name: "swap", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 11, name: "profit", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 12, name: "magic", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 13, name: "comment", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.TradeDealType
 */
export const TradeDealType = new TradeDealType$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetSymbolTickRequest$Type extends MessageType<GetSymbolTickRequest> {
    constructor() {
        super("bridge.GetSymbolTickRequest", [
            { no: 1, name: "symbol", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.GetSymbolTickRequest
 */
export const GetSymbolTickRequest = new GetSymbolTickRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TickStartRequest$Type extends MessageType<TickStartRequest> {
    constructor() {
        super("bridge.TickStartRequest", [
            { no: 1, name: "symbol", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "rate", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.TickStartRequest
 */
export const TickStartRequest = new TickStartRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class CloseTradeRequest$Type extends MessageType<CloseTradeRequest> {
    constructor() {
        super("bridge.CloseTradeRequest", [
            { no: 1, name: "ticket", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "volume", kind: "scalar", opt: true, T: 2 /*ScalarType.FLOAT*/ },
            { no: 3, name: "price", kind: "scalar", opt: true, T: 2 /*ScalarType.FLOAT*/ },
            { no: 4, name: "slippage", kind: "scalar", opt: true, T: 2 /*ScalarType.FLOAT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.CloseTradeRequest
 */
export const CloseTradeRequest = new CloseTradeRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetTradeHistoryRequest$Type extends MessageType<GetTradeHistoryRequest> {
    constructor() {
        super("bridge.GetTradeHistoryRequest", [
            { no: 1, name: "start_date", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "end_date", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.GetTradeHistoryRequest
 */
export const GetTradeHistoryRequest = new GetTradeHistoryRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetTicksFromRequest$Type extends MessageType<GetTicksFromRequest> {
    constructor() {
        super("bridge.GetTicksFromRequest", [
            { no: 1, name: "symbol", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "start_date", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "length", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.GetTicksFromRequest
 */
export const GetTicksFromRequest = new GetTicksFromRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetTicksRangeRequest$Type extends MessageType<GetTicksRangeRequest> {
    constructor() {
        super("bridge.GetTicksRangeRequest", [
            { no: 1, name: "symbol", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "start_date", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "end_date", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "length", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.GetTicksRangeRequest
 */
export const GetTicksRangeRequest = new GetTicksRangeRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ModifyTradeRequest$Type extends MessageType<ModifyTradeRequest> {
    constructor() {
        super("bridge.ModifyTradeRequest", [
            { no: 1, name: "ticket", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "stop_loss", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 3, name: "take_profit", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 4, name: "price", kind: "scalar", opt: true, T: 2 /*ScalarType.FLOAT*/ },
            { no: 5, name: "expiration", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.ModifyTradeRequest
 */
export const ModifyTradeRequest = new ModifyTradeRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class PlaceTradeRequest$Type extends MessageType<PlaceTradeRequest> {
    constructor() {
        super("bridge.PlaceTradeRequest", [
            { no: 1, name: "symbol", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "action_type", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "volume", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 4, name: "stop_loss", kind: "scalar", opt: true, T: 2 /*ScalarType.FLOAT*/ },
            { no: 5, name: "take_profit", kind: "scalar", opt: true, T: 2 /*ScalarType.FLOAT*/ },
            { no: 6, name: "comment", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 7, name: "price", kind: "scalar", opt: true, T: 2 /*ScalarType.FLOAT*/ },
            { no: 8, name: "order", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 9, name: "magic", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 10, name: "stop_limit", kind: "scalar", opt: true, T: 2 /*ScalarType.FLOAT*/ },
            { no: 11, name: "expiration", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 12, name: "position", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 13, name: "position_by", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 14, name: "deviation", kind: "scalar", opt: true, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.PlaceTradeRequest
 */
export const PlaceTradeRequest = new PlaceTradeRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ManageSymbolRequest$Type extends MessageType<ManageSymbolRequest> {
    constructor() {
        super("bridge.ManageSymbolRequest", [
            { no: 1, name: "symbol", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "action", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.ManageSymbolRequest
 */
export const ManageSymbolRequest = new ManageSymbolRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GenericResponseType$Type extends MessageType<GenericResponseType> {
    constructor() {
        super("bridge.GenericResponseType", [
            { no: 1, name: "status", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "message", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.GenericResponseType
 */
export const GenericResponseType = new GenericResponseType$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetAccountResponse$Type extends MessageType<GetAccountResponse> {
    constructor() {
        super("bridge.GetAccountResponse", [
            { no: 1, name: "balance", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 2, name: "equity", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 3, name: "margin", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 4, name: "free_margin", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 5, name: "margin_level", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 6, name: "profit", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 7, name: "server", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 8, name: "trade_mode", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.GetAccountResponse
 */
export const GetAccountResponse = new GetAccountResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetPositionsResponse$Type extends MessageType<GetPositionsResponse> {
    constructor() {
        super("bridge.GetPositionsResponse", [
            { no: 1, name: "positions", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => AccountPositionType },
            { no: 2, name: "total_positions", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.GetPositionsResponse
 */
export const GetPositionsResponse = new GetPositionsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetOrdersResponse$Type extends MessageType<GetOrdersResponse> {
    constructor() {
        super("bridge.GetOrdersResponse", [
            { no: 1, name: "orders", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => AccountOrdersType },
            { no: 2, name: "total_orders", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.GetOrdersResponse
 */
export const GetOrdersResponse = new GetOrdersResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetAvailableSymbolsResponse$Type extends MessageType<GetAvailableSymbolsResponse> {
    constructor() {
        super("bridge.GetAvailableSymbolsResponse", [
            { no: 1, name: "symbols", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => SymbolsType },
            { no: 2, name: "total_symbols", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.GetAvailableSymbolsResponse
 */
export const GetAvailableSymbolsResponse = new GetAvailableSymbolsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetTradeHistoryResponse$Type extends MessageType<GetTradeHistoryResponse> {
    constructor() {
        super("bridge.GetTradeHistoryResponse", [
            { no: 1, name: "deals", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => TradeDealType },
            { no: 2, name: "total_deals", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.GetTradeHistoryResponse
 */
export const GetTradeHistoryResponse = new GetTradeHistoryResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetTicksResponse$Type extends MessageType<GetTicksResponse> {
    constructor() {
        super("bridge.GetTicksResponse", [
            { no: 1, name: "ticks", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => TickType },
            { no: 2, name: "total_ticks", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.GetTicksResponse
 */
export const GetTicksResponse = new GetTicksResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class PlaceTradeResponse$Type extends MessageType<PlaceTradeResponse> {
    constructor() {
        super("bridge.PlaceTradeResponse", [
            { no: 1, name: "status", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "message", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "ticket", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.PlaceTradeResponse
 */
export const PlaceTradeResponse = new PlaceTradeResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ManageSymbolResponse$Type extends MessageType<ManageSymbolResponse> {
    constructor() {
        super("bridge.ManageSymbolResponse", [
            { no: 1, name: "status", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "message", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "symbol", kind: "message", T: () => SymbolsType }
        ]);
    }
}
/**
 * @generated MessageType for protobuf message bridge.ManageSymbolResponse
 */
export const ManageSymbolResponse = new ManageSymbolResponse$Type();
/**
 * @generated ServiceType for protobuf service bridge.BridgeRpcService
 */
export const BridgeRpcService = new ServiceType("bridge.BridgeRpcService", [
    { name: "TickStart", serverStreaming: true, options: {}, I: TickStartRequest, O: TickType },
    { name: "TickStop", options: {}, I: EmptyType, O: GenericResponseType },
    { name: "GetAccount", options: {}, I: EmptyType, O: GetAccountResponse },
    { name: "GetPositions", options: {}, I: EmptyType, O: GetPositionsResponse },
    { name: "GetOrders", options: {}, I: EmptyType, O: GetOrdersResponse },
    { name: "GetAvailableSymbols", options: {}, I: EmptyType, O: GetAvailableSymbolsResponse },
    { name: "GetTradeHistory", options: {}, I: GetTradeHistoryRequest, O: GetTradeHistoryResponse },
    { name: "GetTicksFrom", options: {}, I: GetTicksFromRequest, O: GetTicksResponse },
    { name: "GetTicksRange", options: {}, I: GetTicksRangeRequest, O: GetTicksResponse },
    { name: "CloseTrade", options: {}, I: CloseTradeRequest, O: GenericResponseType },
    { name: "ModifyTrade", options: {}, I: ModifyTradeRequest, O: GenericResponseType },
    { name: "PlaceTrade", options: {}, I: PlaceTradeRequest, O: PlaceTradeResponse },
    { name: "ManageSymbol", options: {}, I: ManageSymbolRequest, O: ManageSymbolResponse },
    { name: "GetTerminalError", options: {}, I: EmptyType, O: GenericResponseType },
    { name: "GetSymbolTick", options: {}, I: GetSymbolTickRequest, O: TickType }
]);
