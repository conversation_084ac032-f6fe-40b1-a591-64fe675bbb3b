import { isValidObjectId } from 'mongoose';
import { IRequest } from './types';

export function to<T>(p: any): T {
  return p;
}

export function toObj<T>(obj: T | string, shouldValidate = true): T | null {
  if (typeof obj === 'string') {
    if (!shouldValidate) return null;
    throw new Error('Invalid object type');
  }
  return obj;
}
export function toArrObj<T>(
  arr: T[] | string[],
  shouldValidate = false,
): T[] | null {
  if (typeof arr[0] === 'string') {
    if (!shouldValidate) return null;
    throw new Error('Invalid object type');
  }

  return arr as any;
}

export function removeFields<T extends Object>(
  obj: T,
  fields: (keyof T)[],
): Partial<T> {
  return Object.fromEntries(
    Object.entries(obj).filter(([key]) => !fields.includes(key as any)),
  ) as Partial<T>;
}

export function extractFields<T extends Object>(
  obj: T,
  fields: (keyof T)[],
): T {
  return Object.fromEntries(
    Object.entries(obj).filter(([key]) => fields.includes(key as any)),
  ) as T;
}

export const getDocId = (doc: any, bypassValidation = false): string => {
  if (doc) {
    if (typeof doc === 'string' && (isValidObjectId(doc) || bypassValidation))
      return doc.toString();
    else if (typeof doc === 'object' || doc.id || doc._id)
      return (doc._id ?? doc.id).toString();
  }

  return null;
};

export function generateRandomId() {
  const timestamp = Date.now(); // Get the current timestamp
  const randomString = Math.random().toString(36).substring(2, 10); // Generate a random string
  return `${timestamp}-${randomString}`;
}

export function isDefined(value: any) {
  return value !== undefined && value !== null;
}

import { ChildProcessWithoutNullStreams, spawn } from 'child_process';

export function delay(milliseconds: number): Promise<void> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, milliseconds);
  });
}

const logger = {
  error: console.log,
  info: console.info,
  warn: console.warn,
};

export function exec(
  cmd: string,
  loc: string,
  args: string[] = [],
  abortSignal: AbortSignal = undefined,
  isLongRunning = false,
) {
  const isProduction = process.env.NODE_ENV === 'production';
  const proc = spawn(cmd, args, {
    cwd: loc,
    shell: true,
    stdio: 'pipe',
    signal: abortSignal,
    env: isProduction
      ? {
          ...process.env,
          WINEPREFIX: '/config/.wine',
        }
      : undefined,
  });

  return new Promise((resolve, reject) => {
    let timeout: NodeJS.Timeout;

    // Set a timeout to prevent hanging
    if (isLongRunning == false)
      timeout = setTimeout(() => {
        proc.kill();
        reject(new Error('Command execution timed out'));
      }, 60000); // 60 seconds timeout

    // Collect stdout data
    const stdoutChunks: Buffer[] = [];
    proc.stdout.on('data', (chunk) => {
      stdoutChunks.push(chunk);
      logger.info(chunk.toString().trim());
    });

    // Collect stderr data
    const stderrChunks: Buffer[] = [];
    proc.stderr.on('data', (chunk) => {
      stderrChunks.push(chunk);
      logger.warn(chunk.toString().trim());
    });

    // Handle successful completion
    proc.on('close', (code) => {
      // Clear the timeout
      clearTimeout(timeout);

      if (code === 0) {
        resolve(proc);
      } else {
        const errorOutput = Buffer.concat(stderrChunks).toString().trim();
        const stdoutOutput = Buffer.concat(stdoutChunks).toString().trim();

        // Create a more informative error
        const error = new Error(`Command failed with code ${code}`);

        // Add additional properties to the error
        Object.assign(error, {
          code,
          stdout: stdoutOutput,
          stderr: errorOutput,
          command: cmd,
        });

        // Log the detailed error
        logger.error('Command execution failed', {
          command: cmd,
          code,
          stdout: stdoutOutput,
          stderr: errorOutput,
        });

        reject(error);
      }
    });

    proc.on('spawn', () => {
      console.log('Process spawned', proc.pid, process.pid);
    });

    // Handle spawn errors
    proc.on('error', (err) => {
      // Clear the timeout
      clearTimeout(timeout);

      logger.error(`Spawn error: ${err.message}`, {
        command: cmd,
        error: err,
      });

      reject(err);
    });
  });
}

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Optionally send an alert or perform cleanup
});

// Global error handler for uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  // Graceful shutdown
  process.exit(1);
});


// Converts an integer (number or BigInt) to a hex string.
export function intToHex(value: number | bigint, hexLength: number = 32): string {
  // Convert to BigInt to support very large values
  const bigValue = BigInt(value);
  // Convert to hex string (without the prefix)
  let hexStr = bigValue.toString(16);
  // Pad with leading zeros if necessary
  if (hexStr.length < hexLength) {
    hexStr = hexStr.padStart(hexLength, "0");
  }
  return "0x" + hexStr;
}

// Converts a hex string to an integer.
// If the resulting number is within the safe integer range, returns a number.
// Otherwise, returns an object containing:
//   - safe: the remainder when divided by Number.MAX_SAFE_INTEGER,
//   - big: the full BigInt value.
export function hexToInt(hex: string): number  {
  // Ensure the hex string does not have the '0x' prefix.
  const normalizedHex = hex.startsWith("0x") ? hex.slice(2) : hex;
  const bigValue = BigInt("0x" + normalizedHex);
  const maxSafe = BigInt(Number.MAX_SAFE_INTEGER);

  if (bigValue <= maxSafe) {
    return Number(bigValue);
  } else {
    return Number(bigValue % maxSafe);
  }
}