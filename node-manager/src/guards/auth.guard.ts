import { CanActivate, ExecutionContext } from '@nestjs/common';
import { IncomingMessage } from 'http';
import { WebSocket } from 'ws';

export class AdminHttpGuard implements CanActivate {
  constructor() {}

  canActivate(context: ExecutionContext): boolean {
    const data: IncomingMessage = context.switchToHttp().getRequest();
    const token = data?.headers?.authorization?.replace('Bearer ', '')?.trim();


    if (token !== process.env.ADMIN_AUTH_TOKEN) {
      return false;
    }
    return true;
  }
}


export class AdminWsGuard implements CanActivate {
  constructor() {}

  canActivate(context: ExecutionContext): boolean {
    const client = context.switchToWs().getClient();
    const c = client.request as IncomingMessage
    if (c.headers.authorization !== process.env.ADMIN_AUTH_TOKEN) {
      return false;
    }
    return true;
  }
}



