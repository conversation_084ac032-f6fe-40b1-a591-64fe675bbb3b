import {
  CanActivate,
  ExecutionContext,
  Injectable,
  ForbiddenException,
} from '@nestjs/common';
import { Request } from 'express';
import { IncomingMessage } from 'http';

@Injectable()
export class LocalIpGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    
    const request: Request = context.switchToHttp().getRequest();
    const clientIp = request.ip || request.connection.remoteAddress;

    // List of allowed local IPs
    const allowedLocalIps = ['127.0.0.1', '::1', '0.0.0.0'];

    // If the IP is not in the allowed list, block access
    if (!allowedLocalIps.includes(clientIp)) {
      throw new ForbiddenException('Access restricted to local network only.');
    }

    return true;
  }
}
@Injectable()
export class LocalWsIpGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    
    const client = context.switchToWs().getClient();
    const c = client.request as IncomingMessage;
    const clientIp =  c.socket.remoteAddress;

    // List of allowed local IPs
    const allowedLocalIps = ['127.0.0.1', '::1', '0.0.0.0'];

    // If the IP is not in the allowed list, block access
    if (!allowedLocalIps.includes(clientIp)) {
      throw new ForbiddenException('Access restricted to local network only.');
    }

    return true;
  }
}
