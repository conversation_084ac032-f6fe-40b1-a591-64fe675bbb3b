import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import * as nodemailer from 'nodemailer';
import { MAIL_TYPE } from 'src/utils/enums';
interface Config {
  smtp_user: string;
  smtp_password: string;
}

@Injectable()
export class MailRepository {
  private config: Config;
  private axios: AxiosInstance;

  constructor(private readonly logger: Logger) {
    const config: Config = {
      smtp_user: process.env.SMTP_USERNAME,
      smtp_password: process.env.SMTP_PASSWORD,
    };
    this.config = config;
    this.axios = axios.create({
      baseURL: process.env.BASE_CLIENT_URL,
      headers: {
        'Content-Type': 'text/html',
      },
    });
  }

  async sendEmail(payload: {
    params: Record<string, string>;
    destination: string;
    subject: string;
    type: MAIL_TYPE;
  }) {
    try {
      const transporter = nodemailer.createTransport({
        host: 'smtp.office365.com',
        port: 587,
        secure: false,
        auth: {
          user: this.config.smtp_user,
          pass: this.config.smtp_password,
        },
        tls: {
          ciphers: 'SSLv3',
        },
      });

      const { data } = await this.axios.get(`api/emails/${payload.type}`, {
        params: payload.params,
      });

      const mailData = {
        from: 'Myblankspace <<EMAIL>>',
        to: payload.destination,
        subject: payload.subject,
        html: data,
      };

      const sendmail = () => {
        return new Promise<string>((resolve, reject) => {
          transporter.sendMail(mailData, function (err, info) {
            if (err) reject(err);
            else resolve(info.response);
          });
        });
      };

      await sendmail();
    } catch (error) {
      this.logger.error(error);
      return {
        error: error?.message,
      };
    }
  }
}
