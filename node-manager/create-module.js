const args = process.argv.slice(2);

const moduleName = args[0];
if (!moduleName) throw new Error('Module name is required');

const commands = [
  `module ./modules/${moduleName}`,
  `service ./modules/${moduleName} --no-spec`,
  `controller ./modules/${moduleName} --no-spec`,
  `class ./modules/${moduleName}/schemas/${moduleName}.schema --no-spec`,
];

const command =
  commands.map((c) => `npx @nestjs/cli generate ${c}`).join(' && ') +
  `&& mkdir ./src/modules/${moduleName}/utils/ &&` +
  `touch ./src/modules/${moduleName}/utils/dto.utils.ts &&` +
  `touch ./src/modules/${moduleName}/utils/enums.utils.ts`;

const exec = require('child_process').exec;
exec(command, (error, stdout, stderr) => {
  console.log('stdout:', stdout);
  console.log('stderr:', stderr);
});
