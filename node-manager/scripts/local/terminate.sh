#LOCAL
WINE_PREFIX="$HOME/.wine"

DRIVE_C="$WINE_PREFIX/drive_c"

MT5_PROG_DIR="$DRIVE_C/Program Files/MetaTrader 5" 
MT4_PROG_DIR="$DRIVE_C/Program Files (x86)/Metatrader 4" 

if [ -z "$1" ]; then
    echo "Usage: $0 ID"
    exit 1
fi

ID="$1"
TYPE="$2"

PROCESS_SEARCH=""
TERMINAL_DIR=""

if [ "$TYPE" == "mt4" ]; then
    PROCESS_SEARCH="$ID.*terminal.exe"
    TERMINAL_DIR="$MT4_PROG_DIR/terminals/$ID"
fi

if [ "$TYPE" == "mt5" ]; then
    PROCESS_SEARCH="$ID.*terminal64.exe"
    TERMINAL_DIR="$MT5_PROG_DIR/terminals/$ID"
fi

# Find the process using ps and grep
PROCESS_INFO=$(ps -ef | grep wine | grep -E "$PROCESS_SEARCH" | grep -v grep)

if [ -z "$PROCESS_INFO" ]; then
    echo "No terminal process found with the id: $ID"
    exit 0
fi

# Extract the PID(s) from the process info
PIDS=$(echo "$PROCESS_INFO" | awk '{print $2}')

# Kill the process(es)
echo "Killing the following processes:"
echo "$PROCESS_INFO"

for PID in $PIDS; do
    kill -9 $PID
    if [ $? -eq 0 ]; then
        echo "Successfully killed process with PID $PID"
        echo "Removing terminal directory..."
        rm -r "$TERMINAL_DIR"
        echo "Successfully removed directory with ID $ID"
    else
        echo "Failed to kill process with PID $PID"
    fi
done
