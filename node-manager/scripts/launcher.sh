#!/bin/bash

WINE_PREFIX='/config/.wine'
DRIVE_C="$WINE_PREFIX/drive_c"

MT5_PROG_DIR="$DRIVE_C/Program Files/MetaTrader 5" 
MT4_PROG_DIR="$DRIVE_C/Program Files (x86)/Metatrader 4" 


args=("$@")

echo "args: ${args[@]}"

ID="${args[0]}"
LOGIN="${args[1]}"
PASSWORD="${args[2]}"
SERVER="${args[3]}"
LOGIN_CALLBACK="${args[4]}"
TERMINATE_CALLBACK="${args[5]}"
WS_PORT="${args[6]}"

RPC_PORT="${args[7]}"
AUTH_TOKEN="${args[8]}"

TERMINAL_TYPE="${args[9]}"
TERMINAL_PORT="${args[10]}"


log() {
    tput setaf 1  
    tput bold     
    date=$(date +"%H:%M:%S")
    echo "${GREEN}LOGGER: $1 $date${RESET}"
    tput sgr0 
}

#-----------------------------------------------------------------------------
#-----------------------------------------------------------------------------

log "Handling $TERMINAL_TYPE launch with args: ID:$ID LOGIN:$LOGIN PASSWORD:$PASSWORD SERVER:$SERVER CALLBACK:$LOGIN_CALLBACK" RPC_PORT:$RPC_PORT AUTH_TOKEN:$AUTH_TOKEN TERMINAL_TYPE:$TERMINAL_TYPE TERMINAL_PORT:$TERMINAL_PORT 

if [ "$TERMINAL_TYPE" == "mt5" ]; then
    log "Duplicating MT5 terminal..."

    new_dir="$MT5_PROG_DIR/terminals/$WS_PORT"

    log "Creating new MT5 terminal directory: $new_dir..."

    if [ -d "$new_dir" ]; then
        log "Directory already exists: $new_dir. Removing it..."
        sudo rm -rf "$new_dir"
    fi

    mkdir -p "$new_dir"
    rsync -av --exclude='terminals/'  "$MT5_PROG_DIR/" "$new_dir"
    log "MT5 terminal duplicated."
fi

if [ "$TERMINAL_TYPE" == "mt4" ]; then
   
    log "Duplicating MT4 terminal..."

    new_dir="$MT4_PROG_DIR/terminals/$WS_PORT"

    log "Creating new MT4 terminal directory: $new_dir..."

    if [ -d "$new_dir" ]; then
        log "Directory already exists: $new_dir. Removing it..."
        sudo rm -rf "$new_dir"
    fi

    sudo mkdir -p "$new_dir"
    sudo rsync -av --exclude='terminals/'  "$MT4_PROG_DIR/" "$new_dir"
    log "MT4 terminal duplicated."

    sudo echo "
    ExpertsEnable=true
    ExpertsDllImport=true
    ExpertsExpImport=true
    ExpertsTrades=true

    Symbol=EURUSD
    Period=M1
    Expert=MtApi
    ExpertParameters=mtapi.set

    Login=$LOGIN
    Password=$PASSWORD
    Server=$SERVER
    " > "$new_dir/config/start.ini"

    sudo echo "Port=$TERMINAL_PORT  BacktestingLockTicks=0" > "$new_dir/MQL4/Presets/mtapi.set"

    log "Starting MT4 terminal..."
    sudo wine "$new_dir/terminal.exe" "config\\start.ini" & 
fi

export ENVIRONMENT=production
export WINEDEBUG=-all

log "Starting bridge service..."
sudo wine64 python '/config/.wine/drive_c/bridge/main.py' \
  --rpc_port $RPC_PORT \
  --auth_token $AUTH_TOKEN \
  --login $LOGIN \
  --password $PASSWORD \
  --server $SERVER \
  --login_callback $LOGIN_CALLBACK \
  --terminate_callback $TERMINATE_CALLBACK \
  --server_port $WS_PORT \
  --terminal_id $ID \
  --terminal_type $TERMINAL_TYPE \
  --terminal_port $TERMINAL_PORT

log "Finished running the service"
