#!/bin/sh
WINE_PREFIX='/config/.wine'

DRIVE_C="$WINE_PREFIX/drive_c"
MT5_PROG_DIR="$DRIVE_C/Program Files/MetaTrader 5"
MT4_PROG_DIR="$DRIVE_C/Program Files (x86)/Metatrader 4"

if [ $# -lt 2 ]; then
  echo "Usage: $0 ID {mt4|mt5}"
  exit 1
fi

ID="$1"
TYPE="$2"

case "$TYPE" in
  mt4)
    PROCESS_SEARCH="${ID}.*terminal.exe"
    TERMINAL_DIR="$MT4_PROG_DIR/terminals/$ID"
    ;;
  mt5)
    PROCESS_SEARCH="${ID}.*terminal64.exe"
    TERMINAL_DIR="$MT5_PROG_DIR/terminals/$ID"
    ;;
  *)
    echo "Unknown TYPE: $TYPE (expected mt4 or mt5)"
    exit 1
    ;;
esac

# Find the processes
PROCESS_INFO=$(ps -ef | grep wine | grep -E "$PROCESS_SEARCH" | grep -v grep)

if [ -z "$PROCESS_INFO" ]; then
  echo "No terminal process found with the id: $ID"
  exit 0
fi

# Extract PIDs and kill them
echo "Killing the following processes:"
echo "$PROCESS_INFO"
echo "$PROCESS_INFO" | awk '{print $2}' | xargs -r sudo kill -9
