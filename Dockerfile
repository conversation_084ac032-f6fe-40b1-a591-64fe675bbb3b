FROM ghcr.io/linuxserver/baseimage-kasmvnc:debianbookworm

# set version label
ARG BUILD_DATE
ARG VERSION
LABEL build_version="Metatrader Docker:- ${VERSION} Build-date:- ${BUILD_DATE}"
LABEL maintainer="gmartin"

ENV TITLE=Metatrader5
ENV WINEPREFIX="/config/.wine"

# Update package lists and upgrade packages
RUN apt-get update && apt-get upgrade -y

# Install required packages
RUN apt-get install -y \
    python3-pip \
    wget \
    && pip3 install --break-system-packages --upgrade pip 

# Add WineHQ repository key and APT source
RUN wget -q https://dl.winehq.org/wine-builds/winehq.key \
    && apt-key add winehq.key \
    && add-apt-repository 'deb https://dl.winehq.org/wine-builds/debian/ bullseye main' \
    && rm winehq.key

# Add i386 architecture and update package lists
RUN dpkg --add-architecture i386 \
    && apt-get update

# Install WineHQ stable package and dependencies
RUN apt-get install --install-recommends -y \
    winehq-stable \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Add Node.js repository
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash -

# Install Resync
RUN apt-get update && apt-get install -y rsync && apt-get clean

# Install unzip
RUN apt-get update && apt-get install -y unzip && apt-get clean

# Install Node.js
RUN apt-get update && apt-get install -y \
    nodejs \
    && rm -rf /var/lib/apt/lists/*

# Verify Node.js and npm installation
RUN node -v && npm -v

# Install Yarn globally
RUN npm install -g yarn

# Verify Yarn installation (optional)
RUN yarn --version

COPY /node-manager /node-manager
COPY /root /
COPY /metatrader /metatrader

VOLUME /config

RUN chmod +x /metatrader/start.sh
