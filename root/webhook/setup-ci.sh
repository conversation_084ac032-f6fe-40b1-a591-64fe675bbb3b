#!/bin/bash

args=("$@")
echo "args: ${args[@]}"
ADMIN_AUTH="${args[0]}"

sudo apt-get install webhook
sudo echo "
[
  {
    "id": "redeploy-webhook",
    "execute-command": "redeploy.sh",
    "command-working-directory": "/root/webhook",
    "incoming-payload-content-type": "application/json",
    "trigger-rule": {
        "and": [
        
            "match":
            {
                "parameter":
                {
                    "source": "header",
                    "name": "Authorization"
                },
                "type": "value",
                "value": "$ADMIN_AUTH"
            }
        ]
    }
  }
]" > ~/webhook/hooks.json

webhook -hooks ~/webhook/hooks.json -verbose -port 2500