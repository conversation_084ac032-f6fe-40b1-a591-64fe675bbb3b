WINE_PREFIX="$HOME/.wine"
DRIVE_C="$WINE_PREFIX/drive_c"
SETUP_FILE_NAME="$DRIVE_C/mt5setup.exe"

MT5_VERSION="5.0.36"
PYTHON_SETUP_URL="https://www.python.org/ftp/python/3.10.9/python-3.10.9.exe"
MT5_SETUP_URL="https://download.mql5.com/cdn/web/metaquotes.software.corp/mt5/mt5setup.exe"


check_dependency() {
    if ! command -v $1 &> /dev/null; then
        echo "$1 is not installed. Please install it to continue."
        exit 1
    fi
}

is_wine_python_package_installed() {
    wine64 python -c "import pkg_resources; exit(not pkg_resources.require('$1'))" 2>/dev/null
    return $?
}
log() {
    tput setaf 1  
    tput bold     
    date=$(date +"%H:%M:%S")
    echo "${GREEN}LOGGER: $1 $date${RESET}"
    tput sgr0 
}

convert_to_win_path() {
    local input_path="$1"
    local path
    
    path="$(realpath "$input_path")"
    if [ -z "$path" ]; then
        echo "Error: Unable to resolve the path '$input_path'." >&2
        return 1
    fi

    local converted_path="Z:${path//\//\\\\}"
    echo "$converted_path"
}


#-----------------------------------------------------------------------------
#-----------------------------------------------------------------------------


if ! command -v wine &> /dev/null; then
        echo "Wine is not installed. Please install it to continue.Run brew install --cask xquartz && brew install --cask wine-stable "
        exit 1
    fi
log "Wine64 is installed."


if [ -e "$DRIVE_C/Program Files/MetaTrader 5/terminal64.exe" ]; then
    log "MT5 Directory exists."
else
    log "Initializing MetaTrader5 installation..."

    log "Modifying registry..."
    wine64 reg add "HKEY_CURRENT_USER\\Software\\Wine" /v Version /t REG_SZ /d "win10" /f

    log "Downloading MT5 installer..."
    curl -o $SETUP_FILE_NAME $MT5_SETUP_URL

    log "Installing MT5 installer..."
    wine64 $SETUP_FILE_NAME "/auto" & wait

    log "Cleaning up..."
    rm -f $SETUP_FILE_NAME
fi

if [ -e "$DRIVE_C/Program Files (x86)/Metatrader 4/terminal.exe" ]; then
    log "MT4 Directory exists."
else
    log "Initializing MetaTrader4 installation..."

    log "Installing MT4..."
    unzip ../metatrader/static/mt4.zip -d "$DRIVE_C/Program Files (x86)"

    if [ -e "$DRIVE_C/windows/syswow64/MTConnector.dll" ]; then
        log "Mtconnector library already exists."
    else
        cp "$DRIVE_C/Program Files (x86)/Metatrader 4/MTConnector.dll"  "$DRIVE_C/windows/syswow64"
    fi
    
    rm -rf  "$DRIVE_C/Program Files (x86)/__MACOSX"
    
    log "Installed MT4" 
fi


if ! wine64 python --version 2>/dev/null; then
    log "Installing Python in Wine..."
    curl -L $PYTHON_SETUP_URL -o /tmp/python-installer.exe

    wine64 /tmp/python-installer.exe /quiet InstallAllUsers=1 PrependPath=1
    rm /tmp/python-installer.exe
    log "Python installed in Wine."
else
    log "Python is already installed in Wine."
fi

log "Upgrading and installing Python libraries"
wine64 python -m pip install --upgrade --no-cache-dir pip


log "Installing MetaTrader5 library in Windows"
if ! is_wine_python_package_installed "MetaTrader5==$MT5_VERSION"; then
    wine64 python -m pip install --no-cache-dir MetaTrader5==$MT5_VERSION
fi



log "Installing numpy library in Windows"
if ! is_wine_python_package_installed "numpy==1.26.4"; then
    wine64 python -m pip install --no-cache-dir numpy==1.26.4
fi

log "Checking and installing websockets library in Windows if necessary"
if ! is_wine_python_package_installed "websockets"; then
    wine64 python -m pip install --no-cache-dir websockets
fi

log "Checking and installing requests library in Windows if necessary"
if ! is_wine_python_package_installed "requests"; then
    wine64 python -m pip install --no-cache-dir requests
fi

log "Checking and installing pytz in Windows if necessary"
if ! is_wine_python_package_installed "pytz"; then
    wine64 python -m pip install --no-cache-dir pytz
fi

log "Checking and installing grpcio in Windows if necessary"
if ! is_wine_python_package_installed "grpcio"; then
    wine64 python -m pip install --no-cache-dir grpcio
fi

log "Checking and installing aiohttp in Windows if necessary"
if ! is_wine_python_package_installed "aiohttp"; then
    wine64 python -m pip install --no-cache-dir aiohttp
fi

log "Checking and installing grpcio-tools in Windows if necessary"
if ! is_wine_python_package_installed "grpcio-tools"; then
    wine64 python -m pip install --no-cache-dir grpcio
fi



export NODE_ENV="local"

log "Finished installation."

log "Initializing Terminal Manager."
cd "../node-manager"

log "Running Terminal Manager."
npm run start:dev
# npm run build
# node dist/main 





