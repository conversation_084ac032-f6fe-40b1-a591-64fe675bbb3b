#!/bin/bash

# Configuration variables
mt5file='/config/.wine/drive_c/Program Files/MetaTrader 5/terminal64.exe'
mt4file='/config/.wine/drive_c/Program Files (x86)/Metatrader 4/terminal.exe'

wine_executable="wine64" #***
metatrader_version="5.0.36"
drive_c="/config/.wine/drive_c"

python_url="https://www.python.org/ftp/python/3.10.9/python-3.10.9.exe"
mono_url="https://dl.winehq.org/wine/wine-mono/9.4.0/wine-mono-9.4.0-x86.msi"

# Function to display a graphical message
show_message() {
    tput setaf 1  
    tput bold     
    date=$(date +"%H:%M:%S")
    echo "${GREEN}LOGGER: $1 $date${RESET}"
    tput sgr0 
}

# Function to check if a dependency is installed
check_dependency() {
    if ! command -v $1 &> /dev/null; then
        echo "$1 is not installed. Please install it to continue."
        exit 1
    fi
}

# Function to check if a Python package is installed
is_python_package_installed() {
    python3 -c "import pkg_resources; exit(not pkg_resources.require('$1'))" 2>/dev/null
    return $?
}

# Function to check if a Python package is installed in Wine
is_wine_python_package_installed() {
    #***
    sudo $wine_executable python -c "import pkg_resources; exit(not pkg_resources.require('$1'))" 2>/dev/null
    return $?
}

#==================================================
# Check for necessary dependencies
check_dependency "curl"
check_dependency "$wine_executable"



# Create config directory if it doesn't exist
if [ ! -d $drive_c ]; then
    show_message "Creating config directory..."
    sudo mkdir -p $drive_c
    show_message "Config directory created successfully."
else
    show_message "Config directory already exists."
fi

show_message "Checking and installing gdown library in Linux if necessary"
if ! is_python_package_installed "gdown"; then
    sudo python3 -m pip install --break-system-packages --no-cache-dir gdown
else
   show_message "gdown already exists."
fi

if [ ! -e "$drive_c/static" ]; then
    cd "$drive_c/"
    sudo gdown --folder 1szN2IMztKaNW1VGOtqsUj4e7J_p3OB-z
fi

# Install Mono if not present
if [ ! -e "/config/.wine/drive_c/windows/mono" ]; then
    show_message "Downloading and installing Mono..."
    #***
    sudo curl -o /config/.wine/drive_c/mono.msi $mono_url
    sudo WINEDLLOVERRIDES=mscoree=d $wine_executable msiexec /i /config/.wine/drive_c/mono.msi /qn

    sudo rm /config/.wine/drive_c/mono.msi
    show_message "Mono installed."
else
    show_message "Mono is already installed."
fi

# Check if MetaTrader 5 is already installed
if [ -e "$mt5file" ]; then
    show_message "File $mt5file already exists."
else
    show_message "File $mt5file is not installed. Installing..."
    sudo unzip "$drive_c/static/mt5.zip" -d "$drive_c/Program Files"

    sudo mkdir -p "$drive_c/Program Files/MetaTrader 5/Config"
    sudo unzip "$drive_c/static/servers.dat.zip" -d "$drive_c/Program Files/MetaTrader 5/Config"

    show_message "Installed MT5" 
fi

# Check if MetaTrader 4 is already installed
if [ -e "$mt4file" ]; then
    show_message "File $mt4file already exists."
else
    show_message "File $mt4file is not installed. Installing..."
    sudo unzip "$drive_c/static/mt4.zip"  -d "$drive_c/Program Files (x86)"

    show_message "Installed MT4" 
fi


# --------------------------------------------- #
# -------------PYTHON INSTALLATIONS------------ #
# --------------------------------------------- #


# Install Python in Wine if not present
if ! sudo $wine_executable python --version 2>/dev/null; then
    show_message "Installing Python in Wine..."
    sudo curl -L $python_url -o /tmp/python-installer.exe
    sudo $wine_executable /tmp/python-installer.exe /quiet InstallAllUsers=1 PrependPath=1
    sudo rm /tmp/python-installer.exe
    show_message "Python installed in Wine."
else
    show_message "Python is already installed in Wine."
fi

# Upgrade pip and install required packages
    show_message "Installing Python libraries"
    sudo $wine_executable python -m pip install --upgrade --no-cache-dir pip


# Install MetaTrader5 library in Windows if not installed
show_message "Installing MetaTrader5 library in Windows"
if ! is_wine_python_package_installed "MetaTrader5==$metatrader_version"; then
    sudo $wine_executable python -m pip install --no-cache-dir MetaTrader5==$metatrader_version
fi


show_message "Installing Numpy library in Windows"
if ! is_wine_python_package_installed "numpy==1.26.4"; then
    sudo $wine_executable python -m pip install --no-cache-dir numpy==1.26.4
fi


# Install websockets library in Windows if not installed
show_message "Checking and installing websockets library in Windows if necessary"
if ! is_wine_python_package_installed "websockets"; then
    sudo $wine_executable python -m pip install   --no-cache-dir websockets
fi

# Install requests library in Windows if not installed
show_message "Checking and installing requests library in Windows if necessary"
if ! is_wine_python_package_installed "requests"; then
    sudo $wine_executable python -m pip install   --no-cache-dir requests
fi

show_message "Checking and installing pytz library in Windows if necessary"
if ! is_wine_python_package_installed "pytz"; then
    sudo $wine_executable python -m pip install   --no-cache-dir pytz
fi

show_message "Checking and installing grpcio-tools library in Windows if necessary"
if ! is_wine_python_package_installed "grpcio-tools"; then
    sudo $wine_executable python -m pip install   --no-cache-dir grpcio-tools
fi

show_message "Checking and installing grpcio library in Windows if necessary"
if ! is_wine_python_package_installed "grpcio"; then
    sudo $wine_executable python -m pip install --no-cache-dir grpcio
fi

#show_message "Checking and installing pyxdg library in Linux if necessary"
if ! is_python_package_installed "pyxdg"; then
    sudo python3 -m pip install --break-system-packages  --no-cache-dir pyxdg
fi

show_message "Checking and installing aiohttp library in Windows if necessary"
if ! is_wine_python_package_installed "aiohttp"; then
    sudo $wine_executable python -m pip install --no-cache-dir aiohttp
fi


# --------------------------------------------- #
# -----------PYTHON INSTALLATIONS-------------- #
# --------------------------------------------- #



show_message "Starting terminal-manager..."
export NODE_ENV="production"

sudo cp -r /metatrader /config/metatrader 
sudo cp -r /config/metatrader/bridge "/config/.wine/drive_c/"
sudo cp -r /node-manager /config/

cd /config/node-manager
sudo yarn
sudo yarn build

cd /config
node -r ./node-manager/dist/main.js

