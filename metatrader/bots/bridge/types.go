package bridge

import "context"

type Options struct {
	Id     string
	RpcUri string
	WsUri  string
	Token  string
	Client BridgeRpcServiceClient
	Ctx    context.Context
}

type BotState struct {
	Is_paused bool `json:"is_paused"`
}

type Callback func(Options, *TickType)

type Map map[string]interface{}

type WebSocketData struct {
	Config map[string]interface{} `json:"config"`
	State  BotState               `json:"state"`
	Id     string                 `json:"id"`
}

type GenericWsMessage struct {
	Event string      `json:"event"`
	Data  interface{} `json:"data"`
}
