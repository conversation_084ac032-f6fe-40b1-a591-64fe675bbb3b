package bridge

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/gorilla/websocket"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
)

var (
	id       = flag.String("id", "", "Id of this bot executable")
	rpc_uri  = flag.String("rpc_uri", "", "Address of the gRPC server")
	ws_uri   = flag.String("ws_uri", "", "Address of the WebSocket server")
	token    = flag.String("auth_token", "", "Authentication token for the terminal")
	env_file = flag.String("env_file", "./env.json", "Path to the environment file")
)

var State *BotState = &BotState{
	Is_paused: false,
}

func Initialize(config *Map, tick_callback Callback) (error, Options) {

	defer func() {
		if r := recover(); r != nil {
			log.Printf("Recovered from panic in callback: %v", r)
		}
	}()

	// parse flags
	flag.Parse()

	if *rpc_uri == "" || *ws_uri == "" || *token == "" || *id == "" {
		fmt.Println("Error: Missing required flags")
		flag.Usage()
		os.Exit(1)
	}

	// connect to grpc server
	conn, err := grpc.NewClient(*rpc_uri, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("did not connect: %v", err)
	}

	defer conn.Close()
	c := NewBridgeRpcServiceClient(conn)

	ctx := context.Background()

	ctx = metadata.AppendToOutgoingContext(ctx, "authorization", *token)

	// connect to websockets server
	wc, _, err := websocket.DefaultDialer.Dial(*ws_uri, nil)
	if err != nil {
		log.Fatal("dial:", err)
	}
	defer wc.Close()

	// Handle incoming messages
	done := make(chan struct{})
	go func() {
		defer close(done)
		for {
			_, message, err := wc.ReadMessage()
			if err != nil {
				log.Println("read:", err)
				return
			}
			// Parse the JSON message into a map
			var data map[string]interface{}
			if err := json.Unmarshal(message, &data); err != nil {
				log.Println("json unmarshal error:", err)
				continue
			}

			var command = data["cmd"].(string)

			switch command {
			case "START":
				State.Is_paused = false
			case "PAUSE":
				State.Is_paused = true
			case "UPDATE":
				if err := json.Unmarshal(message, &config); err != nil {
					log.Println("json unmarshal error while updating config:", err)
					continue
				}
			case "GET_STATUS":
				jsonData, err := json.Marshal(&GenericWsMessage{Event: "bot:status", Data: WebSocketData{Config: *config, State: *State, Id: *id}})

				if err != nil {
					fmt.Println("Error marshaling to JSON:", err)
					return
				}
				wc.WriteMessage(websocket.TextMessage, jsonData)
			default:
				fmt.Println("Invalid operation!")
			}
		}
	}()

	// open env file
	file, err := os.Open(*env_file)
	if err != nil {
		fmt.Println("Error opening file:", err)
	}

	defer file.Close()

	decoder := json.NewDecoder(file)
	err = decoder.Decode(&config)

	if err != nil {
		fmt.Println("Error decoding JSON:", err)
		return err, Options{}
	}

	options := Options{
		Id:     *id,
		RpcUri: *rpc_uri,
		WsUri:  *ws_uri,
		Token:  *token,
		Client: c,
		Ctx:    ctx,
	}

	// send message to websockets server
	jsonData, err := json.Marshal(&GenericWsMessage{Event: "bot:init", Data: WebSocketData{Config: *config, State: *State, Id: *id}})

	if err != nil {
		fmt.Println("Error marshaling to JSON:", err)
		return err, options
	}
	print(string(jsonData))
	wc.WriteMessage(websocket.BinaryMessage, jsonData)

	// start ticking

	symbol := (*config)["symbol"].(string)
	rate := (*config)["rate"].(float64)
	remote_ticking := (*config)["remote_ticking"].(bool)

	if symbol == "" {
		return fmt.Errorf("symbol is required"), options
	}

	if remote_ticking == true {

		c.TickStop(ctx, &EmptyType{})

		var tickStream, _ = c.TickStart(ctx, &TickStartRequest{Symbol: symbol, Rate: float32(rate)})

		for {
			if State.Is_paused == true {
				continue
			}
			current_symbol := (*config)["symbol"].(string)

			if current_symbol != symbol {
				c.TickStop(ctx, &EmptyType{})
				tickStream, _ = c.TickStart(ctx, &TickStartRequest{Symbol: current_symbol, Rate: float32(rate)})
				symbol = current_symbol
			}
			res, err := tickStream.Recv()

			if err != nil {
				log.Printf("Error receiving stream: %v", err)
			}
			tick_callback(options, res)
		}
	} else {
		for {
			if State.Is_paused == true {
				continue
			}
			tick_callback(options, nil)
			time.Sleep(time.Duration(rate) * time.Second)
		}
	}

}
