// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.26.1
// source: bridge.proto

package bridge

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	BridgeRpcService_TickStart_FullMethodName           = "/bridge.BridgeRpcService/TickStart"
	BridgeRpcService_TickStop_FullMethodName            = "/bridge.BridgeRpcService/TickStop"
	BridgeRpcService_GetAccount_FullMethodName          = "/bridge.BridgeRpcService/GetAccount"
	BridgeRpcService_GetPositions_FullMethodName        = "/bridge.BridgeRpcService/GetPositions"
	BridgeRpcService_GetOrders_FullMethodName           = "/bridge.BridgeRpcService/GetOrders"
	BridgeRpcService_GetAvailableSymbols_FullMethodName = "/bridge.BridgeRpcService/GetAvailableSymbols"
	BridgeRpcService_GetTradeHistory_FullMethodName     = "/bridge.BridgeRpcService/GetTradeHistory"
	BridgeRpcService_GetTicksFrom_FullMethodName        = "/bridge.BridgeRpcService/GetTicksFrom"
	BridgeRpcService_GetTicksRange_FullMethodName       = "/bridge.BridgeRpcService/GetTicksRange"
	BridgeRpcService_CloseTrade_FullMethodName          = "/bridge.BridgeRpcService/CloseTrade"
	BridgeRpcService_ModifyTrade_FullMethodName         = "/bridge.BridgeRpcService/ModifyTrade"
	BridgeRpcService_PlaceTrade_FullMethodName          = "/bridge.BridgeRpcService/PlaceTrade"
	BridgeRpcService_ManageSymbol_FullMethodName        = "/bridge.BridgeRpcService/ManageSymbol"
	BridgeRpcService_GetTerminalError_FullMethodName    = "/bridge.BridgeRpcService/GetTerminalError"
	BridgeRpcService_GetSymbolTick_FullMethodName       = "/bridge.BridgeRpcService/GetSymbolTick"
)

// BridgeRpcServiceClient is the client API for BridgeRpcService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// service
type BridgeRpcServiceClient interface {
	TickStart(ctx context.Context, in *TickStartRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[TickType], error)
	TickStop(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GenericResponseType, error)
	GetAccount(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GetAccountResponse, error)
	GetPositions(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GetPositionsResponse, error)
	GetOrders(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GetOrdersResponse, error)
	GetAvailableSymbols(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GetAvailableSymbolsResponse, error)
	GetTradeHistory(ctx context.Context, in *GetTradeHistoryRequest, opts ...grpc.CallOption) (*GetTradeHistoryResponse, error)
	GetTicksFrom(ctx context.Context, in *GetTicksFromRequest, opts ...grpc.CallOption) (*GetTicksResponse, error)
	GetTicksRange(ctx context.Context, in *GetTicksRangeRequest, opts ...grpc.CallOption) (*GetTicksResponse, error)
	CloseTrade(ctx context.Context, in *CloseTradeRequest, opts ...grpc.CallOption) (*GenericResponseType, error)
	ModifyTrade(ctx context.Context, in *ModifyTradeRequest, opts ...grpc.CallOption) (*GenericResponseType, error)
	PlaceTrade(ctx context.Context, in *PlaceTradeRequest, opts ...grpc.CallOption) (*PlaceTradeResponse, error)
	ManageSymbol(ctx context.Context, in *ManageSymbolRequest, opts ...grpc.CallOption) (*ManageSymbolResponse, error)
	GetTerminalError(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GenericResponseType, error)
	GetSymbolTick(ctx context.Context, in *GetSymbolTickRequest, opts ...grpc.CallOption) (*TickType, error)
}

type bridgeRpcServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBridgeRpcServiceClient(cc grpc.ClientConnInterface) BridgeRpcServiceClient {
	return &bridgeRpcServiceClient{cc}
}

func (c *bridgeRpcServiceClient) TickStart(ctx context.Context, in *TickStartRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[TickType], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &BridgeRpcService_ServiceDesc.Streams[0], BridgeRpcService_TickStart_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[TickStartRequest, TickType]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type BridgeRpcService_TickStartClient = grpc.ServerStreamingClient[TickType]

func (c *bridgeRpcServiceClient) TickStop(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GenericResponseType, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenericResponseType)
	err := c.cc.Invoke(ctx, BridgeRpcService_TickStop_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bridgeRpcServiceClient) GetAccount(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GetAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountResponse)
	err := c.cc.Invoke(ctx, BridgeRpcService_GetAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bridgeRpcServiceClient) GetPositions(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GetPositionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPositionsResponse)
	err := c.cc.Invoke(ctx, BridgeRpcService_GetPositions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bridgeRpcServiceClient) GetOrders(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GetOrdersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOrdersResponse)
	err := c.cc.Invoke(ctx, BridgeRpcService_GetOrders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bridgeRpcServiceClient) GetAvailableSymbols(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GetAvailableSymbolsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAvailableSymbolsResponse)
	err := c.cc.Invoke(ctx, BridgeRpcService_GetAvailableSymbols_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bridgeRpcServiceClient) GetTradeHistory(ctx context.Context, in *GetTradeHistoryRequest, opts ...grpc.CallOption) (*GetTradeHistoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTradeHistoryResponse)
	err := c.cc.Invoke(ctx, BridgeRpcService_GetTradeHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bridgeRpcServiceClient) GetTicksFrom(ctx context.Context, in *GetTicksFromRequest, opts ...grpc.CallOption) (*GetTicksResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTicksResponse)
	err := c.cc.Invoke(ctx, BridgeRpcService_GetTicksFrom_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bridgeRpcServiceClient) GetTicksRange(ctx context.Context, in *GetTicksRangeRequest, opts ...grpc.CallOption) (*GetTicksResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTicksResponse)
	err := c.cc.Invoke(ctx, BridgeRpcService_GetTicksRange_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bridgeRpcServiceClient) CloseTrade(ctx context.Context, in *CloseTradeRequest, opts ...grpc.CallOption) (*GenericResponseType, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenericResponseType)
	err := c.cc.Invoke(ctx, BridgeRpcService_CloseTrade_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bridgeRpcServiceClient) ModifyTrade(ctx context.Context, in *ModifyTradeRequest, opts ...grpc.CallOption) (*GenericResponseType, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenericResponseType)
	err := c.cc.Invoke(ctx, BridgeRpcService_ModifyTrade_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bridgeRpcServiceClient) PlaceTrade(ctx context.Context, in *PlaceTradeRequest, opts ...grpc.CallOption) (*PlaceTradeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PlaceTradeResponse)
	err := c.cc.Invoke(ctx, BridgeRpcService_PlaceTrade_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bridgeRpcServiceClient) ManageSymbol(ctx context.Context, in *ManageSymbolRequest, opts ...grpc.CallOption) (*ManageSymbolResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ManageSymbolResponse)
	err := c.cc.Invoke(ctx, BridgeRpcService_ManageSymbol_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bridgeRpcServiceClient) GetTerminalError(ctx context.Context, in *EmptyType, opts ...grpc.CallOption) (*GenericResponseType, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenericResponseType)
	err := c.cc.Invoke(ctx, BridgeRpcService_GetTerminalError_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bridgeRpcServiceClient) GetSymbolTick(ctx context.Context, in *GetSymbolTickRequest, opts ...grpc.CallOption) (*TickType, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TickType)
	err := c.cc.Invoke(ctx, BridgeRpcService_GetSymbolTick_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BridgeRpcServiceServer is the server API for BridgeRpcService service.
// All implementations must embed UnimplementedBridgeRpcServiceServer
// for forward compatibility.
//
// service
type BridgeRpcServiceServer interface {
	TickStart(*TickStartRequest, grpc.ServerStreamingServer[TickType]) error
	TickStop(context.Context, *EmptyType) (*GenericResponseType, error)
	GetAccount(context.Context, *EmptyType) (*GetAccountResponse, error)
	GetPositions(context.Context, *EmptyType) (*GetPositionsResponse, error)
	GetOrders(context.Context, *EmptyType) (*GetOrdersResponse, error)
	GetAvailableSymbols(context.Context, *EmptyType) (*GetAvailableSymbolsResponse, error)
	GetTradeHistory(context.Context, *GetTradeHistoryRequest) (*GetTradeHistoryResponse, error)
	GetTicksFrom(context.Context, *GetTicksFromRequest) (*GetTicksResponse, error)
	GetTicksRange(context.Context, *GetTicksRangeRequest) (*GetTicksResponse, error)
	CloseTrade(context.Context, *CloseTradeRequest) (*GenericResponseType, error)
	ModifyTrade(context.Context, *ModifyTradeRequest) (*GenericResponseType, error)
	PlaceTrade(context.Context, *PlaceTradeRequest) (*PlaceTradeResponse, error)
	ManageSymbol(context.Context, *ManageSymbolRequest) (*ManageSymbolResponse, error)
	GetTerminalError(context.Context, *EmptyType) (*GenericResponseType, error)
	GetSymbolTick(context.Context, *GetSymbolTickRequest) (*TickType, error)
	mustEmbedUnimplementedBridgeRpcServiceServer()
}

// UnimplementedBridgeRpcServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBridgeRpcServiceServer struct{}

func (UnimplementedBridgeRpcServiceServer) TickStart(*TickStartRequest, grpc.ServerStreamingServer[TickType]) error {
	return status.Errorf(codes.Unimplemented, "method TickStart not implemented")
}
func (UnimplementedBridgeRpcServiceServer) TickStop(context.Context, *EmptyType) (*GenericResponseType, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TickStop not implemented")
}
func (UnimplementedBridgeRpcServiceServer) GetAccount(context.Context, *EmptyType) (*GetAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccount not implemented")
}
func (UnimplementedBridgeRpcServiceServer) GetPositions(context.Context, *EmptyType) (*GetPositionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPositions not implemented")
}
func (UnimplementedBridgeRpcServiceServer) GetOrders(context.Context, *EmptyType) (*GetOrdersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrders not implemented")
}
func (UnimplementedBridgeRpcServiceServer) GetAvailableSymbols(context.Context, *EmptyType) (*GetAvailableSymbolsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableSymbols not implemented")
}
func (UnimplementedBridgeRpcServiceServer) GetTradeHistory(context.Context, *GetTradeHistoryRequest) (*GetTradeHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTradeHistory not implemented")
}
func (UnimplementedBridgeRpcServiceServer) GetTicksFrom(context.Context, *GetTicksFromRequest) (*GetTicksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTicksFrom not implemented")
}
func (UnimplementedBridgeRpcServiceServer) GetTicksRange(context.Context, *GetTicksRangeRequest) (*GetTicksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTicksRange not implemented")
}
func (UnimplementedBridgeRpcServiceServer) CloseTrade(context.Context, *CloseTradeRequest) (*GenericResponseType, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloseTrade not implemented")
}
func (UnimplementedBridgeRpcServiceServer) ModifyTrade(context.Context, *ModifyTradeRequest) (*GenericResponseType, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyTrade not implemented")
}
func (UnimplementedBridgeRpcServiceServer) PlaceTrade(context.Context, *PlaceTradeRequest) (*PlaceTradeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlaceTrade not implemented")
}
func (UnimplementedBridgeRpcServiceServer) ManageSymbol(context.Context, *ManageSymbolRequest) (*ManageSymbolResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ManageSymbol not implemented")
}
func (UnimplementedBridgeRpcServiceServer) GetTerminalError(context.Context, *EmptyType) (*GenericResponseType, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTerminalError not implemented")
}
func (UnimplementedBridgeRpcServiceServer) GetSymbolTick(context.Context, *GetSymbolTickRequest) (*TickType, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSymbolTick not implemented")
}
func (UnimplementedBridgeRpcServiceServer) mustEmbedUnimplementedBridgeRpcServiceServer() {}
func (UnimplementedBridgeRpcServiceServer) testEmbeddedByValue()                          {}

// UnsafeBridgeRpcServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BridgeRpcServiceServer will
// result in compilation errors.
type UnsafeBridgeRpcServiceServer interface {
	mustEmbedUnimplementedBridgeRpcServiceServer()
}

func RegisterBridgeRpcServiceServer(s grpc.ServiceRegistrar, srv BridgeRpcServiceServer) {
	// If the following call pancis, it indicates UnimplementedBridgeRpcServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BridgeRpcService_ServiceDesc, srv)
}

func _BridgeRpcService_TickStart_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(TickStartRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(BridgeRpcServiceServer).TickStart(m, &grpc.GenericServerStream[TickStartRequest, TickType]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type BridgeRpcService_TickStartServer = grpc.ServerStreamingServer[TickType]

func _BridgeRpcService_TickStop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyType)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BridgeRpcServiceServer).TickStop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BridgeRpcService_TickStop_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BridgeRpcServiceServer).TickStop(ctx, req.(*EmptyType))
	}
	return interceptor(ctx, in, info, handler)
}

func _BridgeRpcService_GetAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyType)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BridgeRpcServiceServer).GetAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BridgeRpcService_GetAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BridgeRpcServiceServer).GetAccount(ctx, req.(*EmptyType))
	}
	return interceptor(ctx, in, info, handler)
}

func _BridgeRpcService_GetPositions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyType)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BridgeRpcServiceServer).GetPositions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BridgeRpcService_GetPositions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BridgeRpcServiceServer).GetPositions(ctx, req.(*EmptyType))
	}
	return interceptor(ctx, in, info, handler)
}

func _BridgeRpcService_GetOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyType)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BridgeRpcServiceServer).GetOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BridgeRpcService_GetOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BridgeRpcServiceServer).GetOrders(ctx, req.(*EmptyType))
	}
	return interceptor(ctx, in, info, handler)
}

func _BridgeRpcService_GetAvailableSymbols_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyType)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BridgeRpcServiceServer).GetAvailableSymbols(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BridgeRpcService_GetAvailableSymbols_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BridgeRpcServiceServer).GetAvailableSymbols(ctx, req.(*EmptyType))
	}
	return interceptor(ctx, in, info, handler)
}

func _BridgeRpcService_GetTradeHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTradeHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BridgeRpcServiceServer).GetTradeHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BridgeRpcService_GetTradeHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BridgeRpcServiceServer).GetTradeHistory(ctx, req.(*GetTradeHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BridgeRpcService_GetTicksFrom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTicksFromRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BridgeRpcServiceServer).GetTicksFrom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BridgeRpcService_GetTicksFrom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BridgeRpcServiceServer).GetTicksFrom(ctx, req.(*GetTicksFromRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BridgeRpcService_GetTicksRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTicksRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BridgeRpcServiceServer).GetTicksRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BridgeRpcService_GetTicksRange_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BridgeRpcServiceServer).GetTicksRange(ctx, req.(*GetTicksRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BridgeRpcService_CloseTrade_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseTradeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BridgeRpcServiceServer).CloseTrade(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BridgeRpcService_CloseTrade_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BridgeRpcServiceServer).CloseTrade(ctx, req.(*CloseTradeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BridgeRpcService_ModifyTrade_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyTradeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BridgeRpcServiceServer).ModifyTrade(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BridgeRpcService_ModifyTrade_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BridgeRpcServiceServer).ModifyTrade(ctx, req.(*ModifyTradeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BridgeRpcService_PlaceTrade_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlaceTradeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BridgeRpcServiceServer).PlaceTrade(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BridgeRpcService_PlaceTrade_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BridgeRpcServiceServer).PlaceTrade(ctx, req.(*PlaceTradeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BridgeRpcService_ManageSymbol_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManageSymbolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BridgeRpcServiceServer).ManageSymbol(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BridgeRpcService_ManageSymbol_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BridgeRpcServiceServer).ManageSymbol(ctx, req.(*ManageSymbolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BridgeRpcService_GetTerminalError_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyType)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BridgeRpcServiceServer).GetTerminalError(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BridgeRpcService_GetTerminalError_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BridgeRpcServiceServer).GetTerminalError(ctx, req.(*EmptyType))
	}
	return interceptor(ctx, in, info, handler)
}

func _BridgeRpcService_GetSymbolTick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSymbolTickRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BridgeRpcServiceServer).GetSymbolTick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BridgeRpcService_GetSymbolTick_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BridgeRpcServiceServer).GetSymbolTick(ctx, req.(*GetSymbolTickRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BridgeRpcService_ServiceDesc is the grpc.ServiceDesc for BridgeRpcService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BridgeRpcService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "bridge.BridgeRpcService",
	HandlerType: (*BridgeRpcServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TickStop",
			Handler:    _BridgeRpcService_TickStop_Handler,
		},
		{
			MethodName: "GetAccount",
			Handler:    _BridgeRpcService_GetAccount_Handler,
		},
		{
			MethodName: "GetPositions",
			Handler:    _BridgeRpcService_GetPositions_Handler,
		},
		{
			MethodName: "GetOrders",
			Handler:    _BridgeRpcService_GetOrders_Handler,
		},
		{
			MethodName: "GetAvailableSymbols",
			Handler:    _BridgeRpcService_GetAvailableSymbols_Handler,
		},
		{
			MethodName: "GetTradeHistory",
			Handler:    _BridgeRpcService_GetTradeHistory_Handler,
		},
		{
			MethodName: "GetTicksFrom",
			Handler:    _BridgeRpcService_GetTicksFrom_Handler,
		},
		{
			MethodName: "GetTicksRange",
			Handler:    _BridgeRpcService_GetTicksRange_Handler,
		},
		{
			MethodName: "CloseTrade",
			Handler:    _BridgeRpcService_CloseTrade_Handler,
		},
		{
			MethodName: "ModifyTrade",
			Handler:    _BridgeRpcService_ModifyTrade_Handler,
		},
		{
			MethodName: "PlaceTrade",
			Handler:    _BridgeRpcService_PlaceTrade_Handler,
		},
		{
			MethodName: "ManageSymbol",
			Handler:    _BridgeRpcService_ManageSymbol_Handler,
		},
		{
			MethodName: "GetTerminalError",
			Handler:    _BridgeRpcService_GetTerminalError_Handler,
		},
		{
			MethodName: "GetSymbolTick",
			Handler:    _BridgeRpcService_GetSymbolTick_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "TickStart",
			Handler:       _BridgeRpcService_TickStart_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bridge.proto",
}
