package bridge

import (
	"fmt"
	"reflect"
	"strings"
)

func toTitleCase(s string) string {
	if len(s) == 0 {
		return s
	}
	return strings.ToUpper(string(s[0])) + s[1:]
}

func MapToStruct(m map[string]interface{}, s interface{}) error {
	structValue := reflect.ValueOf(s).Elem()

	for key, value := range m {
		structFieldName := toTitleCase(key)
		field := structValue.FieldByName(structFieldName)
		if !field.IsValid() {
			continue // Skip if the field doesn't exist
		}
		if !field.CanSet() {
			return fmt.Errorf("cannot set field %s", structFieldName)
		}

		val := reflect.ValueOf(value)
		if !val.IsValid() { // Handle nil values
			if field.Kind() == reflect.Ptr || field.Kind() == reflect.Interface || field.Kind() == reflect.Map || field.Kind() == reflect.Slice {
				field.Set(reflect.Zero(field.Type()))
				continue
			} else {
				return fmt.<PERSON><PERSON><PERSON>("cannot assign nil to field %s of type %s", structFieldName, field.Type())
			}
		}

		// Handle nested maps for struct or pointer to struct fields
		if val.Kind() == reflect.Map {
			nestedMap := make(map[string]interface{})
			for _, k := range val.MapKeys() {
				if k.Kind() != reflect.String {
					return fmt.Errorf("nested map key in field %s is not a string", structFieldName)
				}
				nestedMap[k.String()] = val.MapIndex(k).Interface()
			}

			// Check if field is a struct or pointer to struct
			if field.Kind() == reflect.Struct {
				if err := MapToStruct(nestedMap, field.Addr().Interface()); err != nil {
					return fmt.Errorf("error mapping field %s: %v", structFieldName, err)
				}
				continue
			} else if field.Kind() == reflect.Ptr && field.Type().Elem().Kind() == reflect.Struct {
				if field.IsNil() {
					field.Set(reflect.New(field.Type().Elem()))
				}
				if err := MapToStruct(nestedMap, field.Interface()); err != nil {
					return fmt.Errorf("error mapping field %s: %v", structFieldName, err)
				}
				continue
			}
		}

		// Check type compatibility for non-nested fields
		// if field.Type() != val.Type() {
		// 	return fmt.Errorf("type mismatch for field %s: expected %v, got %v", structFieldName, field.Type(), val.Type())
		// }
		field.Set(val)
	}
	return nil
}
