// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.3
// 	protoc        v5.26.1
// source: bridge.proto

package bridge

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// types
type EmptyType struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmptyType) Reset() {
	*x = EmptyType{}
	mi := &file_bridge_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmptyType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyType) ProtoMessage() {}

func (x *EmptyType) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyType.ProtoReflect.Descriptor instead.
func (*EmptyType) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{0}
}

type TickType struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ask           float32                `protobuf:"fixed32,1,opt,name=ask,proto3" json:"ask,omitempty"`
	Bid           float32                `protobuf:"fixed32,2,opt,name=bid,proto3" json:"bid,omitempty"`
	Time          int64                  `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TickType) Reset() {
	*x = TickType{}
	mi := &file_bridge_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TickType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TickType) ProtoMessage() {}

func (x *TickType) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TickType.ProtoReflect.Descriptor instead.
func (*TickType) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{1}
}

func (x *TickType) GetAsk() float32 {
	if x != nil {
		return x.Ask
	}
	return 0
}

func (x *TickType) GetBid() float32 {
	if x != nil {
		return x.Bid
	}
	return 0
}

func (x *TickType) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

type AccountPositionType struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ticket        int64                  `protobuf:"varint,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
	Symbol        string                 `protobuf:"bytes,2,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Type          int64                  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Volume        float32                `protobuf:"fixed32,4,opt,name=volume,proto3" json:"volume,omitempty"`
	OpenPrice     float32                `protobuf:"fixed32,5,opt,name=open_price,json=openPrice,proto3" json:"open_price,omitempty"`
	CurrentPrice  float32                `protobuf:"fixed32,6,opt,name=current_price,json=currentPrice,proto3" json:"current_price,omitempty"`
	StopLoss      float32                `protobuf:"fixed32,7,opt,name=stop_loss,json=stopLoss,proto3" json:"stop_loss,omitempty"`
	TakeProfit    float32                `protobuf:"fixed32,8,opt,name=take_profit,json=takeProfit,proto3" json:"take_profit,omitempty"`
	Profit        float32                `protobuf:"fixed32,9,opt,name=profit,proto3" json:"profit,omitempty"`
	Comment       string                 `protobuf:"bytes,10,opt,name=comment,proto3" json:"comment,omitempty"`
	Magic         int64                  `protobuf:"varint,11,opt,name=magic,proto3" json:"magic,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountPositionType) Reset() {
	*x = AccountPositionType{}
	mi := &file_bridge_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountPositionType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountPositionType) ProtoMessage() {}

func (x *AccountPositionType) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountPositionType.ProtoReflect.Descriptor instead.
func (*AccountPositionType) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{2}
}

func (x *AccountPositionType) GetTicket() int64 {
	if x != nil {
		return x.Ticket
	}
	return 0
}

func (x *AccountPositionType) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *AccountPositionType) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AccountPositionType) GetVolume() float32 {
	if x != nil {
		return x.Volume
	}
	return 0
}

func (x *AccountPositionType) GetOpenPrice() float32 {
	if x != nil {
		return x.OpenPrice
	}
	return 0
}

func (x *AccountPositionType) GetCurrentPrice() float32 {
	if x != nil {
		return x.CurrentPrice
	}
	return 0
}

func (x *AccountPositionType) GetStopLoss() float32 {
	if x != nil {
		return x.StopLoss
	}
	return 0
}

func (x *AccountPositionType) GetTakeProfit() float32 {
	if x != nil {
		return x.TakeProfit
	}
	return 0
}

func (x *AccountPositionType) GetProfit() float32 {
	if x != nil {
		return x.Profit
	}
	return 0
}

func (x *AccountPositionType) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *AccountPositionType) GetMagic() int64 {
	if x != nil {
		return x.Magic
	}
	return 0
}

type AccountOrdersType struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Ticket         int64                  `protobuf:"varint,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
	Symbol         string                 `protobuf:"bytes,2,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Type           int64                  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Volume         float32                `protobuf:"fixed32,4,opt,name=volume,proto3" json:"volume,omitempty"`
	PriceOpen      float32                `protobuf:"fixed32,5,opt,name=price_open,json=priceOpen,proto3" json:"price_open,omitempty"`
	PriceCurrent   float32                `protobuf:"fixed32,6,opt,name=price_current,json=priceCurrent,proto3" json:"price_current,omitempty"`
	StopLoss       float32                `protobuf:"fixed32,7,opt,name=stop_loss,json=stopLoss,proto3" json:"stop_loss,omitempty"`
	TakeProfit     float32                `protobuf:"fixed32,8,opt,name=take_profit,json=takeProfit,proto3" json:"take_profit,omitempty"`
	Comment        string                 `protobuf:"bytes,9,opt,name=comment,proto3" json:"comment,omitempty"`
	TimeSetup      int64                  `protobuf:"varint,10,opt,name=time_setup,json=timeSetup,proto3" json:"time_setup,omitempty"`
	TimeExpiration int64                  `protobuf:"varint,11,opt,name=time_expiration,json=timeExpiration,proto3" json:"time_expiration,omitempty"`
	Magic          int64                  `protobuf:"varint,12,opt,name=magic,proto3" json:"magic,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AccountOrdersType) Reset() {
	*x = AccountOrdersType{}
	mi := &file_bridge_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountOrdersType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountOrdersType) ProtoMessage() {}

func (x *AccountOrdersType) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountOrdersType.ProtoReflect.Descriptor instead.
func (*AccountOrdersType) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{3}
}

func (x *AccountOrdersType) GetTicket() int64 {
	if x != nil {
		return x.Ticket
	}
	return 0
}

func (x *AccountOrdersType) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *AccountOrdersType) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AccountOrdersType) GetVolume() float32 {
	if x != nil {
		return x.Volume
	}
	return 0
}

func (x *AccountOrdersType) GetPriceOpen() float32 {
	if x != nil {
		return x.PriceOpen
	}
	return 0
}

func (x *AccountOrdersType) GetPriceCurrent() float32 {
	if x != nil {
		return x.PriceCurrent
	}
	return 0
}

func (x *AccountOrdersType) GetStopLoss() float32 {
	if x != nil {
		return x.StopLoss
	}
	return 0
}

func (x *AccountOrdersType) GetTakeProfit() float32 {
	if x != nil {
		return x.TakeProfit
	}
	return 0
}

func (x *AccountOrdersType) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *AccountOrdersType) GetTimeSetup() int64 {
	if x != nil {
		return x.TimeSetup
	}
	return 0
}

func (x *AccountOrdersType) GetTimeExpiration() int64 {
	if x != nil {
		return x.TimeExpiration
	}
	return 0
}

func (x *AccountOrdersType) GetMagic() int64 {
	if x != nil {
		return x.Magic
	}
	return 0
}

type SymbolsType struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Path          string                 `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	Digits        int64                  `protobuf:"varint,4,opt,name=digits,proto3" json:"digits,omitempty"`
	Spread        float32                `protobuf:"fixed32,5,opt,name=spread,proto3" json:"spread,omitempty"`
	Time          int64                  `protobuf:"varint,6,opt,name=time,proto3" json:"time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SymbolsType) Reset() {
	*x = SymbolsType{}
	mi := &file_bridge_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SymbolsType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SymbolsType) ProtoMessage() {}

func (x *SymbolsType) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SymbolsType.ProtoReflect.Descriptor instead.
func (*SymbolsType) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{4}
}

func (x *SymbolsType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SymbolsType) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SymbolsType) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *SymbolsType) GetDigits() int64 {
	if x != nil {
		return x.Digits
	}
	return 0
}

func (x *SymbolsType) GetSpread() float32 {
	if x != nil {
		return x.Spread
	}
	return 0
}

func (x *SymbolsType) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

type TradeDealType struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ticket        int64                  `protobuf:"varint,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
	Order         int64                  `protobuf:"varint,2,opt,name=order,proto3" json:"order,omitempty"`
	Time          int64                  `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	Type          int64                  `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	Entry         float32                `protobuf:"fixed32,5,opt,name=entry,proto3" json:"entry,omitempty"`
	Symbol        string                 `protobuf:"bytes,6,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Volume        float32                `protobuf:"fixed32,7,opt,name=volume,proto3" json:"volume,omitempty"`
	Price         float32                `protobuf:"fixed32,8,opt,name=price,proto3" json:"price,omitempty"`
	Commission    float32                `protobuf:"fixed32,9,opt,name=commission,proto3" json:"commission,omitempty"`
	Swap          float32                `protobuf:"fixed32,10,opt,name=swap,proto3" json:"swap,omitempty"`
	Profit        float32                `protobuf:"fixed32,11,opt,name=profit,proto3" json:"profit,omitempty"`
	Magic         int64                  `protobuf:"varint,12,opt,name=magic,proto3" json:"magic,omitempty"`
	Comment       string                 `protobuf:"bytes,13,opt,name=comment,proto3" json:"comment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TradeDealType) Reset() {
	*x = TradeDealType{}
	mi := &file_bridge_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TradeDealType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TradeDealType) ProtoMessage() {}

func (x *TradeDealType) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TradeDealType.ProtoReflect.Descriptor instead.
func (*TradeDealType) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{5}
}

func (x *TradeDealType) GetTicket() int64 {
	if x != nil {
		return x.Ticket
	}
	return 0
}

func (x *TradeDealType) GetOrder() int64 {
	if x != nil {
		return x.Order
	}
	return 0
}

func (x *TradeDealType) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *TradeDealType) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *TradeDealType) GetEntry() float32 {
	if x != nil {
		return x.Entry
	}
	return 0
}

func (x *TradeDealType) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *TradeDealType) GetVolume() float32 {
	if x != nil {
		return x.Volume
	}
	return 0
}

func (x *TradeDealType) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *TradeDealType) GetCommission() float32 {
	if x != nil {
		return x.Commission
	}
	return 0
}

func (x *TradeDealType) GetSwap() float32 {
	if x != nil {
		return x.Swap
	}
	return 0
}

func (x *TradeDealType) GetProfit() float32 {
	if x != nil {
		return x.Profit
	}
	return 0
}

func (x *TradeDealType) GetMagic() int64 {
	if x != nil {
		return x.Magic
	}
	return 0
}

func (x *TradeDealType) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

type GetSymbolTickRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSymbolTickRequest) Reset() {
	*x = GetSymbolTickRequest{}
	mi := &file_bridge_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSymbolTickRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSymbolTickRequest) ProtoMessage() {}

func (x *GetSymbolTickRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSymbolTickRequest.ProtoReflect.Descriptor instead.
func (*GetSymbolTickRequest) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{6}
}

func (x *GetSymbolTickRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

type TickStartRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Rate          float32                `protobuf:"fixed32,2,opt,name=rate,proto3" json:"rate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TickStartRequest) Reset() {
	*x = TickStartRequest{}
	mi := &file_bridge_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TickStartRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TickStartRequest) ProtoMessage() {}

func (x *TickStartRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TickStartRequest.ProtoReflect.Descriptor instead.
func (*TickStartRequest) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{7}
}

func (x *TickStartRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *TickStartRequest) GetRate() float32 {
	if x != nil {
		return x.Rate
	}
	return 0
}

type CloseTradeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ticket        int64                  `protobuf:"varint,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
	Volume        *float32               `protobuf:"fixed32,2,opt,name=volume,proto3,oneof" json:"volume,omitempty"`
	Price         *float32               `protobuf:"fixed32,3,opt,name=price,proto3,oneof" json:"price,omitempty"`
	Slippage      *float32               `protobuf:"fixed32,4,opt,name=slippage,proto3,oneof" json:"slippage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CloseTradeRequest) Reset() {
	*x = CloseTradeRequest{}
	mi := &file_bridge_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CloseTradeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseTradeRequest) ProtoMessage() {}

func (x *CloseTradeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseTradeRequest.ProtoReflect.Descriptor instead.
func (*CloseTradeRequest) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{8}
}

func (x *CloseTradeRequest) GetTicket() int64 {
	if x != nil {
		return x.Ticket
	}
	return 0
}

func (x *CloseTradeRequest) GetVolume() float32 {
	if x != nil && x.Volume != nil {
		return *x.Volume
	}
	return 0
}

func (x *CloseTradeRequest) GetPrice() float32 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *CloseTradeRequest) GetSlippage() float32 {
	if x != nil && x.Slippage != nil {
		return *x.Slippage
	}
	return 0
}

type GetTradeHistoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StartDate     int64                  `protobuf:"varint,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       int64                  `protobuf:"varint,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTradeHistoryRequest) Reset() {
	*x = GetTradeHistoryRequest{}
	mi := &file_bridge_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTradeHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTradeHistoryRequest) ProtoMessage() {}

func (x *GetTradeHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTradeHistoryRequest.ProtoReflect.Descriptor instead.
func (*GetTradeHistoryRequest) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{9}
}

func (x *GetTradeHistoryRequest) GetStartDate() int64 {
	if x != nil {
		return x.StartDate
	}
	return 0
}

func (x *GetTradeHistoryRequest) GetEndDate() int64 {
	if x != nil {
		return x.EndDate
	}
	return 0
}

type GetTicksFromRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	StartDate     int64                  `protobuf:"varint,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	Length        int64                  `protobuf:"varint,3,opt,name=length,proto3" json:"length,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTicksFromRequest) Reset() {
	*x = GetTicksFromRequest{}
	mi := &file_bridge_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTicksFromRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicksFromRequest) ProtoMessage() {}

func (x *GetTicksFromRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicksFromRequest.ProtoReflect.Descriptor instead.
func (*GetTicksFromRequest) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{10}
}

func (x *GetTicksFromRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *GetTicksFromRequest) GetStartDate() int64 {
	if x != nil {
		return x.StartDate
	}
	return 0
}

func (x *GetTicksFromRequest) GetLength() int64 {
	if x != nil {
		return x.Length
	}
	return 0
}

type GetTicksRangeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	StartDate     int64                  `protobuf:"varint,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       int64                  `protobuf:"varint,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Length        int64                  `protobuf:"varint,4,opt,name=length,proto3" json:"length,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTicksRangeRequest) Reset() {
	*x = GetTicksRangeRequest{}
	mi := &file_bridge_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTicksRangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicksRangeRequest) ProtoMessage() {}

func (x *GetTicksRangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicksRangeRequest.ProtoReflect.Descriptor instead.
func (*GetTicksRangeRequest) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{11}
}

func (x *GetTicksRangeRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *GetTicksRangeRequest) GetStartDate() int64 {
	if x != nil {
		return x.StartDate
	}
	return 0
}

func (x *GetTicksRangeRequest) GetEndDate() int64 {
	if x != nil {
		return x.EndDate
	}
	return 0
}

func (x *GetTicksRangeRequest) GetLength() int64 {
	if x != nil {
		return x.Length
	}
	return 0
}

type ModifyTradeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ticket        int64                  `protobuf:"varint,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
	StopLoss      float32                `protobuf:"fixed32,2,opt,name=stop_loss,json=stopLoss,proto3" json:"stop_loss,omitempty"`
	TakeProfit    float32                `protobuf:"fixed32,3,opt,name=take_profit,json=takeProfit,proto3" json:"take_profit,omitempty"`
	Price         *float32               `protobuf:"fixed32,4,opt,name=price,proto3,oneof" json:"price,omitempty"`
	Expiration    *int64                 `protobuf:"varint,5,opt,name=expiration,proto3,oneof" json:"expiration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModifyTradeRequest) Reset() {
	*x = ModifyTradeRequest{}
	mi := &file_bridge_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModifyTradeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyTradeRequest) ProtoMessage() {}

func (x *ModifyTradeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyTradeRequest.ProtoReflect.Descriptor instead.
func (*ModifyTradeRequest) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{12}
}

func (x *ModifyTradeRequest) GetTicket() int64 {
	if x != nil {
		return x.Ticket
	}
	return 0
}

func (x *ModifyTradeRequest) GetStopLoss() float32 {
	if x != nil {
		return x.StopLoss
	}
	return 0
}

func (x *ModifyTradeRequest) GetTakeProfit() float32 {
	if x != nil {
		return x.TakeProfit
	}
	return 0
}

func (x *ModifyTradeRequest) GetPrice() float32 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *ModifyTradeRequest) GetExpiration() int64 {
	if x != nil && x.Expiration != nil {
		return *x.Expiration
	}
	return 0
}

type PlaceTradeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	ActionType    int64                  `protobuf:"varint,2,opt,name=action_type,json=actionType,proto3" json:"action_type,omitempty"`
	Volume        float32                `protobuf:"fixed32,3,opt,name=volume,proto3" json:"volume,omitempty"`
	StopLoss      *float32               `protobuf:"fixed32,4,opt,name=stop_loss,json=stopLoss,proto3,oneof" json:"stop_loss,omitempty"`
	TakeProfit    *float32               `protobuf:"fixed32,5,opt,name=take_profit,json=takeProfit,proto3,oneof" json:"take_profit,omitempty"`
	Comment       *string                `protobuf:"bytes,6,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
	Price         *float32               `protobuf:"fixed32,7,opt,name=price,proto3,oneof" json:"price,omitempty"`
	Order         *int64                 `protobuf:"varint,8,opt,name=order,proto3,oneof" json:"order,omitempty"`
	Magic         *int64                 `protobuf:"varint,9,opt,name=magic,proto3,oneof" json:"magic,omitempty"`
	StopLimit     *float32               `protobuf:"fixed32,10,opt,name=stop_limit,json=stopLimit,proto3,oneof" json:"stop_limit,omitempty"`
	Expiration    *int64                 `protobuf:"varint,11,opt,name=expiration,proto3,oneof" json:"expiration,omitempty"`
	Position      *int64                 `protobuf:"varint,12,opt,name=position,proto3,oneof" json:"position,omitempty"`
	PositionBy    *int64                 `protobuf:"varint,13,opt,name=position_by,json=positionBy,proto3,oneof" json:"position_by,omitempty"`
	Deviation     *int64                 `protobuf:"varint,14,opt,name=deviation,proto3,oneof" json:"deviation,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaceTradeRequest) Reset() {
	*x = PlaceTradeRequest{}
	mi := &file_bridge_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaceTradeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaceTradeRequest) ProtoMessage() {}

func (x *PlaceTradeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaceTradeRequest.ProtoReflect.Descriptor instead.
func (*PlaceTradeRequest) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{13}
}

func (x *PlaceTradeRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *PlaceTradeRequest) GetActionType() int64 {
	if x != nil {
		return x.ActionType
	}
	return 0
}

func (x *PlaceTradeRequest) GetVolume() float32 {
	if x != nil {
		return x.Volume
	}
	return 0
}

func (x *PlaceTradeRequest) GetStopLoss() float32 {
	if x != nil && x.StopLoss != nil {
		return *x.StopLoss
	}
	return 0
}

func (x *PlaceTradeRequest) GetTakeProfit() float32 {
	if x != nil && x.TakeProfit != nil {
		return *x.TakeProfit
	}
	return 0
}

func (x *PlaceTradeRequest) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

func (x *PlaceTradeRequest) GetPrice() float32 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *PlaceTradeRequest) GetOrder() int64 {
	if x != nil && x.Order != nil {
		return *x.Order
	}
	return 0
}

func (x *PlaceTradeRequest) GetMagic() int64 {
	if x != nil && x.Magic != nil {
		return *x.Magic
	}
	return 0
}

func (x *PlaceTradeRequest) GetStopLimit() float32 {
	if x != nil && x.StopLimit != nil {
		return *x.StopLimit
	}
	return 0
}

func (x *PlaceTradeRequest) GetExpiration() int64 {
	if x != nil && x.Expiration != nil {
		return *x.Expiration
	}
	return 0
}

func (x *PlaceTradeRequest) GetPosition() int64 {
	if x != nil && x.Position != nil {
		return *x.Position
	}
	return 0
}

func (x *PlaceTradeRequest) GetPositionBy() int64 {
	if x != nil && x.PositionBy != nil {
		return *x.PositionBy
	}
	return 0
}

func (x *PlaceTradeRequest) GetDeviation() int64 {
	if x != nil && x.Deviation != nil {
		return *x.Deviation
	}
	return 0
}

type ManageSymbolRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Action        string                 `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ManageSymbolRequest) Reset() {
	*x = ManageSymbolRequest{}
	mi := &file_bridge_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ManageSymbolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManageSymbolRequest) ProtoMessage() {}

func (x *ManageSymbolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManageSymbolRequest.ProtoReflect.Descriptor instead.
func (*ManageSymbolRequest) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{14}
}

func (x *ManageSymbolRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *ManageSymbolRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

type GenericResponseType struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        int64                  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenericResponseType) Reset() {
	*x = GenericResponseType{}
	mi := &file_bridge_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenericResponseType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenericResponseType) ProtoMessage() {}

func (x *GenericResponseType) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenericResponseType.ProtoReflect.Descriptor instead.
func (*GenericResponseType) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{15}
}

func (x *GenericResponseType) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GenericResponseType) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Balance       float32                `protobuf:"fixed32,1,opt,name=balance,proto3" json:"balance,omitempty"`
	Equity        float32                `protobuf:"fixed32,2,opt,name=equity,proto3" json:"equity,omitempty"`
	Margin        float32                `protobuf:"fixed32,3,opt,name=margin,proto3" json:"margin,omitempty"`
	FreeMargin    float32                `protobuf:"fixed32,4,opt,name=free_margin,json=freeMargin,proto3" json:"free_margin,omitempty"`
	MarginLevel   float32                `protobuf:"fixed32,5,opt,name=margin_level,json=marginLevel,proto3" json:"margin_level,omitempty"`
	Profit        float32                `protobuf:"fixed32,6,opt,name=profit,proto3" json:"profit,omitempty"`
	Server        string                 `protobuf:"bytes,7,opt,name=server,proto3" json:"server,omitempty"`
	TradeMode     int64                  `protobuf:"varint,8,opt,name=trade_mode,json=tradeMode,proto3" json:"trade_mode,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountResponse) Reset() {
	*x = GetAccountResponse{}
	mi := &file_bridge_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountResponse) ProtoMessage() {}

func (x *GetAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountResponse.ProtoReflect.Descriptor instead.
func (*GetAccountResponse) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{16}
}

func (x *GetAccountResponse) GetBalance() float32 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *GetAccountResponse) GetEquity() float32 {
	if x != nil {
		return x.Equity
	}
	return 0
}

func (x *GetAccountResponse) GetMargin() float32 {
	if x != nil {
		return x.Margin
	}
	return 0
}

func (x *GetAccountResponse) GetFreeMargin() float32 {
	if x != nil {
		return x.FreeMargin
	}
	return 0
}

func (x *GetAccountResponse) GetMarginLevel() float32 {
	if x != nil {
		return x.MarginLevel
	}
	return 0
}

func (x *GetAccountResponse) GetProfit() float32 {
	if x != nil {
		return x.Profit
	}
	return 0
}

func (x *GetAccountResponse) GetServer() string {
	if x != nil {
		return x.Server
	}
	return ""
}

func (x *GetAccountResponse) GetTradeMode() int64 {
	if x != nil {
		return x.TradeMode
	}
	return 0
}

type GetPositionsResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Positions      []*AccountPositionType `protobuf:"bytes,1,rep,name=positions,proto3" json:"positions,omitempty"`
	TotalPositions int64                  `protobuf:"varint,2,opt,name=total_positions,json=totalPositions,proto3" json:"total_positions,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetPositionsResponse) Reset() {
	*x = GetPositionsResponse{}
	mi := &file_bridge_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPositionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPositionsResponse) ProtoMessage() {}

func (x *GetPositionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPositionsResponse.ProtoReflect.Descriptor instead.
func (*GetPositionsResponse) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{17}
}

func (x *GetPositionsResponse) GetPositions() []*AccountPositionType {
	if x != nil {
		return x.Positions
	}
	return nil
}

func (x *GetPositionsResponse) GetTotalPositions() int64 {
	if x != nil {
		return x.TotalPositions
	}
	return 0
}

type GetOrdersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Orders        []*AccountOrdersType   `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	TotalOrders   int64                  `protobuf:"varint,2,opt,name=total_orders,json=totalOrders,proto3" json:"total_orders,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrdersResponse) Reset() {
	*x = GetOrdersResponse{}
	mi := &file_bridge_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrdersResponse) ProtoMessage() {}

func (x *GetOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrdersResponse.ProtoReflect.Descriptor instead.
func (*GetOrdersResponse) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{18}
}

func (x *GetOrdersResponse) GetOrders() []*AccountOrdersType {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *GetOrdersResponse) GetTotalOrders() int64 {
	if x != nil {
		return x.TotalOrders
	}
	return 0
}

type GetAvailableSymbolsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbols       []*SymbolsType         `protobuf:"bytes,1,rep,name=symbols,proto3" json:"symbols,omitempty"`
	TotalSymbols  int64                  `protobuf:"varint,2,opt,name=total_symbols,json=totalSymbols,proto3" json:"total_symbols,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAvailableSymbolsResponse) Reset() {
	*x = GetAvailableSymbolsResponse{}
	mi := &file_bridge_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAvailableSymbolsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableSymbolsResponse) ProtoMessage() {}

func (x *GetAvailableSymbolsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableSymbolsResponse.ProtoReflect.Descriptor instead.
func (*GetAvailableSymbolsResponse) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{19}
}

func (x *GetAvailableSymbolsResponse) GetSymbols() []*SymbolsType {
	if x != nil {
		return x.Symbols
	}
	return nil
}

func (x *GetAvailableSymbolsResponse) GetTotalSymbols() int64 {
	if x != nil {
		return x.TotalSymbols
	}
	return 0
}

type GetTradeHistoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Deals         []*TradeDealType       `protobuf:"bytes,1,rep,name=deals,proto3" json:"deals,omitempty"`
	TotalDeals    int64                  `protobuf:"varint,2,opt,name=total_deals,json=totalDeals,proto3" json:"total_deals,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTradeHistoryResponse) Reset() {
	*x = GetTradeHistoryResponse{}
	mi := &file_bridge_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTradeHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTradeHistoryResponse) ProtoMessage() {}

func (x *GetTradeHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTradeHistoryResponse.ProtoReflect.Descriptor instead.
func (*GetTradeHistoryResponse) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{20}
}

func (x *GetTradeHistoryResponse) GetDeals() []*TradeDealType {
	if x != nil {
		return x.Deals
	}
	return nil
}

func (x *GetTradeHistoryResponse) GetTotalDeals() int64 {
	if x != nil {
		return x.TotalDeals
	}
	return 0
}

type GetTicksResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ticks         []*TickType            `protobuf:"bytes,1,rep,name=ticks,proto3" json:"ticks,omitempty"`
	TotalTicks    int64                  `protobuf:"varint,2,opt,name=total_ticks,json=totalTicks,proto3" json:"total_ticks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTicksResponse) Reset() {
	*x = GetTicksResponse{}
	mi := &file_bridge_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTicksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicksResponse) ProtoMessage() {}

func (x *GetTicksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicksResponse.ProtoReflect.Descriptor instead.
func (*GetTicksResponse) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{21}
}

func (x *GetTicksResponse) GetTicks() []*TickType {
	if x != nil {
		return x.Ticks
	}
	return nil
}

func (x *GetTicksResponse) GetTotalTicks() int64 {
	if x != nil {
		return x.TotalTicks
	}
	return 0
}

type PlaceTradeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        int64                  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Ticket        *string                `protobuf:"bytes,3,opt,name=ticket,proto3,oneof" json:"ticket,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaceTradeResponse) Reset() {
	*x = PlaceTradeResponse{}
	mi := &file_bridge_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaceTradeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaceTradeResponse) ProtoMessage() {}

func (x *PlaceTradeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaceTradeResponse.ProtoReflect.Descriptor instead.
func (*PlaceTradeResponse) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{22}
}

func (x *PlaceTradeResponse) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PlaceTradeResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PlaceTradeResponse) GetTicket() string {
	if x != nil && x.Ticket != nil {
		return *x.Ticket
	}
	return ""
}

type ManageSymbolResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        int64                  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Symbol        *SymbolsType           `protobuf:"bytes,3,opt,name=symbol,proto3,oneof" json:"symbol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ManageSymbolResponse) Reset() {
	*x = ManageSymbolResponse{}
	mi := &file_bridge_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ManageSymbolResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManageSymbolResponse) ProtoMessage() {}

func (x *ManageSymbolResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bridge_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManageSymbolResponse.ProtoReflect.Descriptor instead.
func (*ManageSymbolResponse) Descriptor() ([]byte, []int) {
	return file_bridge_proto_rawDescGZIP(), []int{23}
}

func (x *ManageSymbolResponse) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ManageSymbolResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ManageSymbolResponse) GetSymbol() *SymbolsType {
	if x != nil {
		return x.Symbol
	}
	return nil
}

var File_bridge_proto protoreflect.FileDescriptor

var file_bridge_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06,
	0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x22, 0x0b, 0x0a, 0x09, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x42, 0x0a, 0x08, 0x54, 0x69, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x61, 0x73, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x61, 0x73,
	0x6b, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03,
	0x62, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xbb, 0x02, 0x0a, 0x13, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x09, 0x6f, 0x70, 0x65, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x73, 0x74, 0x6f, 0x70, 0x5f, 0x6c, 0x6f, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x08, 0x73, 0x74, 0x6f, 0x70, 0x4c, 0x6f, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x61, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0a, 0x74, 0x61, 0x6b, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x6d, 0x61, 0x67, 0x69, 0x63, 0x22, 0xe9, 0x02, 0x0a, 0x11, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73,
	0x74, 0x6f, 0x70, 0x5f, 0x6c, 0x6f, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08,
	0x73, 0x74, 0x6f, 0x70, 0x4c, 0x6f, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x6b, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x74,
	0x61, 0x6b, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x75,
	0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x74,
	0x75, 0x70, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x74, 0x69, 0x6d,
	0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6d,
	0x61, 0x67, 0x69, 0x63, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6d, 0x61, 0x67, 0x69,
	0x63, 0x22, 0x9b, 0x01, 0x0a, 0x0b, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x64,
	0x69, 0x67, 0x69, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x64, 0x69, 0x67,
	0x69, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x70, 0x72, 0x65, 0x61, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x06, 0x73, 0x70, 0x72, 0x65, 0x61, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x22,
	0xbd, 0x02, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x64, 0x65, 0x44, 0x65, 0x61, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6e, 0x74, 0x72, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x77, 0x61, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x04, 0x73, 0x77, 0x61, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x6d, 0x61, 0x67, 0x69, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22,
	0x2e, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x54, 0x69, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x22,
	0x3e, 0x0a, 0x10, 0x54, 0x69, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x22,
	0xa6, 0x01, 0x0a, 0x11, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x1b, 0x0a,
	0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52,
	0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x48, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x73, 0x6c, 0x69, 0x70, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x48, 0x02, 0x52, 0x08, 0x73, 0x6c, 0x69, 0x70, 0x70,
	0x61, 0x67, 0x65, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x73, 0x6c, 0x69, 0x70, 0x70, 0x61, 0x67, 0x65, 0x22, 0x52, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0x64, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65,
	0x6e, 0x67, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67,
	0x74, 0x68, 0x22, 0x80, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x22, 0xc3, 0x01, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x6f, 0x70, 0x5f, 0x6c, 0x6f, 0x73,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x73, 0x74, 0x6f, 0x70, 0x4c, 0x6f, 0x73,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x74, 0x61, 0x6b, 0x65, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x74, 0x12, 0x19, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x02, 0x48, 0x00, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a,
	0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x01, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe0, 0x04, 0x0a, 0x11,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x54, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x09, 0x73, 0x74, 0x6f, 0x70, 0x5f, 0x6c, 0x6f, 0x73, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x08, 0x73, 0x74, 0x6f, 0x70, 0x4c, 0x6f, 0x73,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x74, 0x61, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x48, 0x01, 0x52, 0x0a, 0x74, 0x61, 0x6b,
	0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x07, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x48, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x04, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12,
	0x19, 0x0a, 0x05, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x48, 0x05,
	0x52, 0x05, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74,
	0x6f, 0x70, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x48, 0x06,
	0x52, 0x09, 0x73, 0x74, 0x6f, 0x70, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x23,
	0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x07, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x03, 0x48, 0x08, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x48, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x09, 0x64, 0x65,
	0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x48, 0x0a, 0x52,
	0x09, 0x64, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a,
	0x0a, 0x5f, 0x73, 0x74, 0x6f, 0x70, 0x5f, 0x6c, 0x6f, 0x73, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x74, 0x61, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x42, 0x0a, 0x0a, 0x08, 0x5f,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x08, 0x0a, 0x06, 0x5f,
	0x6d, 0x61, 0x67, 0x69, 0x63, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x6f, 0x70, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x79,
	0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x45,
	0x0a, 0x13, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x47, 0x0a, 0x13, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x69, 0x63,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xf1,
	0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x06, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61, 0x72, 0x67, 0x69,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x6d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x12,
	0x1f, 0x0a, 0x0b, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x66, 0x72, 0x65, 0x65, 0x4d, 0x61, 0x72, 0x67, 0x69, 0x6e,
	0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x6d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x06, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x22, 0x7a, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x69,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x22, 0x71, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d, 0x0a, 0x07, 0x73, 0x79, 0x6d, 0x62,
	0x6f, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x62, 0x72, 0x69, 0x64,
	0x67, 0x65, 0x2e, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07,
	0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x22, 0x67, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x44, 0x65, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x64,
	0x65, 0x61, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x65,
	0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x44, 0x65, 0x61, 0x6c, 0x73, 0x22, 0x5b, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x05, 0x74, 0x69, 0x63,
	0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67,
	0x65, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x74, 0x69, 0x63, 0x6b,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x69, 0x63,
	0x6b, 0x73, 0x22, 0x6e, 0x0a, 0x12, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x06, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x22, 0x85, 0x01, 0x0a, 0x14, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x53, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x30, 0x0a,
	0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x48, 0x00, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x88, 0x01, 0x01, 0x42,
	0x09, 0x0a, 0x07, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x32, 0x98, 0x08, 0x0a, 0x10, 0x42,
	0x72, 0x69, 0x64, 0x67, 0x65, 0x52, 0x70, 0x63, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x39, 0x0a, 0x09, 0x54, 0x69, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x18, 0x2e, 0x62,
	0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x10, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e,
	0x54, 0x69, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x30, 0x01, 0x12, 0x3a, 0x0a, 0x08, 0x54, 0x69,
	0x63, 0x6b, 0x53, 0x74, 0x6f, 0x70, 0x12, 0x11, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x1b, 0x2e, 0x62, 0x72, 0x69, 0x64,
	0x67, 0x65, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x11, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x1a, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x11, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x1c, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x12, 0x11, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x1a, 0x19, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4d, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x12, 0x11, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x23, 0x2e, 0x62, 0x72, 0x69, 0x64,
	0x67, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x12, 0x1e, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1f, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x45, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x46, 0x72,
	0x6f, 0x6d, 0x12, 0x1b, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x69, 0x63, 0x6b, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x18, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0d, 0x47, 0x65, 0x74,
	0x54, 0x69, 0x63, 0x6b, 0x73, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1c, 0x2e, 0x62, 0x72, 0x69,
	0x64, 0x67, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x44, 0x0a, 0x0a, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x12, 0x19, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x62, 0x72,
	0x69, 0x64, 0x67, 0x65, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x46, 0x0a, 0x0b, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x54, 0x72, 0x61, 0x64, 0x65, 0x12, 0x1a, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65,
	0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x43, 0x0a, 0x0a, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x54, 0x72, 0x61, 0x64, 0x65, 0x12, 0x19,
	0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x62, 0x72, 0x69, 0x64,
	0x67, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x54, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x0c, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x53,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1b, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x4d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x42, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x11, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x1b, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65,
	0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x54, 0x69, 0x63, 0x6b, 0x12, 0x1c, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x54, 0x69, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x10, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x2e, 0x54, 0x69, 0x63,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x42, 0x09, 0x5a, 0x07, 0x2f, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_bridge_proto_rawDescOnce sync.Once
	file_bridge_proto_rawDescData = file_bridge_proto_rawDesc
)

func file_bridge_proto_rawDescGZIP() []byte {
	file_bridge_proto_rawDescOnce.Do(func() {
		file_bridge_proto_rawDescData = protoimpl.X.CompressGZIP(file_bridge_proto_rawDescData)
	})
	return file_bridge_proto_rawDescData
}

var file_bridge_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_bridge_proto_goTypes = []any{
	(*EmptyType)(nil),                   // 0: bridge.EmptyType
	(*TickType)(nil),                    // 1: bridge.TickType
	(*AccountPositionType)(nil),         // 2: bridge.AccountPositionType
	(*AccountOrdersType)(nil),           // 3: bridge.AccountOrdersType
	(*SymbolsType)(nil),                 // 4: bridge.SymbolsType
	(*TradeDealType)(nil),               // 5: bridge.TradeDealType
	(*GetSymbolTickRequest)(nil),        // 6: bridge.GetSymbolTickRequest
	(*TickStartRequest)(nil),            // 7: bridge.TickStartRequest
	(*CloseTradeRequest)(nil),           // 8: bridge.CloseTradeRequest
	(*GetTradeHistoryRequest)(nil),      // 9: bridge.GetTradeHistoryRequest
	(*GetTicksFromRequest)(nil),         // 10: bridge.GetTicksFromRequest
	(*GetTicksRangeRequest)(nil),        // 11: bridge.GetTicksRangeRequest
	(*ModifyTradeRequest)(nil),          // 12: bridge.ModifyTradeRequest
	(*PlaceTradeRequest)(nil),           // 13: bridge.PlaceTradeRequest
	(*ManageSymbolRequest)(nil),         // 14: bridge.ManageSymbolRequest
	(*GenericResponseType)(nil),         // 15: bridge.GenericResponseType
	(*GetAccountResponse)(nil),          // 16: bridge.GetAccountResponse
	(*GetPositionsResponse)(nil),        // 17: bridge.GetPositionsResponse
	(*GetOrdersResponse)(nil),           // 18: bridge.GetOrdersResponse
	(*GetAvailableSymbolsResponse)(nil), // 19: bridge.GetAvailableSymbolsResponse
	(*GetTradeHistoryResponse)(nil),     // 20: bridge.GetTradeHistoryResponse
	(*GetTicksResponse)(nil),            // 21: bridge.GetTicksResponse
	(*PlaceTradeResponse)(nil),          // 22: bridge.PlaceTradeResponse
	(*ManageSymbolResponse)(nil),        // 23: bridge.ManageSymbolResponse
}
var file_bridge_proto_depIdxs = []int32{
	2,  // 0: bridge.GetPositionsResponse.positions:type_name -> bridge.AccountPositionType
	3,  // 1: bridge.GetOrdersResponse.orders:type_name -> bridge.AccountOrdersType
	4,  // 2: bridge.GetAvailableSymbolsResponse.symbols:type_name -> bridge.SymbolsType
	5,  // 3: bridge.GetTradeHistoryResponse.deals:type_name -> bridge.TradeDealType
	1,  // 4: bridge.GetTicksResponse.ticks:type_name -> bridge.TickType
	4,  // 5: bridge.ManageSymbolResponse.symbol:type_name -> bridge.SymbolsType
	7,  // 6: bridge.BridgeRpcService.TickStart:input_type -> bridge.TickStartRequest
	0,  // 7: bridge.BridgeRpcService.TickStop:input_type -> bridge.EmptyType
	0,  // 8: bridge.BridgeRpcService.GetAccount:input_type -> bridge.EmptyType
	0,  // 9: bridge.BridgeRpcService.GetPositions:input_type -> bridge.EmptyType
	0,  // 10: bridge.BridgeRpcService.GetOrders:input_type -> bridge.EmptyType
	0,  // 11: bridge.BridgeRpcService.GetAvailableSymbols:input_type -> bridge.EmptyType
	9,  // 12: bridge.BridgeRpcService.GetTradeHistory:input_type -> bridge.GetTradeHistoryRequest
	10, // 13: bridge.BridgeRpcService.GetTicksFrom:input_type -> bridge.GetTicksFromRequest
	11, // 14: bridge.BridgeRpcService.GetTicksRange:input_type -> bridge.GetTicksRangeRequest
	8,  // 15: bridge.BridgeRpcService.CloseTrade:input_type -> bridge.CloseTradeRequest
	12, // 16: bridge.BridgeRpcService.ModifyTrade:input_type -> bridge.ModifyTradeRequest
	13, // 17: bridge.BridgeRpcService.PlaceTrade:input_type -> bridge.PlaceTradeRequest
	14, // 18: bridge.BridgeRpcService.ManageSymbol:input_type -> bridge.ManageSymbolRequest
	0,  // 19: bridge.BridgeRpcService.GetTerminalError:input_type -> bridge.EmptyType
	6,  // 20: bridge.BridgeRpcService.GetSymbolTick:input_type -> bridge.GetSymbolTickRequest
	1,  // 21: bridge.BridgeRpcService.TickStart:output_type -> bridge.TickType
	15, // 22: bridge.BridgeRpcService.TickStop:output_type -> bridge.GenericResponseType
	16, // 23: bridge.BridgeRpcService.GetAccount:output_type -> bridge.GetAccountResponse
	17, // 24: bridge.BridgeRpcService.GetPositions:output_type -> bridge.GetPositionsResponse
	18, // 25: bridge.BridgeRpcService.GetOrders:output_type -> bridge.GetOrdersResponse
	19, // 26: bridge.BridgeRpcService.GetAvailableSymbols:output_type -> bridge.GetAvailableSymbolsResponse
	20, // 27: bridge.BridgeRpcService.GetTradeHistory:output_type -> bridge.GetTradeHistoryResponse
	21, // 28: bridge.BridgeRpcService.GetTicksFrom:output_type -> bridge.GetTicksResponse
	21, // 29: bridge.BridgeRpcService.GetTicksRange:output_type -> bridge.GetTicksResponse
	15, // 30: bridge.BridgeRpcService.CloseTrade:output_type -> bridge.GenericResponseType
	15, // 31: bridge.BridgeRpcService.ModifyTrade:output_type -> bridge.GenericResponseType
	22, // 32: bridge.BridgeRpcService.PlaceTrade:output_type -> bridge.PlaceTradeResponse
	23, // 33: bridge.BridgeRpcService.ManageSymbol:output_type -> bridge.ManageSymbolResponse
	15, // 34: bridge.BridgeRpcService.GetTerminalError:output_type -> bridge.GenericResponseType
	1,  // 35: bridge.BridgeRpcService.GetSymbolTick:output_type -> bridge.TickType
	21, // [21:36] is the sub-list for method output_type
	6,  // [6:21] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_bridge_proto_init() }
func file_bridge_proto_init() {
	if File_bridge_proto != nil {
		return
	}
	file_bridge_proto_msgTypes[8].OneofWrappers = []any{}
	file_bridge_proto_msgTypes[12].OneofWrappers = []any{}
	file_bridge_proto_msgTypes[13].OneofWrappers = []any{}
	file_bridge_proto_msgTypes[22].OneofWrappers = []any{}
	file_bridge_proto_msgTypes[23].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bridge_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_bridge_proto_goTypes,
		DependencyIndexes: file_bridge_proto_depIdxs,
		MessageInfos:      file_bridge_proto_msgTypes,
	}.Build()
	File_bridge_proto = out.File
	file_bridge_proto_rawDesc = nil
	file_bridge_proto_goTypes = nil
	file_bridge_proto_depIdxs = nil
}
