package main

import (
	. "bridge"
	"log"
)

var config Map = Map{}

type Config struct {
	Symbol         string
	Remote_ticking bool
	Rate           float64
}

func main() {

	is_ticking := false

	var fn Callback = func(o Options, tick *TickType) {

		defer func() {
			if r := recover(); r != nil {
				log.Printf("Recovered from panic in callback: %v", r)
			}
		}()

		c := Config{}
		MapToStruct(config, &c)

		if is_ticking == false {
			is_ticking = true
		}

	}

	Initialize(&config, fn)
}
