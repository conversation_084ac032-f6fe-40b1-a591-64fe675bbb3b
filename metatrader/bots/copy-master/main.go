package main

import (
	. "bridge"
	"encoding/json"
	"fmt"
	"log"
	. "shared"
	"slices"

	"github.com/IBM/sarama"
)

var config Map = Map{}

type Config struct {
	Symbol         string
	Remote_ticking bool
	Rate           float64
	Kafka_uri      string
	Slaves         []interface{}
}

func main() {

	is_ticking := false
	old_positions := []int64{}

	watched_positions := []*AccountPositionType{}
	watched_positions_map := map[int]*AccountPositionType{}

	var producer sarama.SyncProducer

	var fn Callback = func(o Options, tick *TickType) {

		defer func() {
			if r := recover(); r != nil {
				log.Printf("Recovered from panic in callback: %v", r)
			}
		}()

		c := Config{}
		MapToStruct(config, &c)

		if is_ticking == false {
			is_ticking = true
			p, err := sarama.NewSyncProducer([]string{c.Kafka_uri}, nil)
			if err != nil {
				log.Println("Failed to start Sarama producer:", err)
			}
			producer = p

			// detect trade history changes
			trades, err := o.Client.GetPositions(o.Ctx, &EmptyType{})
			if err != nil {
				log.Println("Failed to get trade history:", err)
				return
			}

			for _, pos := range trades.Positions {
				old_positions = append(old_positions, pos.Ticket)
			}
		}

		Use(producer)

		trades, err := o.Client.GetPositions(o.Ctx, &EmptyType{})
		if err != nil {
			log.Println("Failed to get current positions:", err)
			return
		}

		// detect closed positions and modified positions
		for index, pos := range watched_positions {
			close_position_index := slices.IndexFunc(trades.Positions, func(c *AccountPositionType) bool { return c.Ticket == pos.Ticket })
			if close_position_index == -1 {
				mapped_position := watched_positions_map[int(pos.Ticket)]
				magic := mapped_position.Magic

				// delete from watched positions
				watched_positions = slices.DeleteFunc(watched_positions, func(c *AccountPositionType) bool { return c.Ticket == pos.Ticket })
				delete(watched_positions_map, int(pos.Ticket))

				// broadcast closed position
				payload := pos
				payload.Magic = int64(magic)
				json_payload, _ := json.Marshal(TradeCopyMessage{Position: payload, Message_type: CLOSE})

				// broadcast closed position
				for _, slave := range c.Slaves {
					msg := &sarama.ProducerMessage{
						Topic: slave.(string),
						Value: sarama.StringEncoder(json_payload),
					}

					partition, offset, err := producer.SendMessage(msg)
					if err != nil {
						log.Println("Failed to send message:", err)
					}
					log.Printf("Message is stored in topic(%s)/partition(%d)/offset(%d)\n", slave.(string), partition, offset)
				}
			}

			modified_position_index := slices.IndexFunc(trades.Positions, func(c *AccountPositionType) bool {

				change_detected := false
				if c.Ticket == pos.Ticket {
					// market orders
					change_detected = c.TakeProfit != pos.TakeProfit || c.StopLoss != pos.StopLoss
				}
				return change_detected
			})

			if modified_position_index != -1 {

				mapped_position := watched_positions_map[int(pos.Ticket)]
				magic := mapped_position.Magic

				// modify existing position
				watched_positions[index] = trades.Positions[modified_position_index]
				watched_positions_map[int(pos.Ticket)] = trades.Positions[modified_position_index]

				// broadcast modified position
				payload := trades.Positions[modified_position_index]
				payload.Magic = int64(magic)
				json_payload, _ := json.Marshal(TradeCopyMessage{Position: payload, Message_type: MODIFY})

				// broadcast closed position
				for _, slave := range c.Slaves {
					msg := &sarama.ProducerMessage{
						Topic: slave.(string),
						Value: sarama.StringEncoder(json_payload),
					}

					partition, offset, err := producer.SendMessage(msg)
					if err != nil {
						log.Println("Failed to send message:", err)
					}
					log.Printf("Message is stored in topic(%s)/partition(%d)/offset(%d)\n", slave.(string), partition, offset)
				}
			}
		}

		// detect new positions
		for _, pos := range trades.Positions {
			in_old_positions := slices.Contains(old_positions, pos.Ticket)
			watched_position_index := slices.IndexFunc(watched_positions, func(c *AccountPositionType) bool { return c.Ticket == pos.Ticket })

			if in_old_positions == false && watched_position_index == -1 {

				// append to watched positions
				watched_positions = append(watched_positions, pos)
				fmt.Println("New position:", pos)

				// broadcast new position
				magic := GenRandomMagic()
				payload := pos
				payload.Magic = int64(magic)
				json_payload, _ := json.Marshal(TradeCopyMessage{Position: payload, Message_type: NEW})

				// append to watched positions map
				watched_positions_map[int(pos.Ticket)] = payload

				// broadcast new position
				for _, slave := range c.Slaves {
					msg := &sarama.ProducerMessage{
						Topic: slave.(string),
						Value: sarama.StringEncoder(json_payload),
					}

					partition, offset, err := producer.SendMessage(msg)
					if err != nil {
						log.Println("Failed to send message:", err)
					}
					log.Printf("Message is stored in topic(%s)/partition(%d)/offset(%d)\n", slave.(string), partition, offset)
				}
			}
		}

	}

	Initialize(&config, fn)

}
