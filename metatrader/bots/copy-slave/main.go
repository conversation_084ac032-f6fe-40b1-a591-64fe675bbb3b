package main

import (
	. "bridge"
	"encoding/json"
	"log"
	. "shared"
	"slices"

	"github.com/IBM/sarama"
)

var config Map = Map{}

type Config struct {
	Symbol         string
	Remote_ticking bool
	Rate           float64
	Kafka_uri      string
}

func main() {
	is_ticking := false

	var fn Callback = func(o Options, tick *TickType) {

		c := Config{}
		MapToStruct(config, &c)

		if is_ticking == false {
			is_ticking = true
			go func() {

				consumer, err := sarama.NewConsumer([]string{c.Kafka_uri}, nil)
				if err != nil {
					log.Println("Failed to start Sarama consumer:", err)
				}
				defer consumer.Close()
				partitionConsumer, err := consumer.ConsumePartition(o.Id, 0, sarama.OffsetNewest)
				if err != nil {
					log.Println("Failed to start partition consumer:", err)
				}
				defer partitionConsumer.Close()
				for {
					select {
					case msg := <-partitionConsumer.Messages():
						log.Printf("Consumed message: %s at offset %d\n", msg.Value, msg.Offset)

						message := TradeCopyMessage{}

						if err := json.Unmarshal(msg.Value, &message); err != nil {
							log.Println("json unmarshal error:", err)
						}

						position := message.Position

						switch message.Message_type {
						case NEW:
							o.Client.PlaceTrade(o.Ctx, &PlaceTradeRequest{
								Symbol:     position.Symbol,
								ActionType: position.Type,
								Volume:     position.Volume,
								StopLoss:   &position.StopLoss,
								TakeProfit: &position.TakeProfit,
								Magic:      &position.Magic,
							})
						case MODIFY:
							trades, err := o.Client.GetPositions(o.Ctx, &EmptyType{})
							if err != nil {
								log.Println("Failed to get current positions:", err)
								return
							}
							position_index := slices.IndexFunc(trades.Positions, func(c *AccountPositionType) bool { return c.Magic == position.Magic })
							if position_index == -1 {
								log.Println("Trade not found")
								return
							} else {
								client_position := trades.Positions[position_index]
								o.Client.ModifyTrade(o.Ctx, &ModifyTradeRequest{
									Ticket:     client_position.Ticket,
									StopLoss:   position.StopLoss,
									TakeProfit: position.TakeProfit,
								})
							}
						case CLOSE:
							trades, err := o.Client.GetPositions(o.Ctx, &EmptyType{})
							if err != nil {
								log.Println("Failed to get current positions:", err)
								return
							}
							position_index := slices.IndexFunc(trades.Positions, func(c *AccountPositionType) bool { return c.Magic == position.Magic })
							if position_index == -1 {
								log.Println("Trade not found")
								return
							} else {
								client_position := trades.Positions[position_index]
								o.Client.CloseTrade(o.Ctx, &CloseTradeRequest{
									Ticket: client_position.Ticket,
									Volume: &client_position.Volume,
								})
							}
						}

					case err := <-partitionConsumer.Errors():
						log.Printf("Error: %s\n", err.Error())
					}
				}
			}()
		}

	}

	Initialize(&config, fn)

}
