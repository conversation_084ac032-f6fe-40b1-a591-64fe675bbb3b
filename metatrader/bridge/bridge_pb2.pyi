from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class EmptyType(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class TickType(_message.Message):
    __slots__ = ("ask", "bid", "time")
    ASK_FIELD_NUMBER: _ClassVar[int]
    BID_FIELD_NUMBER: _ClassVar[int]
    TIME_FIELD_NUMBER: _ClassVar[int]
    ask: float
    bid: float
    time: int
    def __init__(self, ask: _Optional[float] = ..., bid: _Optional[float] = ..., time: _Optional[int] = ...) -> None: ...

class AccountPositionType(_message.Message):
    __slots__ = ("ticket", "symbol", "type", "volume", "open_price", "current_price", "stop_loss", "take_profit", "profit", "comment", "magic")
    TICKET_FIELD_NUMBER: _ClassVar[int]
    SYMBOL_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    VOLUME_FIELD_NUMBER: _ClassVar[int]
    OPEN_PRICE_FIELD_NUMBER: _ClassVar[int]
    CURRENT_PRICE_FIELD_NUMBER: _ClassVar[int]
    STOP_LOSS_FIELD_NUMBER: _ClassVar[int]
    TAKE_PROFIT_FIELD_NUMBER: _ClassVar[int]
    PROFIT_FIELD_NUMBER: _ClassVar[int]
    COMMENT_FIELD_NUMBER: _ClassVar[int]
    MAGIC_FIELD_NUMBER: _ClassVar[int]
    ticket: int
    symbol: str
    type: int
    volume: float
    open_price: float
    current_price: float
    stop_loss: float
    take_profit: float
    profit: float
    comment: str
    magic: int
    def __init__(self, ticket: _Optional[int] = ..., symbol: _Optional[str] = ..., type: _Optional[int] = ..., volume: _Optional[float] = ..., open_price: _Optional[float] = ..., current_price: _Optional[float] = ..., stop_loss: _Optional[float] = ..., take_profit: _Optional[float] = ..., profit: _Optional[float] = ..., comment: _Optional[str] = ..., magic: _Optional[int] = ...) -> None: ...

class AccountOrdersType(_message.Message):
    __slots__ = ("ticket", "symbol", "type", "volume", "price_open", "price_current", "stop_loss", "take_profit", "comment", "time_setup", "time_expiration", "magic")
    TICKET_FIELD_NUMBER: _ClassVar[int]
    SYMBOL_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    VOLUME_FIELD_NUMBER: _ClassVar[int]
    PRICE_OPEN_FIELD_NUMBER: _ClassVar[int]
    PRICE_CURRENT_FIELD_NUMBER: _ClassVar[int]
    STOP_LOSS_FIELD_NUMBER: _ClassVar[int]
    TAKE_PROFIT_FIELD_NUMBER: _ClassVar[int]
    COMMENT_FIELD_NUMBER: _ClassVar[int]
    TIME_SETUP_FIELD_NUMBER: _ClassVar[int]
    TIME_EXPIRATION_FIELD_NUMBER: _ClassVar[int]
    MAGIC_FIELD_NUMBER: _ClassVar[int]
    ticket: int
    symbol: str
    type: int
    volume: float
    price_open: float
    price_current: float
    stop_loss: float
    take_profit: float
    comment: str
    time_setup: int
    time_expiration: int
    magic: int
    def __init__(self, ticket: _Optional[int] = ..., symbol: _Optional[str] = ..., type: _Optional[int] = ..., volume: _Optional[float] = ..., price_open: _Optional[float] = ..., price_current: _Optional[float] = ..., stop_loss: _Optional[float] = ..., take_profit: _Optional[float] = ..., comment: _Optional[str] = ..., time_setup: _Optional[int] = ..., time_expiration: _Optional[int] = ..., magic: _Optional[int] = ...) -> None: ...

class SymbolsType(_message.Message):
    __slots__ = ("name", "description", "path", "digits", "spread", "time")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    PATH_FIELD_NUMBER: _ClassVar[int]
    DIGITS_FIELD_NUMBER: _ClassVar[int]
    SPREAD_FIELD_NUMBER: _ClassVar[int]
    TIME_FIELD_NUMBER: _ClassVar[int]
    name: str
    description: str
    path: str
    digits: int
    spread: float
    time: int
    def __init__(self, name: _Optional[str] = ..., description: _Optional[str] = ..., path: _Optional[str] = ..., digits: _Optional[int] = ..., spread: _Optional[float] = ..., time: _Optional[int] = ...) -> None: ...

class TradeDealType(_message.Message):
    __slots__ = ("ticket", "order", "time", "type", "entry", "symbol", "volume", "price", "commission", "swap", "profit", "magic", "comment")
    TICKET_FIELD_NUMBER: _ClassVar[int]
    ORDER_FIELD_NUMBER: _ClassVar[int]
    TIME_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    ENTRY_FIELD_NUMBER: _ClassVar[int]
    SYMBOL_FIELD_NUMBER: _ClassVar[int]
    VOLUME_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    COMMISSION_FIELD_NUMBER: _ClassVar[int]
    SWAP_FIELD_NUMBER: _ClassVar[int]
    PROFIT_FIELD_NUMBER: _ClassVar[int]
    MAGIC_FIELD_NUMBER: _ClassVar[int]
    COMMENT_FIELD_NUMBER: _ClassVar[int]
    ticket: int
    order: int
    time: int
    type: int
    entry: float
    symbol: str
    volume: float
    price: float
    commission: float
    swap: float
    profit: float
    magic: int
    comment: str
    def __init__(self, ticket: _Optional[int] = ..., order: _Optional[int] = ..., time: _Optional[int] = ..., type: _Optional[int] = ..., entry: _Optional[float] = ..., symbol: _Optional[str] = ..., volume: _Optional[float] = ..., price: _Optional[float] = ..., commission: _Optional[float] = ..., swap: _Optional[float] = ..., profit: _Optional[float] = ..., magic: _Optional[int] = ..., comment: _Optional[str] = ...) -> None: ...

class GetSymbolTickRequest(_message.Message):
    __slots__ = ("symbol",)
    SYMBOL_FIELD_NUMBER: _ClassVar[int]
    symbol: str
    def __init__(self, symbol: _Optional[str] = ...) -> None: ...

class TickStartRequest(_message.Message):
    __slots__ = ("symbol", "rate")
    SYMBOL_FIELD_NUMBER: _ClassVar[int]
    RATE_FIELD_NUMBER: _ClassVar[int]
    symbol: str
    rate: float
    def __init__(self, symbol: _Optional[str] = ..., rate: _Optional[float] = ...) -> None: ...

class CloseTradeRequest(_message.Message):
    __slots__ = ("ticket", "volume", "price", "slippage")
    TICKET_FIELD_NUMBER: _ClassVar[int]
    VOLUME_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    SLIPPAGE_FIELD_NUMBER: _ClassVar[int]
    ticket: int
    volume: float
    price: float
    slippage: float
    def __init__(self, ticket: _Optional[int] = ..., volume: _Optional[float] = ..., price: _Optional[float] = ..., slippage: _Optional[float] = ...) -> None: ...

class GetTradeHistoryRequest(_message.Message):
    __slots__ = ("start_date", "end_date")
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    start_date: int
    end_date: int
    def __init__(self, start_date: _Optional[int] = ..., end_date: _Optional[int] = ...) -> None: ...

class GetTicksFromRequest(_message.Message):
    __slots__ = ("symbol", "start_date", "length")
    SYMBOL_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    LENGTH_FIELD_NUMBER: _ClassVar[int]
    symbol: str
    start_date: int
    length: int
    def __init__(self, symbol: _Optional[str] = ..., start_date: _Optional[int] = ..., length: _Optional[int] = ...) -> None: ...

class GetTicksRangeRequest(_message.Message):
    __slots__ = ("symbol", "start_date", "end_date", "length")
    SYMBOL_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    LENGTH_FIELD_NUMBER: _ClassVar[int]
    symbol: str
    start_date: int
    end_date: int
    length: int
    def __init__(self, symbol: _Optional[str] = ..., start_date: _Optional[int] = ..., end_date: _Optional[int] = ..., length: _Optional[int] = ...) -> None: ...

class ModifyTradeRequest(_message.Message):
    __slots__ = ("ticket", "stop_loss", "take_profit", "price", "expiration")
    TICKET_FIELD_NUMBER: _ClassVar[int]
    STOP_LOSS_FIELD_NUMBER: _ClassVar[int]
    TAKE_PROFIT_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    EXPIRATION_FIELD_NUMBER: _ClassVar[int]
    ticket: int
    stop_loss: float
    take_profit: float
    price: float
    expiration: int
    def __init__(self, ticket: _Optional[int] = ..., stop_loss: _Optional[float] = ..., take_profit: _Optional[float] = ..., price: _Optional[float] = ..., expiration: _Optional[int] = ...) -> None: ...

class PlaceTradeRequest(_message.Message):
    __slots__ = ("symbol", "action_type", "volume", "stop_loss", "take_profit", "comment", "price", "order", "magic", "stop_limit", "expiration", "position", "position_by", "deviation")
    SYMBOL_FIELD_NUMBER: _ClassVar[int]
    ACTION_TYPE_FIELD_NUMBER: _ClassVar[int]
    VOLUME_FIELD_NUMBER: _ClassVar[int]
    STOP_LOSS_FIELD_NUMBER: _ClassVar[int]
    TAKE_PROFIT_FIELD_NUMBER: _ClassVar[int]
    COMMENT_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    ORDER_FIELD_NUMBER: _ClassVar[int]
    MAGIC_FIELD_NUMBER: _ClassVar[int]
    STOP_LIMIT_FIELD_NUMBER: _ClassVar[int]
    EXPIRATION_FIELD_NUMBER: _ClassVar[int]
    POSITION_FIELD_NUMBER: _ClassVar[int]
    POSITION_BY_FIELD_NUMBER: _ClassVar[int]
    DEVIATION_FIELD_NUMBER: _ClassVar[int]
    symbol: str
    action_type: int
    volume: float
    stop_loss: float
    take_profit: float
    comment: str
    price: float
    order: int
    magic: int
    stop_limit: float
    expiration: int
    position: int
    position_by: int
    deviation: int
    def __init__(self, symbol: _Optional[str] = ..., action_type: _Optional[int] = ..., volume: _Optional[float] = ..., stop_loss: _Optional[float] = ..., take_profit: _Optional[float] = ..., comment: _Optional[str] = ..., price: _Optional[float] = ..., order: _Optional[int] = ..., magic: _Optional[int] = ..., stop_limit: _Optional[float] = ..., expiration: _Optional[int] = ..., position: _Optional[int] = ..., position_by: _Optional[int] = ..., deviation: _Optional[int] = ...) -> None: ...

class ManageSymbolRequest(_message.Message):
    __slots__ = ("symbol", "action")
    SYMBOL_FIELD_NUMBER: _ClassVar[int]
    ACTION_FIELD_NUMBER: _ClassVar[int]
    symbol: str
    action: str
    def __init__(self, symbol: _Optional[str] = ..., action: _Optional[str] = ...) -> None: ...

class GenericResponseType(_message.Message):
    __slots__ = ("status", "message")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    MESSAGE_FIELD_NUMBER: _ClassVar[int]
    status: int
    message: str
    def __init__(self, status: _Optional[int] = ..., message: _Optional[str] = ...) -> None: ...

class GetAccountResponse(_message.Message):
    __slots__ = ("balance", "equity", "margin", "free_margin", "margin_level", "profit", "server", "trade_mode")
    BALANCE_FIELD_NUMBER: _ClassVar[int]
    EQUITY_FIELD_NUMBER: _ClassVar[int]
    MARGIN_FIELD_NUMBER: _ClassVar[int]
    FREE_MARGIN_FIELD_NUMBER: _ClassVar[int]
    MARGIN_LEVEL_FIELD_NUMBER: _ClassVar[int]
    PROFIT_FIELD_NUMBER: _ClassVar[int]
    SERVER_FIELD_NUMBER: _ClassVar[int]
    TRADE_MODE_FIELD_NUMBER: _ClassVar[int]
    balance: float
    equity: float
    margin: float
    free_margin: float
    margin_level: float
    profit: float
    server: str
    trade_mode: int
    def __init__(self, balance: _Optional[float] = ..., equity: _Optional[float] = ..., margin: _Optional[float] = ..., free_margin: _Optional[float] = ..., margin_level: _Optional[float] = ..., profit: _Optional[float] = ..., server: _Optional[str] = ..., trade_mode: _Optional[int] = ...) -> None: ...

class GetPositionsResponse(_message.Message):
    __slots__ = ("positions", "total_positions")
    POSITIONS_FIELD_NUMBER: _ClassVar[int]
    TOTAL_POSITIONS_FIELD_NUMBER: _ClassVar[int]
    positions: _containers.RepeatedCompositeFieldContainer[AccountPositionType]
    total_positions: int
    def __init__(self, positions: _Optional[_Iterable[_Union[AccountPositionType, _Mapping]]] = ..., total_positions: _Optional[int] = ...) -> None: ...

class GetOrdersResponse(_message.Message):
    __slots__ = ("orders", "total_orders")
    ORDERS_FIELD_NUMBER: _ClassVar[int]
    TOTAL_ORDERS_FIELD_NUMBER: _ClassVar[int]
    orders: _containers.RepeatedCompositeFieldContainer[AccountOrdersType]
    total_orders: int
    def __init__(self, orders: _Optional[_Iterable[_Union[AccountOrdersType, _Mapping]]] = ..., total_orders: _Optional[int] = ...) -> None: ...

class GetAvailableSymbolsResponse(_message.Message):
    __slots__ = ("symbols", "total_symbols")
    SYMBOLS_FIELD_NUMBER: _ClassVar[int]
    TOTAL_SYMBOLS_FIELD_NUMBER: _ClassVar[int]
    symbols: _containers.RepeatedCompositeFieldContainer[SymbolsType]
    total_symbols: int
    def __init__(self, symbols: _Optional[_Iterable[_Union[SymbolsType, _Mapping]]] = ..., total_symbols: _Optional[int] = ...) -> None: ...

class GetTradeHistoryResponse(_message.Message):
    __slots__ = ("deals", "total_deals")
    DEALS_FIELD_NUMBER: _ClassVar[int]
    TOTAL_DEALS_FIELD_NUMBER: _ClassVar[int]
    deals: _containers.RepeatedCompositeFieldContainer[TradeDealType]
    total_deals: int
    def __init__(self, deals: _Optional[_Iterable[_Union[TradeDealType, _Mapping]]] = ..., total_deals: _Optional[int] = ...) -> None: ...

class GetTicksResponse(_message.Message):
    __slots__ = ("ticks", "total_ticks")
    TICKS_FIELD_NUMBER: _ClassVar[int]
    TOTAL_TICKS_FIELD_NUMBER: _ClassVar[int]
    ticks: _containers.RepeatedCompositeFieldContainer[TickType]
    total_ticks: int
    def __init__(self, ticks: _Optional[_Iterable[_Union[TickType, _Mapping]]] = ..., total_ticks: _Optional[int] = ...) -> None: ...

class PlaceTradeResponse(_message.Message):
    __slots__ = ("status", "message", "ticket")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    MESSAGE_FIELD_NUMBER: _ClassVar[int]
    TICKET_FIELD_NUMBER: _ClassVar[int]
    status: int
    message: str
    ticket: str
    def __init__(self, status: _Optional[int] = ..., message: _Optional[str] = ..., ticket: _Optional[str] = ...) -> None: ...

class ManageSymbolResponse(_message.Message):
    __slots__ = ("status", "message", "symbol")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    MESSAGE_FIELD_NUMBER: _ClassVar[int]
    SYMBOL_FIELD_NUMBER: _ClassVar[int]
    status: int
    message: str
    symbol: SymbolsType
    def __init__(self, status: _Optional[int] = ..., message: _Optional[str] = ..., symbol: _Optional[_Union[SymbolsType, _Mapping]] = ...) -> None: ...
