# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

import bridge_pb2 as bridge__pb2

GRPC_GENERATED_VERSION = '1.69.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in bridge_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class BridgeRpcServiceStub(object):
    """service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.TickStart = channel.unary_stream(
                '/bridge.BridgeRpcService/TickStart',
                request_serializer=bridge__pb2.TickStartRequest.SerializeToString,
                response_deserializer=bridge__pb2.TickType.FromString,
                _registered_method=True)
        self.TickStop = channel.unary_unary(
                '/bridge.BridgeRpcService/TickStop',
                request_serializer=bridge__pb2.EmptyType.SerializeToString,
                response_deserializer=bridge__pb2.GenericResponseType.FromString,
                _registered_method=True)
        self.GetAccount = channel.unary_unary(
                '/bridge.BridgeRpcService/GetAccount',
                request_serializer=bridge__pb2.EmptyType.SerializeToString,
                response_deserializer=bridge__pb2.GetAccountResponse.FromString,
                _registered_method=True)
        self.GetPositions = channel.unary_unary(
                '/bridge.BridgeRpcService/GetPositions',
                request_serializer=bridge__pb2.EmptyType.SerializeToString,
                response_deserializer=bridge__pb2.GetPositionsResponse.FromString,
                _registered_method=True)
        self.GetOrders = channel.unary_unary(
                '/bridge.BridgeRpcService/GetOrders',
                request_serializer=bridge__pb2.EmptyType.SerializeToString,
                response_deserializer=bridge__pb2.GetOrdersResponse.FromString,
                _registered_method=True)
        self.GetAvailableSymbols = channel.unary_unary(
                '/bridge.BridgeRpcService/GetAvailableSymbols',
                request_serializer=bridge__pb2.EmptyType.SerializeToString,
                response_deserializer=bridge__pb2.GetAvailableSymbolsResponse.FromString,
                _registered_method=True)
        self.GetTradeHistory = channel.unary_unary(
                '/bridge.BridgeRpcService/GetTradeHistory',
                request_serializer=bridge__pb2.GetTradeHistoryRequest.SerializeToString,
                response_deserializer=bridge__pb2.GetTradeHistoryResponse.FromString,
                _registered_method=True)
        self.GetTicksFrom = channel.unary_unary(
                '/bridge.BridgeRpcService/GetTicksFrom',
                request_serializer=bridge__pb2.GetTicksFromRequest.SerializeToString,
                response_deserializer=bridge__pb2.GetTicksResponse.FromString,
                _registered_method=True)
        self.GetTicksRange = channel.unary_unary(
                '/bridge.BridgeRpcService/GetTicksRange',
                request_serializer=bridge__pb2.GetTicksRangeRequest.SerializeToString,
                response_deserializer=bridge__pb2.GetTicksResponse.FromString,
                _registered_method=True)
        self.CloseTrade = channel.unary_unary(
                '/bridge.BridgeRpcService/CloseTrade',
                request_serializer=bridge__pb2.CloseTradeRequest.SerializeToString,
                response_deserializer=bridge__pb2.GenericResponseType.FromString,
                _registered_method=True)
        self.ModifyTrade = channel.unary_unary(
                '/bridge.BridgeRpcService/ModifyTrade',
                request_serializer=bridge__pb2.ModifyTradeRequest.SerializeToString,
                response_deserializer=bridge__pb2.GenericResponseType.FromString,
                _registered_method=True)
        self.PlaceTrade = channel.unary_unary(
                '/bridge.BridgeRpcService/PlaceTrade',
                request_serializer=bridge__pb2.PlaceTradeRequest.SerializeToString,
                response_deserializer=bridge__pb2.PlaceTradeResponse.FromString,
                _registered_method=True)
        self.ManageSymbol = channel.unary_unary(
                '/bridge.BridgeRpcService/ManageSymbol',
                request_serializer=bridge__pb2.ManageSymbolRequest.SerializeToString,
                response_deserializer=bridge__pb2.ManageSymbolResponse.FromString,
                _registered_method=True)
        self.GetTerminalError = channel.unary_unary(
                '/bridge.BridgeRpcService/GetTerminalError',
                request_serializer=bridge__pb2.EmptyType.SerializeToString,
                response_deserializer=bridge__pb2.GenericResponseType.FromString,
                _registered_method=True)
        self.GetSymbolTick = channel.unary_unary(
                '/bridge.BridgeRpcService/GetSymbolTick',
                request_serializer=bridge__pb2.GetSymbolTickRequest.SerializeToString,
                response_deserializer=bridge__pb2.TickType.FromString,
                _registered_method=True)


class BridgeRpcServiceServicer(object):
    """service
    """

    def TickStart(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TickStop(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAccount(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPositions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetOrders(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAvailableSymbols(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTradeHistory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTicksFrom(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTicksRange(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CloseTrade(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ModifyTrade(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PlaceTrade(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ManageSymbol(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTerminalError(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSymbolTick(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_BridgeRpcServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'TickStart': grpc.unary_stream_rpc_method_handler(
                    servicer.TickStart,
                    request_deserializer=bridge__pb2.TickStartRequest.FromString,
                    response_serializer=bridge__pb2.TickType.SerializeToString,
            ),
            'TickStop': grpc.unary_unary_rpc_method_handler(
                    servicer.TickStop,
                    request_deserializer=bridge__pb2.EmptyType.FromString,
                    response_serializer=bridge__pb2.GenericResponseType.SerializeToString,
            ),
            'GetAccount': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAccount,
                    request_deserializer=bridge__pb2.EmptyType.FromString,
                    response_serializer=bridge__pb2.GetAccountResponse.SerializeToString,
            ),
            'GetPositions': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPositions,
                    request_deserializer=bridge__pb2.EmptyType.FromString,
                    response_serializer=bridge__pb2.GetPositionsResponse.SerializeToString,
            ),
            'GetOrders': grpc.unary_unary_rpc_method_handler(
                    servicer.GetOrders,
                    request_deserializer=bridge__pb2.EmptyType.FromString,
                    response_serializer=bridge__pb2.GetOrdersResponse.SerializeToString,
            ),
            'GetAvailableSymbols': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAvailableSymbols,
                    request_deserializer=bridge__pb2.EmptyType.FromString,
                    response_serializer=bridge__pb2.GetAvailableSymbolsResponse.SerializeToString,
            ),
            'GetTradeHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTradeHistory,
                    request_deserializer=bridge__pb2.GetTradeHistoryRequest.FromString,
                    response_serializer=bridge__pb2.GetTradeHistoryResponse.SerializeToString,
            ),
            'GetTicksFrom': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTicksFrom,
                    request_deserializer=bridge__pb2.GetTicksFromRequest.FromString,
                    response_serializer=bridge__pb2.GetTicksResponse.SerializeToString,
            ),
            'GetTicksRange': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTicksRange,
                    request_deserializer=bridge__pb2.GetTicksRangeRequest.FromString,
                    response_serializer=bridge__pb2.GetTicksResponse.SerializeToString,
            ),
            'CloseTrade': grpc.unary_unary_rpc_method_handler(
                    servicer.CloseTrade,
                    request_deserializer=bridge__pb2.CloseTradeRequest.FromString,
                    response_serializer=bridge__pb2.GenericResponseType.SerializeToString,
            ),
            'ModifyTrade': grpc.unary_unary_rpc_method_handler(
                    servicer.ModifyTrade,
                    request_deserializer=bridge__pb2.ModifyTradeRequest.FromString,
                    response_serializer=bridge__pb2.GenericResponseType.SerializeToString,
            ),
            'PlaceTrade': grpc.unary_unary_rpc_method_handler(
                    servicer.PlaceTrade,
                    request_deserializer=bridge__pb2.PlaceTradeRequest.FromString,
                    response_serializer=bridge__pb2.PlaceTradeResponse.SerializeToString,
            ),
            'ManageSymbol': grpc.unary_unary_rpc_method_handler(
                    servicer.ManageSymbol,
                    request_deserializer=bridge__pb2.ManageSymbolRequest.FromString,
                    response_serializer=bridge__pb2.ManageSymbolResponse.SerializeToString,
            ),
            'GetTerminalError': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTerminalError,
                    request_deserializer=bridge__pb2.EmptyType.FromString,
                    response_serializer=bridge__pb2.GenericResponseType.SerializeToString,
            ),
            'GetSymbolTick': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSymbolTick,
                    request_deserializer=bridge__pb2.GetSymbolTickRequest.FromString,
                    response_serializer=bridge__pb2.TickType.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'bridge.BridgeRpcService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('bridge.BridgeRpcService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class BridgeRpcService(object):
    """service
    """

    @staticmethod
    def TickStart(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/bridge.BridgeRpcService/TickStart',
            bridge__pb2.TickStartRequest.SerializeToString,
            bridge__pb2.TickType.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TickStop(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bridge.BridgeRpcService/TickStop',
            bridge__pb2.EmptyType.SerializeToString,
            bridge__pb2.GenericResponseType.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAccount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bridge.BridgeRpcService/GetAccount',
            bridge__pb2.EmptyType.SerializeToString,
            bridge__pb2.GetAccountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPositions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bridge.BridgeRpcService/GetPositions',
            bridge__pb2.EmptyType.SerializeToString,
            bridge__pb2.GetPositionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetOrders(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bridge.BridgeRpcService/GetOrders',
            bridge__pb2.EmptyType.SerializeToString,
            bridge__pb2.GetOrdersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAvailableSymbols(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bridge.BridgeRpcService/GetAvailableSymbols',
            bridge__pb2.EmptyType.SerializeToString,
            bridge__pb2.GetAvailableSymbolsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTradeHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bridge.BridgeRpcService/GetTradeHistory',
            bridge__pb2.GetTradeHistoryRequest.SerializeToString,
            bridge__pb2.GetTradeHistoryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTicksFrom(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bridge.BridgeRpcService/GetTicksFrom',
            bridge__pb2.GetTicksFromRequest.SerializeToString,
            bridge__pb2.GetTicksResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTicksRange(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bridge.BridgeRpcService/GetTicksRange',
            bridge__pb2.GetTicksRangeRequest.SerializeToString,
            bridge__pb2.GetTicksResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CloseTrade(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bridge.BridgeRpcService/CloseTrade',
            bridge__pb2.CloseTradeRequest.SerializeToString,
            bridge__pb2.GenericResponseType.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ModifyTrade(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bridge.BridgeRpcService/ModifyTrade',
            bridge__pb2.ModifyTradeRequest.SerializeToString,
            bridge__pb2.GenericResponseType.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PlaceTrade(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bridge.BridgeRpcService/PlaceTrade',
            bridge__pb2.PlaceTradeRequest.SerializeToString,
            bridge__pb2.PlaceTradeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ManageSymbol(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bridge.BridgeRpcService/ManageSymbol',
            bridge__pb2.ManageSymbolRequest.SerializeToString,
            bridge__pb2.ManageSymbolResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTerminalError(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bridge.BridgeRpcService/GetTerminalError',
            bridge__pb2.EmptyType.SerializeToString,
            bridge__pb2.GenericResponseType.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSymbolTick(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bridge.BridgeRpcService/GetSymbolTick',
            bridge__pb2.GetSymbolTickRequest.SerializeToString,
            bridge__pb2.TickType.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
