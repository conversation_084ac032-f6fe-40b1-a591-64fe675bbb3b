import os
import MetaTrader5 as mt5  # type: ignore
import requests  # type: ignore
import time
from functools import wraps

import argparse
import asyncio

import rpc_service
from mt4_bridge import Metatrader4 as mt4

from aiohttp import web


# Create parser
parser = argparse.ArgumentParser(description="Example script for passing arguments.")

# Add arguments
parser.add_argument("--server_port", type=int, help="Sever port", default="Unknown")
parser.add_argument("--terminal_id", type=str, help="Terminal id", default="Unknown")
parser.add_argument("--login", type=int, help="Your account login", default="Unknown")
parser.add_argument("--server", type=str, help="Your account server", default="Unknown")
parser.add_argument(
    "--password", type=str, help="Your account password", default="Unknown"
)
parser.add_argument(
    "--login_callback", type=str, help="Callback url on login", default="Unknown"
)
parser.add_argument(
    "--terminate_callback",
    type=str,
    help="Callback url on terminate",
    default="Unknown",
)
parser.add_argument(
    "--rpc_port", type=int, help="Remote procedure server port", default="Unknown"
)
parser.add_argument(
    "--auth_token",
    type=str,
    help="Authentication token for authentication",
    default="Unknown",
)
parser.add_argument(
    "--terminal_type",
    type=str,
    help="Terminal type",
    default="Unknown",
)

parser.add_argument(
    "--terminal_port",
    type=int,
    help="Terminal port",
    default="Unknown",
)

# Parse arguments
args = parser.parse_args()

print(f"Stating Server with Args: {args}")

ACCOUNT_NUMBER = args.login
PASSWORD = args.password
SERVER = args.server

# port for this terminal
symbol = "EURUSD"
port = args.server_port
env = os.environ.get("ENVIRONMENT")

if env == "local":
    terminal_path = rf"C:\Program Files\MetaTrader 5\terminals\{port}\terminal64.exe"
else:
    terminal_path = rf"/config/.wine/drive_c/Program Files/MetaTrader 5/terminals/{port}/terminal64.exe"

stop_event = None

rpc_server = None
http_server = None

retries = 0
loop = None
mt4_client: mt4 = None

# Add a global variable to track connection status
mt4_connection_healthy = True

# Add a decorator to handle connection errors
def with_connection_check(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        global mt4_connection_healthy
        try:
            result = await func(*args, **kwargs)
            # Reset failed attempts counter on successful operation
            if hasattr(mt4_client, 'consecutive_failures'):
                mt4_client.consecutive_failures = 0
            return result
        except Exception as e:
            if "Not connected to MT4 server" in str(e) or "Connection refused" in str(e):
                # Increment failed attempts
                if not hasattr(mt4_client, 'consecutive_failures'):
                    mt4_client.consecutive_failures = 1
                else:
                    mt4_client.consecutive_failures += 1
                
                print(f"Connection failure #{mt4_client.consecutive_failures}: {e}")
                
                # If we've had too many consecutive failures, mark connection as unhealthy
                if mt4_client.consecutive_failures >= 3:
                    print("Too many consecutive connection failures, marking connection as unhealthy")
                    mt4_connection_healthy = False
                    # Trigger shutdown due to persistent connection failure
                    asyncio.create_task(send_shutdown_signal("connection_lost"))
            raise e
    return wrapper

# Add a connection health check function
async def check_connection_health():
    global mt4_connection_healthy
    global mt4_client
    
    while not stop_event.is_set():
        if not mt4_connection_healthy:
            print("Connection health check failed, shutting down")
            stop_event.set()
            break
            
        # If we have an MT4 client, check its connection
        if mt4_client and args.terminal_type == "mt4":
            try:
                # Try a simple operation to test connection
                await mt4_client.ensure_connected()
                if not mt4_client.is_connected:
                    print("Connection check failed - not connected")
                    if not hasattr(mt4_client, 'health_check_failures'):
                        mt4_client.health_check_failures = 1
                    else:
                        mt4_client.health_check_failures += 1
                        
                    if mt4_client.health_check_failures >= 3:
                        print("Too many health check failures, marking connection as unhealthy")
                        mt4_connection_healthy = False
                        asyncio.create_task(send_shutdown_signal("connection_lost"))
                else:
                    # Reset health check failures on successful connection
                    mt4_client.health_check_failures = 0
            except Exception as e:
                print(f"Health check error: {e}")
                if not hasattr(mt4_client, 'health_check_failures'):
                    mt4_client.health_check_failures = 1
                else:
                    mt4_client.health_check_failures += 1
                    
                if mt4_client.health_check_failures >= 3:
                    print("Too many health check failures, marking connection as unhealthy")
                    mt4_connection_healthy = False
                    asyncio.create_task(send_shutdown_signal("connection_lost"))
        
        # Wait before next check
        await asyncio.sleep(30)  # Check every 30 seconds

# Initialize MetaTrader 5
def initialize_mt5():
    if not mt5.initialize(
        login=ACCOUNT_NUMBER,
        password=PASSWORD,
        server=SERVER,
        path=terminal_path,
        portable=True,
    ):
        if not mt5.login(login=ACCOUNT_NUMBER, password=PASSWORD):
            print(f"MetaTrader 5 initialization failed, error: {mt5.last_error()}")
        return False
    print("MetaTrader 5 initialized")
    return True


async def initialize_mt4():
    global mt4_client
    print("Initializing mt4")
    
    state = False
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries and not state:
        try:
            mt4_client = await mt4(
                host="localhost", 
                port=args.terminal_port, 
                loop=loop,
                max_retries=3,  # Configure connection retries
                retry_delay=5.0  # 5 seconds between retries
            ).connect()
            
            await asyncio.sleep(5)
            accountInfo = await mt4_client.get_account_info()
            
            if accountInfo is None:
                raise Exception("Account info is None")
            
            print(f"MT4 client connected successfully: {mt4_client}")
            state = True
            
        except Exception as e:
            retry_count += 1
            print(f"MT4 connection attempt {retry_count} failed: {e}")
            if retry_count < max_retries:
                wait_time = 10 * retry_count
                print(f"Retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
            else:
                print(f"Failed to connect after {max_retries} attempts")
    
    return state


async def handle_shutdown(_):
    global stop_event
    global http_server

    stop_event.set()
    print("Shutting down")


async def handle_tests(_):
    global mt4_client
    c = await mt4_client.get_account_info()
    print(c)


async def send_shutdown_signal(type=None):
    print(f"Shutting down, reason: {type}")
    # Send webhook if connection is closed
    data = {
        "terminal_id": args.terminal_id,
        "type": type,
        "message": f"Terminal shutdown: {type}"
    }
    try:
        response = requests.post(args.terminate_callback, data=data)

        if response.status_code == 200:
            print("Termination webhook sent successfully")
        else:
            print(f"POST request failed with status code: {response.status_code}")
    except Exception as e:
        print(f"Failed to send termination webhook: {e}")


# Main script
async def main():
    global mt4
    global loop
    global mt4_event
    global stop_event
    global rpc_server
    global http_server
    global mt4_connection_healthy

    stop_event = asyncio.Event()
    mt4_event = asyncio.Event()
    loop = asyncio.get_running_loop()

    app = web.Application()
    app.router.add_get("/shutdown", handle_shutdown)
    app.router.add_get("/tests", handle_tests)

    runner = web.AppRunner(app)
    await runner.setup()

    http_server = web.TCPSite(runner, "localhost", port=port)
    await http_server.start()

    if args.terminal_type == "mt4":
        status = await initialize_mt4()
        if not status:
            await send_shutdown_signal("login")
            return
        # Start connection health check
        health_check_task = asyncio.create_task(check_connection_health())
    elif args.terminal_type == "mt5":
        if not initialize_mt5():
            mt5.shutdown()
            await send_shutdown_signal("login")
            return
    else:
        raise ValueError(f"Unknown terminal type: {args.terminal_type}")

    print("Rpc server running on port", args.rpc_port)
    rpc_server = await rpc_service.serve(
        args.rpc_port, args.auth_token, args.terminal_type, mt5, mt4Instance=mt4_client
    )

    # Make the POST request
    data = {
        "status": "success",
        "ws_port": f"{port}",
        "message": "Connected",
        "login": f"{ACCOUNT_NUMBER}",
        "rpc_port": f"{args.rpc_port}",
        "terminal_id": args.terminal_id,
    }

    response = requests.post(args.login_callback, data=data)

    if response.status_code == 200:
        print("Login webhook sent successfully")
    else:
        print(f"POST request failed with status code: {response.status_code}")

    await stop_event.wait()
    
    # Cancel health check task if it's running
    if args.terminal_type == "mt4" and 'health_check_task' in locals():
        health_check_task.cancel()
        
    await rpc_server.stop(5)
    await http_server.stop()

    print("Server stop")


if __name__ == "__main__":
    asyncio.run(main())
