name: Build and Publish Docker Image

on:
  release:
    types: [published]

jobs:
  build_and_push:
    runs-on: ubuntu-latest
    steps:
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      # - name: Cache Docker layers
      #   uses: actions/cache@v3
      #   with:
      #     path: /tmp/.buildx-cache
      #     key: ${{ runner.os }}-buildx-${{ github.sha }}
      #     restore-keys: |
      #       ${{ runner.os }}-buildx-

      - name: Set the version
        id: get_version
        run: echo "VERSION=$(echo ${{ github.ref }} | cut -d '/' -f 3)" >> $GITHUB_ENV

      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v5
        with:
          # context: .
          # file: ./Dockerfile
          platforms: linux/amd64
          push: true
          tags:  gmag11/metatrader5_vnc:${{ env.VERSION }},gmag11/metatrader5_vnc:latest
          # cache-from: type=local,src=/tmp/.buildx-cache
          # cache-to: type=local,dest=/tmp/.buildx-cache

      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}
